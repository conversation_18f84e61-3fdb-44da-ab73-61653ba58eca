<template>
  <div ref="chartContainer" style="width: 100%; height: 100%" />
</template>

<script setup lang="ts">
import { onMounted, ref, nextTick } from "vue";
import * as echarts from "echarts";

const chartContainer = ref<HTMLDivElement | null>(null);
let chart = null;
const initChart = () => {
  if (chartContainer.value) {
    chart = echarts.init(chartContainer.value);
    const option = {
      title: {
        text: "",
      },
      tooltip: {
        trigger: "axis",
      },
      legend: {
        data: ["平均工期", "计划工期"],
      },
      grid: {
        left: "5%",
        right: "5%",
        bottom: "3%",
        containLabel: true,
      },
      xAxis: {
        type: "category",
        boundaryGap: false,
        data: ["秦山", "福清", "田湾", "海南", "三门"],
      },
      yAxis: {
        type: "value",
        name: "工期(天)",
      },
      series: [
        {
          name: "平均工期",
          type: "bar",
          barWidth: 20,
          data: [120, 200, 150, 80, 70],
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: "#188df0" }, // 顶部颜色
              { offset: 1, color: "rgba(24, 141, 240, 0.2)" },
            ]),
          },
        },
        {
          name: "计划工期",
          type: "line",
          data: [90, 182, 191, 234, 290],
        },
      ],
    };
    chart.setOption(option);
  }
};

onMounted(async () => {
  await nextTick();
  setTimeout(() => {
    initChart();
  });
});
</script>

<style scoped>
/* 你可以在这里添加一些样式来美化你的图表容器 */
</style>
