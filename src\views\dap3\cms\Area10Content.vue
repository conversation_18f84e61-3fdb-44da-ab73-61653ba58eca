<template>
  <div class="area10-container">
    <div class="area10-main-title">
      <span class="title-text">变更</span>
      <span class="completion-rate">总数：100</span>
    </div>
    <div class="content-area">
      <div class="chart-container">
        <div id="barChartArea10" class="bar-chart"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import * as echarts from 'echarts/core';

const chartInstance = ref(null);

// 四个分类及其数据
const categories = ['设计完成', '施工方完成', '交底完成', '开工确认'];
const barData = [90, 70, 55, 40];  // 这些数值可以根据实际情况调整

// 渐变色配置
const gradientColors = [
  {
    name: '设计完成',
    color: {
      type: 'linear',
      x: 0, y: 0, x2: 0, y2: 1,
      colorStops: [
        { offset: 0, color: '#4a9af6' },      // 顶部颜色
        { offset: 1, color: 'rgba(74, 154, 246, 0.5)' }  // 底部颜色（透明度降低）
      ]
    }
  },
  {
    name: '施工方完成',
    color: {
      type: 'linear',
      x: 0, y: 0, x2: 0, y2: 1,
      colorStops: [
        { offset: 0, color: '#7ec8a3' },      // 顶部颜色
        { offset: 1, color: 'rgba(126, 200, 163, 0.5)' }  // 底部颜色（透明度降低）
      ]
    }
  },
  {
    name: '交底完成',
    color: {
      type: 'linear',
      x: 0, y: 0, x2: 0, y2: 1,
      colorStops: [
        { offset: 0, color: '#e8bb38' },      // 顶部颜色
        { offset: 1, color: 'rgba(232, 187, 56, 0.5)' }  // 底部颜色（透明度降低）
      ]
    }
  },
  {
    name: '开工确认',
    color: {
      type: 'linear',
      x: 0, y: 0, x2: 0, y2: 1,
      colorStops: [
        { offset: 0, color: '#e7945e' },      // 顶部颜色
        { offset: 1, color: 'rgba(231, 148, 94, 0.5)' }  // 底部颜色（透明度降低）
      ]
    }
  }
];

const initChart = () => {
  const chartDom = document.getElementById('barChartArea10');
  if (!chartDom) return;
  chartInstance.value = echarts.init(chartDom);

  const option = {
    backgroundColor: 'transparent',
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '8%',
      containLabel: true
    },

    xAxis: {
      type: 'category',
      data: categories,
      axisLine: { lineStyle: { color: '#6e7079' } },
      axisLabel: { color: '#ffffff' }
    },
    yAxis: {
      type: 'value',

      interval: 20,
      axisLabel: {
        formatter: '{value}',
        color: '#ffffff'
      },
      splitLine: { lineStyle: { type: 'dashed', color: '#3a3f5a' } },
      axisLine: { show: false },
    },
    series: [
      {
        type: 'bar',
        barWidth: '25%',
        data: barData.map((value, index) => ({
          value: value,
          itemStyle: {
            color: gradientColors[index].color,
            borderRadius: [5, 5, 0, 0]  // 左上角、右上角、右下角、左下角
          }
        })),
        label: {
          show: true,
          position: 'top',
          color: '#ffffff',
          formatter: '{c}'
        }
      }
    ]
  };
  
  chartInstance.value.setOption(option);
};

const handleResize = () => {
  if (chartInstance.value) {
    chartInstance.value.resize();
  }
};

onMounted(() => {
  initChart();
  window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
  if (chartInstance.value) {
    chartInstance.value.dispose();
  }
  window.removeEventListener('resize', handleResize);
});

</script>

<style scoped>
.area10-container {
  background-color: #192347;
  color: #fff;
  height: 100%;
  width: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  border-radius: 8px;
  overflow: hidden;
}

.area10-main-title {
  height: 30px;
  background-color: #61aef9;
  color: #ffffff;
  font-size: 16px;
  font-weight: 500;
  display: flex;
  align-items: center;
  padding-left: 15px;
  padding-right: 15px;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  flex-shrink: 0;
  position: relative;
  justify-content: space-between;
}

.area10-main-title::before {
  content: '';
  position: absolute;
  left: 0px;
  top: 5px;
  bottom: 6px;
  width: 4px;
  border-radius: 3px;
  background: linear-gradient(
    to bottom,
    rgba(255, 255, 255, 0.9),
    rgba(255, 255, 255, 0.3)
  );
}

.completion-rate {
  font-size: 14px;
  font-weight: normal;
}

.content-area {
  flex: 1;
  box-sizing: border-box;
  display: flex;
  align-items: stretch;
}

.chart-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.bar-chart {
  width: 100%;
  height: 90%;
  position: relative;
}
</style> 