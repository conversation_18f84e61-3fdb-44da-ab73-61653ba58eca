<template>
  <a-modal
    :open="formState.openModalType"
    :title="null"
    width="1000px"
    @cancel="handleCancel"
    :footer="null"
  >
    <div class="preview-content">
      <!-- 标题部分 -->
      <div class="modal-header">
        <div class="main-title">
          机组大修调整信息申请审批【编号：2025-0001】
        </div>
        <div class="sub-title">
          （大修时长：05（减少）-02（减至）-01411（大修编号））
        </div>
      </div>

      <!-- 整个表单 -->
      <div class="form-container">
        <a-form :model="formState" layout="vertical" class="form-section">
          <!-- 基本信息 -->
          <a-row :gutter="16" class="form-row">
            <a-col :span="8">
              <a-form-item label="基地：">
                <a-select
                  v-model:value="formState.base"
                  placeholder="请选择基地"
                >
                  <a-select-option value="taishan">泰山基地</a-select-option>
                  <a-select-option value="huanghe">黄河基地</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="大修编号：">
                <a-input
                  v-model:value="formState.repairNo"
                  placeholder="请输入大修编号"
                />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="大修类型：">
                <a-select
                  v-model:value="formState.repairType"
                  placeholder="请选择大修类型"
                >
                  <a-select-option value="1">A修</a-select-option>
                  <a-select-option value="2">B修</a-select-option>
                  <a-select-option value="3">C修</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>

          <!-- 调整类型 -->
          <a-row :gutter="16" class="form-row">
            <a-col :span="24">
              <a-form-item>
                <div class="adjust-type-container">
                  <span class="adjust-type-label">调整类型：</span>
                  <a-radio-group v-model:value="formState.adjustType">
                    <a-radio value="1">大修开始时间调整</a-radio>
                    <a-radio value="2">大修计划工期调整</a-radio>
                  </a-radio-group>
                </div>
              </a-form-item>
            </a-col>
          </a-row>

          <!-- 调整前 -->
          <div class="time-section">
            <div class="section-header">
              <div class="section-title">调整前</div>
            </div>
            <div class="divider"></div>
            <a-row :gutter="16" class="form-row">
              <a-col :span="12">
                <a-form-item label="大修开始时间：">
                  <a-date-picker
                    v-model:value="formState.beforeStartTime"
                    style="width: 100%"
                    :locale="locale"
                    format="YYYY-MM-DD"
                    placeholder="请选择开始时间"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="大修完成时间：">
                  <a-date-picker
                    v-model:value="formState.beforeEndTime"
                    style="width: 100%"
                    :locale="locale"
                    format="YYYY-MM-DD"
                    placeholder="请选择完成时间"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="16" class="form-row">
              <a-col :span="12">
                <a-form-item label="大修计划工期：">
                  <a-date-picker
                    v-model:value="formState.beforePlanDuration"
                    style="width: 100%"
                    :locale="locale"
                    format="YYYY-MM-DD"
                    placeholder="请选择日期"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="大修目标工期：">
                  <a-date-picker
                    v-model:value="formState.beforeTargetDuration"
                    style="width: 100%"
                    :locale="locale"
                    format="YYYY-MM-DD"
                    placeholder="请选择日期"
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </div>

          <!-- 调整后 -->
          <div class="time-section">
            <div class="section-header">
              <div class="section-title">调整后</div>
            </div>
            <div class="divider"></div>
            <a-row :gutter="16" class="form-row">
              <a-col :span="12">
                <a-form-item label="大修开始时间：">
                  <a-date-picker
                    v-model:value="formState.afterStartTime"
                    style="width: 100%"
                    :locale="locale"
                    format="YYYY-MM-DD"
                    placeholder="请选择开始时间"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="大修完成时间：">
                  <a-date-picker
                    v-model:value="formState.afterEndTime"
                    style="width: 100%"
                    :locale="locale"
                    format="YYYY-MM-DD"
                    placeholder="请选择完成时间"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row :gutter="16" class="form-row">
              <a-col :span="12">
                <a-form-item label="大修计划工期：">
                  <a-date-picker
                    v-model:value="formState.afterPlanDuration"
                    style="width: 100%"
                    :locale="locale"
                    format="YYYY-MM-DD"
                    placeholder="请选择计划工期"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="大修目标工期：">
                  <a-date-picker
                    v-model:value="formState.afterTargetDuration"
                    style="width: 100%"
                    :locale="locale"
                    format="YYYY-MM-DD"
                    placeholder="请选择目标工期"
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </div>

          <!-- 调整原因 -->
          <div class="time-section">
            <div class="section-header">
              <div class="section-title">调整原因</div>
            </div>
            <div class="divider"></div>
            <a-row :gutter="16" class="form-row">
              <a-col :span="12">
                <a-form-item label="原因：">
                  <a-textarea
                    v-model:value="formState.reason"
                    :auto-size="{ minRows: 4, maxRows: 4 }"
                    placeholder="请输入调整原因"
                    :resize="false"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="支撑材料：">
                  <div class="support-material"></div>
                </a-form-item>
              </a-col>
            </a-row>
          </div>

          <!-- 申请信息 -->
          <div class="time-section">
            <div class="divider"></div>
            <a-row :gutter="16" class="form-row">
              <a-col :span="12">
                <a-form-item label="申请人：">
                  <a-input
                    v-model:value="formState.applicant"
                    placeholder="请输入申请人姓名"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="申请日期：">
                  <a-date-picker
                    v-model:value="formState.applyDate"
                    style="width: 100%"
                    :locale="locale"
                    format="YYYY-MM-DD"
                    placeholder="请选择申请日期"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <div class="divider"></div>

            <!-- 工程师意见 -->
            <a-row class="form-row engineer-opinion">
              <span class="engineer-label">大修规划工程师意见：</span>
              <a-textarea
                v-model:value="formState.engineerOpinion"
                :auto-size="{ minRows: 4, maxRows: 4 }"
                placeholder="请输入工程师意见"
                :resize="false"
                class="engineer-textarea"
              />
            </a-row>
          </div>
        </a-form>
      </div>

      <!-- 底部按钮 -->
      <div class="modal-footer">
        <a-space>
          <a-button type="primary" @click="handleAgree">同意</a-button>
          <a-button @click="handleCancel">取消</a-button>
        </a-space>
      </div>
    </div>
  </a-modal>
</template>

<script setup>
import { reactive, defineExpose } from "vue";
import dayjs from "dayjs";
import "dayjs/locale/zh-cn";
import locale from "ant-design-vue/es/date-picker/locale/zh_CN";

dayjs.locale("zh-cn");

// 表单数据
const formState = reactive({
  base: "taishan",
  repairNo: "411",
  repairType: "1",
  adjustType: "1",
  beforeStartTime: null,
  beforeEndTime: null,
  beforePlanDuration: null,
  beforeTargetDuration: null,
  afterStartTime: null,
  afterEndTime: null,
  afterPlanDuration: null,
  afterTargetDuration: null,
  reason: "",
  openModalType: false,
  applicant: "", // 申请人
  applyDate: null, // 申请日期
  engineerOpinion: "", // 工程师意见
});

const handleAgree = () => {
  // 处理同意逻辑
  console.log("同意");
  formState.openModalType = false;
};

const handleCancel = () => {
  formState.openModalType = false;
};

const openModal = () => {
  formState.openModalType = true;
};

defineExpose({
  openModal,
});
</script>

<style scoped>
.preview-content {
  display: flex;
  flex-direction: column;
  height: 80vh; /* 设置模态框高度 */
}

.modal-header {
  flex-shrink: 0; /* 防止头部压缩 */
  text-align: center;
  padding: 0 0 8px 0;
  border-bottom: 1px solid #e8e8e8;
  margin-bottom: 8px;
}

.main-title {
  font-size: 16px;
  font-weight: bold;
  color: #000;
  margin-bottom: 8px;
}

.sub-title {
  font-size: 14px;
  color: #666;
}

.form-container {
  flex: 1;
  overflow-y: auto;
  padding: 0 16px;
  margin: 8px -16px;
}

.modal-footer {
  flex-shrink: 0; /* 防止底部压缩 */
  padding-top: 16px;
  border-top: 1px solid #e8e8e8;
  text-align: center;
}

/* 自定义滚动条样式 */
.form-container::-webkit-scrollbar {
  width: 6px;
}

.form-container::-webkit-scrollbar-thumb {
  background-color: #ccc;
  border-radius: 3px;
}

.form-container::-webkit-scrollbar-track {
  background-color: #f1f1f1;
}

.form-section {
  margin-bottom: 8px; /* 调整表单区域间距 */
}

.time-section {
  margin-bottom: 4px; /* 调整区块间距 */
}

.section-header {
  margin-bottom: 0; /* 移除标题底部间距 */
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #000;
  padding-left: 8px;
  border-left: 4px solid transparent; /* 移除原有边框 */
  position: relative; /* 用于伪元素定位 */
}

/* 左侧色块渐变 */
.section-title::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 4px;
  height: 90%;
  background: linear-gradient(180deg, rgba(44, 152, 253, 0.3), #1890ff);
}

.divider {
  height: 1px;
  background-color: #e8e8e8;
  margin: 4px 0; /* 保持 4px 的间距 */
}

.form-row {
  margin-bottom: 4px !important; /* 调整行间距 */
}

:deep(.ant-form-item) {
  margin-bottom: 4px !important; /* 调整表单项间距 */
}

.attachment-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.attachment-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.attachment-date {
  color: #999;
  margin-left: 8px;
}

:deep(.ant-input) {
  resize: none !important;
}

.support-material {
  height: 95px; /* 与文本框等高 */
  background-color: #f5f5f5;
  border: 1px solid #d9d9d9;
  border-radius: 2px;
}

/* 确保文本域不能调整大小 */
:deep(.ant-input-textarea-show-count textarea) {
  resize: none !important;
}

:deep(.ant-form-item-label) {
  padding-bottom: 8px; /* 调整 label 和输入框之间的间距 */
  text-align: left; /* label 左对齐 */
}

:deep(.ant-form-vertical .ant-form-item-label) {
  padding: 0 0 8px; /* 移除默认的内边距 */
}

.adjust-type-container {
  display: flex;
  align-items: center;
}

.adjust-type-label {
  font-size: 14px;
  margin-right: 16px;
  color: rgba(0, 0, 0, 0.85);
  white-space: nowrap;
}

:deep(.ant-radio-group) {
  display: flex;
  gap: 32px;
}

.engineer-opinion {
  margin-top: 4px; /* 调整工程师意见上方间距 */
}

.engineer-label {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
  white-space: nowrap;
  margin-right: 8px;
  line-height: 32px;
}

.engineer-textarea {
  flex: 1;
}

:deep(.ant-input) {
  resize: none !important;
}
</style>
