<template>
  <div class="settings-container">
    <a-card title="时间重叠检测" :bordered="false">
      <a-form layout="inline">
        <a-form-item label="开始时间">
          <a-date-picker
            v-model:value="checkTimeRange.startTime"
            :format="dateFormat"
            @change="checkOverlapping"
            :locale="zhCN"
            placeholder="请选择开始时间"
          />
        </a-form-item>
        <a-form-item label="结束时间">
          <a-date-picker
            v-model:value="checkTimeRange.endTime"
            :format="dateFormat"
            @change="checkOverlapping"
            :locale="zhCN"
            placeholder="请选择结束时间"
          />
        </a-form-item>
      </a-form>
      <div
        class="check-result"
        v-if="checkTimeRange.startTime && checkTimeRange.endTime"
      >
        <a-alert
          :type="hasOverlap ? 'error' : 'success'"
          :message="
            hasOverlap
              ? '该时间段与现有维修计划有重叠！'
              : '该时间段无重叠维修计划'
          "
          banner
        />
      </div>
    </a-card>

    <!-- 添加现有维修计划表格 -->
    <a-card title="现有维修计划" :bordered="false">
      <a-table
        :columns="columns"
        :data-source="formattedMaintenancePlans"
        :pagination="false"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'duration'">
            <span
              :style="{
                color: isOverlappingWithSelected(record)
                  ? '#ff4d4f'
                  : 'inherit',
              }"
            >
              {{ record.duration }}
            </span>
          </template>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed } from "vue";
import type { Dayjs } from "dayjs";
import zhCN from "ant-design-vue/es/date-picker/locale/zh_CN";

const dateFormat = "YYYY-MM-DD";

// 时间重叠检测
const checkTimeRange = ref({
  startTime: null as Dayjs | null,
  endTime: null as Dayjs | null,
});

const hasOverlap = ref(false);

// 维修计划数据（这里需要从你的数据源获取）
const maintenancePlans = ref([
  {
    key: 1,
    unit: 1,
    year2024: {
      startTime: "2024-07-29",
      endTime: "2025-09-14",
      text: "维修计划1-2024",
    },
    year2025: {
      startTime: "2025-12-09",
      endTime: "2025-12-18",
      text: "维修计划1-2025",
    },
    year2027: {
      startTime: "2027-01-16",
      endTime: "2027-08-12",
      text: "维修计划1-2027",
    },
    year2028: {
      startTime: "2028-10-30",
      endTime: "2028-11-30",
      text: "维修计划1-2028",
    },
  },
  {
    key: 2,
    unit: 2,
    year2024: {
      startTime: "2024-08-19",
      endTime: "2024-09-19",
      text: "维修计划2-2024",
      rowIndex: 1,
    },
    year2025: {
      startTime: "2025-12-03",
      endTime: "2025-12-26",
      text: "维修计划2-2025",
      rowIndex: 1,
    },
    year2027: {
      startTime: "2027-06-26",
      endTime: "2027-07-27",
      text: "维修计划2-2027",
      rowIndex: 1,
    },
    year2028: {
      startTime: "2028-05-20",
      endTime: "2028-10-30",
      text: "维修计划2-2028",
      rowIndex: 1,
    },
    year2029: {
      startTime: "2029-04-02",
      endTime: "2029-04-12",
      text: "维修计划2-2029",
      rowIndex: 1,
    },
  },
  {
    key: 3,
    unit: 3,
    year2024: {
      startTime: "2024-03-15",
      endTime: "2025-11-10",
      text: "维修计划3-2024",
      rowIndex: 2,
    },
    year2026: {
      startTime: "2026-10-10",
      endTime: "2026-11-05",
      text: "维修计划3-2026",
      rowIndex: 2,
    },
    year2028: {
      startTime: "2028-09-30",
      endTime: "2028-11-24",
      text: "维修计划3-2028",
      rowIndex: 2,
    },
    year2029: {
      startTime: "2029-07-11",
      endTime: "2029-11-15",
      text: "维修计划3-2029",
      rowIndex: 2,
    },
  },
  {
    key: 4,
    unit: 4,
    year2024: {
      startTime: "2024-12-13",
      endTime: "2024-12-15",
      text: "维修计划4-2024",
      rowIndex: 3,
    },
    year2025: {
      startTime: "2025-03-15",
      endTime: "2025-08-20",
      text: "维修计划4-2025",
      rowIndex: 3,
    },
    year2027: {
      startTime: "2027-11-26",
      endTime: "2027-11-28",
      text: "维修计划4-2027",
      rowIndex: 3,
    },
  },
  {
    key: 5,
    unit: 5,
    year2027: {
      startTime: "2027-08-29",
      endTime: "2028-02-07",
      text: "维修计划5-2027",
      rowIndex: 4,
    },
    year2028: {
      startTime: "2028-08-07",
      endTime: "2028-11-29",
      text: "维修计划5-2028",
      rowIndex: 4,
    },
    year2029: {
      startTime: "2029-07-29",
      endTime: "2029-10-07",
      text: "维修计划5-2029",
      rowIndex: 4,
    },
  },
  {
    key: 6,
    unit: 6,
    year2027: {
      startTime: "2027-08-29",
      endTime: "2028-02-07",
      text: "维修计划5-2027",
      rowIndex: 5,
    },
    year2028: {
      startTime: "2028-08-07",
      endTime: "2028-11-29",
      text: "维修计划5-2028",
      rowIndex: 5,
    },
    year2029: {
      startTime: "2029-07-29",
      endTime: "2029-10-07",
      text: "维修计划5-2029",
      rowIndex: 5,
    },
  },
]);
/**
 * 检查两个时间段是否重叠
 * @param startTime1 第一个时间段的开始时间
 * @param endTime1 第一个时间段的结束时间
 * @param startTime2 第二个时间段的开始时间
 * @param endTime2 第二个时间段的结束时间
 * @returns boolean 是否重叠
 */
const isTimeOverlapping = (
  startTime1: Date | string,
  endTime1: Date | string,
  startTime2: Date | string,
  endTime2: Date | string
): boolean => {
  const start1 = new Date(startTime1);
  const end1 = new Date(endTime1);
  const start2 = new Date(startTime2);
  const end2 = new Date(endTime2);

  return start1 <= end2 && end1 >= start2;
};

// 检查输入的时间范围是否与现有计划重叠
const checkOverlapping = () => {
  if (!checkTimeRange.value.startTime || !checkTimeRange.value.endTime) return;

  const inputStart = checkTimeRange.value.startTime.format(dateFormat);
  const inputEnd = checkTimeRange.value.endTime.format(dateFormat);

  // 遍历所有维修计划检查重叠
  hasOverlap.value = maintenancePlans.value.some((item) => {
    return Object.keys(item)
      .filter((key) => key.startsWith("year"))
      .some((yearKey) => {
        if (!item[yearKey]) return false;
        return isTimeOverlapping(
          inputStart,
          inputEnd,
          item[yearKey].startTime,
          item[yearKey].endTime
        );
      });
  });
};

// 检查表格中的记录是否与选中时间重叠
const isOverlappingWithSelected = (record: any): boolean => {
  if (!checkTimeRange.value.startTime || !checkTimeRange.value.endTime)
    return false;

  return isTimeOverlapping(
    checkTimeRange.value.startTime.format(dateFormat),
    checkTimeRange.value.endTime.format(dateFormat),
    record.startTime,
    record.endTime
  );
};

// 表格列定义
const columns = [
  {
    title: "机组",
    dataIndex: "unit",
    key: "unit",
  },
  {
    title: "维修计划",
    dataIndex: "text",
    key: "text",
  },
  {
    title: "维修时间",
    dataIndex: "duration",
    key: "duration",
  },
];

// 格式化维修计划数据用于表格显示
const formattedMaintenancePlans = computed(() => {
  return maintenancePlans.value.flatMap((plan) => {
    return Object.keys(plan)
      .filter((key) => key.startsWith("year"))
      .map((yearKey) => {
        const yearPlan = plan[yearKey];
        return {
          key: `${plan.key}-${yearKey}`,
          unit: `机组${plan.unit}`,
          text: yearPlan.text,
          duration: `${yearPlan.startTime} 至 ${yearPlan.endTime}`,
          startTime: yearPlan.startTime,
          endTime: yearPlan.endTime,
        };
      });
  });
});

function checkTimeOverlap(newPlan, maintenancePlans) {
  const newStartTime = new Date(newPlan.startTime);
  const newEndTime = new Date(newPlan.endTime);

  // 用于存储重叠的计划
  const overlappingPlans = [];

  // 遍历所有维修计划
  maintenancePlans.value.forEach((plan) => {
    // 遍历每个计划的所有年份
    Object.keys(plan).forEach((key) => {
      // 只检查包含年份的属性（year开头的属性）
      if (key.startsWith("year")) {
        const yearPlan = plan[key];
        const planStartTime = new Date(yearPlan.startTime);
        const planEndTime = new Date(yearPlan.endTime);

        // 检查时间是否重叠
        if (newStartTime <= planEndTime && newEndTime >= planStartTime) {
          overlappingPlans.push({
            unit: plan.unit,
            year: key,
            text: yearPlan.text,
            startTime: yearPlan.startTime,
            endTime: yearPlan.endTime,
          });
        }
      }
    });
  });

  return 
    hasOverlap: overlappingPlans.length >= 2,
 
  
}

// 计算甘特图样式的方法
const getGanttStyle = (startTime, endTime) => {
  // 判断是否经过春节
  const isSpringFestival = isSpringFestivalPeriod(startTime, endTime);
  
  const baseStyle = {
    position: 'absolute',
    height: '30px',
    borderRadius: '4px',
    zIndex: 1,
  };

  // 如果经过春节，保持原有颜色，否则变成灰色
  const colorStyle = isSpringFestival 
    ? {
        backgroundColor: '#e6f7ff',
        border: '1px solid #91d5ff',
      }
    : {
        backgroundColor: '#f5f5f5',
        border: '1px solid #d9d9d9',
      };

  return {
    ...baseStyle,
    ...colorStyle,
  };
};

// 判断是否经过春节的方法
const isSpringFestivalPeriod = (startTime, endTime) => {
  const start = new Date(startTime);
  const end = new Date(endTime);
  
  // 获取计划时间范围内的所有年份
  const years = [];
  for (let year = start.getFullYear(); year <= end.getFullYear(); year++) {
    years.push(year);
  }
  
  // 检查是否有任一年的春节在计划范围内
  return years.some(year => {
    const festivalStart = new Date(year, 0, 20); // 1月20日
    const festivalEnd = new Date(year, 1, 20);   // 2月20日
    
    return (start <= festivalEnd && end >= festivalStart);
  });
};
</script>

<style scoped>
.settings-container {
  padding: 24px;
}

.check-result {
  margin-top: 16px;
}

.ant-card {
  margin-bottom: 24px;
}

.ant-card-head-title {
  font-weight: 500;
}

:deep(.ant-table-thead > tr > th) {
  background-color: #fafafa;
}

:deep(.ant-table-tbody > tr > td) {
  padding: 12px 16px;
}
</style>
