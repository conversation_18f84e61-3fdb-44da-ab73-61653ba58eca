<template>
  <div class="annual-duration-chart">
    <div class="chart-title">
      <span class="title-text">年度工期统计</span>
      <div class="title-right">
        <div class="select-box">
          <select
            v-model="selectedType"
            @change="SelectChange"
            class="type-select"
          >
            <option value="A">A</option>
            <option value="B">B</option>
            <option value="C">C</option>
          </select>
        </div>
      </div>
    </div>
    <div ref="chartRef" class="chart-container"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from "vue";
import * as echarts from "echarts/core";
import { BarChart, LineChart } from "echarts/charts";
import {
  GridComponent,
  TooltipComponent,
  LegendComponent,
  MarkPointComponent,
} from "echarts/components";
import { Canvas<PERSON>enderer } from "echarts/renderers";

// 注册必要的组件
echarts.use([
  <PERSON><PERSON><PERSON>,
  LineChart,
  GridComponent,
  Tooltip<PERSON>omponent,
  LegendComponent,
  MarkPointComponent,
  CanvasRenderer,
]);

const chartRef = ref<HTMLElement | null>(null);
let chart: echarts.ECharts | null = null;
const selectedType = ref("A");

// 年度工期数据 - 根据类型分组
const dataByType = {
  A: [
    { year: "2020", repairCount: 18, avgDuration: 32 },
    { year: "2021", repairCount: 25, avgDuration: 28 },
    { year: "2022", repairCount: 40, avgDuration: 33 },
    { year: "2023", repairCount: 21, avgDuration: 29 },
    { year: "2024", repairCount: 28, avgDuration: 32 },
  ],
  B: [
    { year: "2020", repairCount: 16, avgDuration: 30 },
    { year: "2021", repairCount: 22, avgDuration: 26 },
    { year: "2022", repairCount: 35, avgDuration: 31 },
    { year: "2023", repairCount: 19, avgDuration: 27 },
    { year: "2024", repairCount: 24, avgDuration: 30 },
  ],
  C: [
    { year: "2020", repairCount: 20, avgDuration: 35 },
    { year: "2021", repairCount: 28, avgDuration: 32 },
    { year: "2022", repairCount: 40, avgDuration: 36 },
    { year: "2023", repairCount: 23, avgDuration: 31 },
    { year: "2024", repairCount: 30, avgDuration: 34 },
  ],
};

const SelectChange = () => {
  console.log(selectedType.value);
};

const updateChart = () => {
  if (!chart) return;

  const currentData = dataByType[selectedType.value];

  // 准备数据
  const years = currentData.map((item) => item.year);
  const repairCounts = currentData.map((item) => item.repairCount);
  const avgDurations = currentData.map((item) => item.avgDuration);

  // 配置项
  const option = {
    backgroundColor: "#030d3a", // 深蓝色背景
    legend: {
      data: ["大修次数", "年度平均工期"],
      right: "center",
      top: 0,
      textStyle: {
        color: "#fff",
        fontSize: 12,
      },
      itemWidth: 10,
      itemHeight: 10,
      icon: "circle", // 圆形图例
    },
    grid: {
      left: "3%",
      right: "5%",
      bottom: "15%",
      top: "15%",
      containLabel: true,
    },
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
      backgroundColor: "rgba(0, 20, 60, 0.8)",
      borderColor: "rgba(0, 180, 255, 0.3)",
      borderWidth: 1,
      textStyle: {
        color: "#fff",
      },
      formatter: function (params) {
        const year = params[0].name;
        const tooltipWidth = 150;

        // 项目配置映射 - 便于统一管理样式和减少重复代码
        const itemsConfig = {
          大修次数: {
            color: "#29a3ff",
            label: "大修次数:",
          },
          年度平均工期: {
            color: "#00e9a1",
            label: "年度平均工期:",
          },
        };

        // 头部年份信息
        let tooltipStr = `<div style="padding: 3px 6px; text-align: center; font-weight: bold; color: #fff; width: ${tooltipWidth}px;">${year}年</div>`;

        // 生成每个指标的HTML
        params.forEach((param) => {
          const config = itemsConfig[param.seriesName];

          // 只处理配置中定义的系列
          if (config) {
            tooltipStr += `
              <div style="display: flex; justify-content: space-between; align-items: center; margin: 5px 0;">
                <span style="color: ${config.color}; margin-right: 15px;">●</span>
                <span style="flex: 1; color: #ccc;">${config.label}</span>
                <span style="font-weight: bold; color: ${config.color};">${param.value}</span>
              </div>
            `;
          }
        });
        return tooltipStr;
      },
    },
    xAxis: {
      type: "category",
      data: years,
      axisLine: {
        lineStyle: {
          color: "#1d345d",
        },
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        color: "#fff",
        fontSize: 12,
      },
    },
    yAxis: {
      type: "value",
      max: 45,
      interval: 10,
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        lineStyle: {
          color: "#1d345d",
          type: "dashed",
        },
      },
      axisLabel: {
        color: "#fff",
        fontSize: 12,
      },
    },
    series: [
      {
        name: "大修次数",
        type: "pictorialBar",
        symbolSize: [20, 10],
        symbolOffset: [0, -7], // 上部椭圆
        symbolPosition: "end",
        z: 12,
        color: "#66ccfc",
        data: repairCounts,
      },
      {
        name: "",
        type: "pictorialBar",
        symbolSize: [20, 10],
        symbolOffset: [0, 5], // 下部椭圆
        z: 12,
        color: "#3b77a1",
        data: repairCounts,
      },
      {
        type: "bar",
        barWidth: 20,
        barGap: "10%",
        barCateGoryGap: "10%",
        itemStyle: {
          normal: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: "#0795f2",
              },
              {
                offset: 1,
                color: "#0B3147",
              },
            ]),
            opacity: 1,
          },
        },
        data: repairCounts,
      },
      {
        name: "年度平均工期",
        type: "line",
        smooth: true,
        symbol: "circle",
        symbolSize: 8,
        lineStyle: {
          width: 3,
          color: "#00e9a1",
        },
        itemStyle: {
          color: "#00e9a1",
          borderColor: "#fff",
          borderWidth: 2,
        },
        data: avgDurations,
        z: 2,
      },
    ],
  };

  // 应用配置项
  chart.setOption(option);
  chart.on("legendselectchanged", function (params) {
    if (params.name === "大修次数") {
      const isSelected = params?.selected["大修次数"];
      const newData = isSelected
        ? repairCounts
        : Array(repairCounts.length).fill(0);

      // 批量更新多个系列的数据
      [0, 1, 2].forEach((seriesIndex) => {
        option.series[seriesIndex].data = newData;
      });
      // 应用更新后的配置
      chart.setOption(option);
    }
  });
};

const initChart = () => {
  if (!chartRef.value) return;

  // 初始化图表
  chart = echarts.init(chartRef.value);

  // 更新图表数据
  updateChart();

  // 监听窗口大小变化，调整图表大小
  window.addEventListener("resize", handleResize);
};

const handleResize = () => {
  chart?.resize();
};

// 监听选择类型变化，更新图表
watch(selectedType, () => {
  updateChart();
});

onMounted(() => {
  initChart();
});

onUnmounted(() => {
  if (chart) {
    chart.dispose();
    chart = null;
  }
  window.removeEventListener("resize", handleResize);
});
</script>

<style scoped>
.annual-duration-chart {
  width: 100%;
  height: 100%;
  background-color: #030d3a;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  padding: 15px;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.5);
}

.chart-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 5px;
  border-bottom: 1px solid rgba(0, 255, 255, 0.2);
  margin-bottom: 10px;
}

.title-right {
  display: flex;
  align-items: center;
}

.title-text {
  color: #00afff;
  font-size: 16px;
  font-weight: bold;
  position: relative;
  padding-left: 15px;
}

.title-text::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 16px;
  background: linear-gradient(to bottom, #0066ff, #00ccff);
}

.select-box {
  position: relative;
}

.type-select {
  background-color: rgba(0, 60, 120, 0.5);
  border: 1px solid rgba(0, 180, 255, 0.5);
  color: #fff;
  padding: 3px 10px;
  font-size: 12px;
  border-radius: 4px;
  cursor: pointer;
  outline: none;
  appearance: none;
  -webkit-appearance: none;
  width: 60px;
  text-align: center;
}

.select-box::after {
  content: "";
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 4px solid #00afff;
  pointer-events: none;
}

.chart-container {
  flex: 1;
  width: 100%;
}
</style>
