<template>
  <div class="overview-container">
    <!-- 标题部分 (5%) -->
    <div class="section-header">
      <div class="header-line"></div>
      <h2 class="section-title">大修概览</h2>
    </div>

    <!-- 内容区域 (95%) -->
    <div class="content-area">
      <!-- 第一行内容 (平分高度) -->
      <div class="row">
        <div class="content-block">
          <div class="block-title">年度总工期</div>
          <div class="block-content">
            <div class="period-info">
              <div class="period-item">
                <div class="period-label">计划总工期:</div>
                <div class="period-value">
                  602<span class="period-unit">天</span>
                </div>
              </div>
              <div class="period-item">
                <div class="period-label">已执行工期:</div>
                <div class="period-value">
                  352.86<span class="period-unit">天</span>
                </div>
              </div>
              <div class="period-item">
                <div class="period-label">工期偏差:</div>
                <div class="period-value negative">
                  -12.62<span class="period-unit">天</span>
                </div>
              </div>
            </div>
            <div class="progress-bar">
              <div class="progress-fill" style="width: 58%"></div>
              <div class="progress-labels">
                <span class="progress-start">0</span>
                <span class="progress-end">602</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 第二行内容 (平分高度) - 两个色块 -->
      <div class="row">
        <div class="content-block half-block">
          <div class="block-title">年度平均工期</div>
          <div class="block-content">
            <div class="data-comparison">
              <div class="data-item">
                <div class="data-label">计划工期</div>
                <div class="data-value">
                  34.50<span class="data-unit">天</span>
                </div>
              </div>
              <div class="arrow-icon">
                <i class="arrow down"></i>
              </div>
              <div class="data-item">
                <div class="data-label">实际工期</div>
                <div class="data-value">
                  31.21<span class="data-unit">天</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="content-block half-block">
          <div class="block-title">年度平均能力因子</div>
          <div class="block-content">
            <div class="data-comparison">
              <div class="data-item">
                <div class="data-label">计划工期</div>
                <div class="data-value">
                  92.69<span class="data-unit">%</span>
                </div>
              </div>
              <div class="arrow-icon">
                <i class="arrow down"></i>
              </div>
              <div class="data-item">
                <div class="data-label">实际工期</div>
                <div class="data-value">
                  93.54<span class="data-unit">%</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 第三行内容 (平分高度) - 三个色块，每个宽度25% -->
      <div class="row third-row">
        <div class="content-block record-block">
          <div class="block-title">QF-OT107</div>
          <div class="block-content">
            <div class="record-card">
              <div class="record-content">
                <div class="record-icon"></div>
                <div class="record-info">
                  <div class="record-label">年度最佳</div>
                  <div class="record-value">
                    25.02<span class="record-unit">天</span>
                  </div>
                </div>
              </div>
            </div>
            X
          </div>
        </div>

        <div class="content-block record-block">
          <div class="block-title">Q2-OT105</div>
          <div class="block-content">
            <div class="record-card">
              <div class="record-content">
                <div class="record-icon"></div>
                <div class="record-info">
                  <div class="record-label">年度A类最佳</div>
                  <div class="record-value">
                    25.02<span class="record-unit">天</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="content-block record-block">
          <div class="block-title">HN-OT206</div>
          <div class="block-content">
            <div class="record-card">
              <div class="record-content">
                <div class="record-icon"></div>
                <div class="record-info">
                  <div class="record-label">M310年度最佳</div>
                  <div class="record-value">
                    25.02<span class="record-unit">天</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// 可以在这里添加数据和逻辑
</script>

<style lang="scss" scoped>
.overview-container {
  width: 100%;
  height: 100%;
  display: flex;
  padding: 10px;
  flex-direction: column;
  border-radius: 4px;
  box-sizing: border-box;
  overflow: hidden;
}

/* 标题样式 - 占5%高度 */
.section-header {
  display: flex;
  align-items: center;
  height: 5%;
  min-height: 30px;
  margin-bottom: 10px;
  flex-shrink: 0;
}

.header-line {
  width: 4px;
  height: 25px; /* 增加高度匹配标题 */
  background-color: #00f0ff;
  margin-right: 8px;
}

.section-title {
  color: #00f0ff;
  font-size: 25px; /* 增加大修概览标题大小为25px */
  margin: 0;
  font-weight: normal;
  white-space: nowrap;
}

/* 内容区域 - 占95%高度 */
.content-area {
  height: 95%;
  display: flex;
  flex-direction: column;
  gap: 10px;
  overflow: hidden;
}

/* 行布局 - 平均分配高度 */
.row {
  display: flex;
  gap: 10px;
  flex: 1;
  min-height: 0; /* 允许行在必要时收缩 */
}

.third-row {
  justify-content: space-around;
}

/* 内容块样式 */
.content-block {
  display: flex;
  flex-direction: column;
  flex: 1;
  background-color: rgba(0, 240, 255, 0.05); /* 浅蓝色背景 */
  border: 1px solid rgba(0, 240, 255, 0.2);
  border-radius: 4px;
  overflow: hidden;
  box-sizing: border-box;
}

.half-block {
  width: calc(50% - 5px);
  flex: 0 0 calc(50% - 5px);
}

.record-block {
  width: 30%;
  flex: 0 0 30%;
  max-width: 200px;
}

.block-title {
  font-size: 18px; /* 调整内容标题大小为18px */
  color: #00f0ff;
  text-align: center;
  font-weight: bold;
  padding: 5px 0;
  border-bottom: 1px solid rgba(0, 240, 255, 0.3);
  margin: 0;
}

.block-content {
  padding: 5px;
  display: flex;
  flex-direction: column;
  flex: 1;
}

/* 工期信息样式 */
.period-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
  flex-shrink: 0;
}

.period-item {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.period-label {
  color: #b0bec5;
  font-size: 12px;
  margin-right: 5px; /* 添加右侧间距 */
}

.period-value {
  color: #00f0ff;
  font-size: 16px;
  font-weight: bold;
}

.period-value.negative {
  color: #ff4d4f;
}

.period-unit {
  font-size: 12px;
  margin-left: 2px;
}

.progress-bar {
  height: 18px; /* 调整为18px高度 */
  background-color: rgba(176, 190, 197, 0.2);
  border-radius: 9px; /* 半高度圆角 */
  overflow: hidden;
  margin-top: auto;
  flex-shrink: 0;
  position: relative; /* 为标签定位 */
}

.progress-fill {
  height: 100%;
  background-color: #65cfa6; /* 浅绿色进度条 */
  border-radius: 9px; /* 半高度圆角 */
}

.progress-labels {
  position: absolute;
  width: 100%;
  display: flex;
  justify-content: space-between;
  padding: 0 10px;
  top: 0;
  left: 0;
  height: 100%;
  align-items: center;
  box-sizing: border-box;
  font-size: 10px;
  color: #ffffff;
}

/* 数据比较样式 */
.data-comparison {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex: 1;
}

.data-item {
  text-align: center;
  flex: 1;
}

.data-label {
  color: #b0bec5;
  font-size: 12px;
  margin-bottom: 2px; /* 减小间距 */
}

.data-value {
  color: #00f0ff;
  font-size: 16px;
  font-weight: bold;
}

.data-unit {
  font-size: 12px;
  margin-left: 2px;
}

.arrow-icon {
  width: 24px; /* 减小图标尺寸 */
  height: 24px;
  background-color: rgba(0, 240, 255, 0.1);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
}

.arrow {
  border: solid #00f0ff;
  border-width: 0 2px 2px 0;
  display: inline-block;
  padding: 3px;
}

.down {
  transform: rotate(45deg);
  -webkit-transform: rotate(45deg);
}

/* 最佳记录样式 */
.code-label {
  color: #b0bec5;
  font-size: 12px;
  text-align: center;
  margin-bottom: 8px;
  flex-shrink: 0;
}

.record-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  height: 100%;
}

.record-content {
  display: flex;
  width: 100%;
  padding: 5px;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.record-icon {
  width: 36px;
  height: 36px;
  background-color: rgba(0, 240, 255, 0.1);
  border: 1px solid rgba(0, 240, 255, 0.5);
  border-radius: 50%;
  margin-right: 10px;
  flex-shrink: 0;
}

.record-info {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.record-label {
  color: #b0bec5;
  font-size: 12px;
  margin-bottom: 2px;
}

.record-value {
  color: #00f0ff;
  font-size: 16px;
  font-weight: bold;
}

.record-unit {
  font-size: 12px;
  margin-left: 2px;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .section-title {
    font-size: 20px; /* 响应式调整大修概览标题 */
  }

  .block-title {
    font-size: 16px; /* 响应式调整内容标题 */
  }

  .period-value,
  .data-value,
  .record-value {
    font-size: 14px;
  }

  .period-label,
  .data-label,
  .record-label,
  .code-label,
  .period-unit,
  .data-unit,
  .record-unit {
    font-size: 10px;
  }

  .record-icon {
    width: 30px;
    height: 30px;
  }

  .arrow-icon {
    width: 20px;
    height: 20px;
  }
}
</style>
