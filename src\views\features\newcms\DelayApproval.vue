<template>
  <div>
    <a-table
      :columns="delayColumns"
      :data-source="delayTableData"
      :pagination="false"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'actions'">
          <a-space>
            <a-button type="primary" size="small" @click="handleApprove(record)">
              批准
            </a-button>
            <a-button type="primary" danger size="small" @click="handleReject(record)">
              回退
            </a-button>
          </a-space>
        </template>
      </template>
    </a-table>
  </div>
</template>

<script setup>
import { ref } from "vue";
import { message } from "ant-design-vue";

// 延期审批表格列定义
const delayColumns = [
  {
    title: "责任部门",
    dataIndex: "department",
    key: "department",
  },
  {
    title: "科室",
    dataIndex: "office",
    key: "office",
  },
  {
    title: "协调人",
    dataIndex: "coordinator",
    key: "coordinator",
  },
  {
    title: "责任人",
    dataIndex: "responsible",
    key: "responsible",
  },
  {
    title: "处室责任人",
    dataIndex: "officeResponsible",
    key: "officeResponsible",
  },
  {
    title: "延期说明",
    dataIndex: "delayReason",
    key: "delayReason",
  },
  {
    title: "申请延期时间",
    dataIndex: "applyDelayTime",
    key: "applyDelayTime",
  },
  {
    title: "批准延期时间",
    dataIndex: "approveDelayTime",
    key: "approveDelayTime",
  },
  {
    title: "审批",
    key: "actions",
    width: 150,
  },
];

// 延期审批表格数据
const delayTableData = ref([
  {
    id: 1,
    department: "技术部",
    office: "研发科",
    coordinator: "张三",
    responsible: "李四",
    officeResponsible: "王五",
    delayReason: "需要更多时间进行测试",
    applyDelayTime: "2024-03-20",
    approveDelayTime: "",
  },
  {
    id: 2,
    department: "运营部",
    office: "运维科",
    coordinator: "赵六",
    responsible: "钱七",
    officeResponsible: "孙八",
    delayReason: "依赖系统升级延迟",
    applyDelayTime: "2024-03-21",
    approveDelayTime: "",
  },
]);

// 处理批准操作
const handleApprove = (record) => {
  console.log("批准延期", record);
  message.success("已批准延期申请");
};

// 处理回退操作
const handleReject = (record) => {
  console.log("回退延期", record);
  message.success("已回退延期申请");
};
</script>

<style scoped>
:deep(.ant-table-thead > tr > th) {
  text-align: center;
}

:deep(.ant-table-tbody > tr > td) {
  text-align: center;
}
</style> 