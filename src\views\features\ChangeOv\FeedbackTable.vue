<template>
  <div class="feedback-table-container">
    <a-table
      :columns="columns"
      :data-source="tableData"
      bordered
      size="middle"
      :pagination="false"
    >
      <template #bodyCell="{ column, text }">
        <template v-if="column.dataIndex === 'progress'">
          <span>{{ text }}</span>
        </template>
      </template>
    </a-table>
  </div>
</template>

<script setup>
import { ref } from "vue";

// 定义表格列
const columns = [
  {
    title: "序号",
    dataIndex: "index",
    width: 60,
    align: "center",
  },
  {
    title: "标题",
    dataIndex: "title",
    width: 220,
  },
  {
    title: "责任工程师",
    dataIndex: "engineer",
    width: 100,
    align: "center",
  },
  {
    title: "采购申请单号",
    dataIndex: "purchaseReq",
    width: 150,
    align: "center",
  },
  {
    title: "进展",
    dataIndex: "progress",
    width: 200,
  },
  {
    title: "需关注事项",
    dataIndex: "attention",
    width: 250,
  },
  {
    title: "备注",
    dataIndex: "remarks",
    width: 100,
  },
];

// 表格数据
const tableData = [
  {
    key: "1",
    index: 1,
    title: "12汀非安全级抗震调阀设计及物项替代",
    engineer: "周振柱",
    purchaseReq: "备件需求单号：412003206",
    progress: "备件已通过PLT中审核，目前尚未完成立项。",
    attention:
      "请关于月完成变立项申请，由于无PL申请，采购立项后请商工重点关注采购进展",
    remarks: "",
  },
  {
    key: "2",
    index: 2,
    title: "工兆A、B热工保护回路优化",
    engineer: "孙王明",
    purchaseReq: "初步设计特上电/技术委员会",
    progress: "请关于交文审中申请，请初步设计通过后半月内提交采购立项申请",
    attention: "请关于提交申请",
    remarks: "",
  },
  {
    key: "3",
    index: 3,
    title: "电站计算机系统操作员站频发掉实时请求改造",
    engineer: "王峰华",
    purchaseReq: "初步设计特上电/技术委员会",
    progress: "请关于交文审中申请，请初步设计通过后半月内提交采购立项申请",
    attention: "请关于提交申请",
    remarks: "",
  },
  {
    key: "4",
    index: 4,
    title:
      "#O4#广网内工业水箱、真空密封水箱、定子水箱、汽子水箱液位远传测出计算机",
    engineer: "俞晓",
    purchaseReq: "初步设计特上电/技术委员会",
    progress: "请关于交文审中申请，请初步设计通过后半月内提交采购立项申请",
    attention: "请关于提交申请",
    remarks: "",
  },
  {
    key: "5",
    index: 5,
    title: "",
    engineer: "抓取",
    purchaseReq: "抓取",
    progress: "人工反馈",
    attention: "",
    remarks: "",
  },
];
</script>

<style scoped>
.feedback-table-container {
  width: 100%;
}
</style>
