<template>
  <a-modal
    v-model:visible="visible"
    :title="modalTitle"
    width="1400px"
    :footer="null"
    @cancel="handleCancel"
    :bodyStyle="{ height: '700px', overflow: 'auto', padding: '0' }"
  >
    <div class="task-manage-container">
      <!-- 查询条件区域 -->
      <div class="query-area">
        <div class="query-item">
          <span class="query-label">大修选择：</span>
          <a-select
            v-model:value="queryParams.repairId"
            style="width: 180px"
            placeholder="请选择大修"
            allowClear
            size="small"
          >
            <a-select-option
              v-for="item in repairOptions"
              :key="item.value"
              :value="item.value"
            >
              {{ item.label }}
            </a-select-option>
          </a-select>
        </div>
        <div class="query-item">
          <span class="query-label">处室选择：</span>
          <a-select
            v-model:value="queryParams.departmentId"
            style="width: 180px"
            placeholder="请选择处室"
            allowClear
            size="small"
          >
            <a-select-option
              v-for="item in departmentOptions"
              :key="item.value"
              :value="item.value"
            >
              {{ item.label }}
            </a-select-option>
          </a-select>
        </div>
        <div class="query-buttons">
          <a-button type="primary" size="small" @click="handleQuery"
            >查询</a-button
          >
          <a-button size="small" style="margin-left: 8px" @click="resetQuery"
            >重置</a-button
          >
        </div>
      </div>

      <!-- 标题和操作区域 -->
      <div class="task-header">
        <h3>大修任务管理</h3>
        <a-button type="primary" size="small" @click="handleAddTask"
          >新增</a-button
        >
      </div>

      <!-- 表格区域 -->
      <a-table
        :columns="columns"
        :data-source="taskList"
        :pagination="{ pageSize: 10 }"
        bordered
        size="middle"
        :scroll="{ x: 1500 }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'action'">
            <a-space size="small">
              <a @click="handleEdit(record)">修改</a>
              <a-divider type="vertical" />
              <a-popconfirm
                title="确定要删除这条任务吗?"
                @confirm="handleDelete(record)"
              >
                <a>删除</a>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </div>
  </a-modal>

  <!-- 引入新增任务弹窗组件 -->
  <AddTaskModal
    ref="addTaskModalRef"
    @add-success="handleAddSuccess"
    @edit-success="handleEditSuccess"
  />
</template>

<script setup lang="ts">
import { ref, defineExpose, reactive, computed } from "vue";
import AddTaskModal from "./AddTaskModal.vue";

// 引用新增任务模态框
const addTaskModalRef = ref(null);

// 模态框可见性状态
const visible = ref(false);
// 当前选中的大修编号
const currentRepairCode = ref("");

// 模态框标题
const modalTitle = computed(() => {
  if (currentRepairCode.value) {
    return `${currentRepairCode.value} 大修任务管理(年度配置管理二级页面)`;
  }
  return "Q2-0T412大修任务管理(年度配置管理二级页面)";
});

// 查询参数
const queryParams = reactive({
  repairId: undefined,
  departmentId: undefined,
});

// 大修选项
const repairOptions = [
  { value: "QX-0099", label: "2023年设备检修" },
  { value: "QX-0011", label: "2023年电气检修" },
  { value: "QX-00139", label: "2023年机械检修" },
];

// 处室选项
const departmentOptions = [
  { value: "dept1", label: "运行处" },
  { value: "dept2", label: "技术处" },
  { value: "dept3", label: "安全处" },
];

// 查询按钮点击事件
const handleQuery = () => {
  console.log("查询条件:", queryParams);
  // 根据查询条件过滤任务列表
  // 实际应用中，这里可能会调用API进行查询
};

// 重置查询条件
const resetQuery = () => {
  queryParams.repairId = undefined;
  queryParams.departmentId = undefined;
};

// 表格列定义
const columns = [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    width: 70,
    align: "center",
    fixed: "left",
  },
  {
    title: "任务属性",
    dataIndex: "taskType",
    key: "taskType",
    width: 120,
  },
  {
    title: "处室",
    dataIndex: "department",
    key: "department",
    width: 100,
  },
  {
    title: "任务名称",
    dataIndex: "taskName",
    key: "taskName",
    width: 150,
  },
  {
    title: "窗口",
    dataIndex: "window",
    key: "window",
    width: 80,
  },
  {
    title: "阶段",
    dataIndex: "stage",
    key: "stage",
    width: 80,
  },
  {
    title: "人员要求",
    dataIndex: "personnelRequirement",
    key: "personnelRequirement",
    width: 140,
  },
  {
    title: "到场时间",
    dataIndex: "startTime",
    key: "startTime",
    width: 100,
  },
  {
    title: "离场时间",
    dataIndex: "endTime",
    key: "endTime",
    width: 100,
  },
  {
    title: "标准人数",
    dataIndex: "standardCount",
    key: "standardCount",
    width: 130,
    align: "center",
  },
  {
    title: "已配置",
    dataIndex: "configuredCount",
    key: "configuredCount",
    width: 80,
    align: "center",
  },
  {
    title: "缺口",
    dataIndex: "gap",
    key: "gap",
    width: 60,
    align: "center",
  },
  {
    title: "备注",
    dataIndex: "remark",
    key: "remark",
    width: 150,
  },
  {
    title: "操作",
    key: "action",
    fixed: "right",
    width: 120,
  },
];

// 示例任务数据
const taskList = ref([
  {
    key: "1",
    index: 1,
    taskType: "检修",
    department: "运行处",
    taskName: "设备检修",
    window: "窗口1",
    stage: "阶段1",
    personnelRequirement: "高级工程师",
    startTime: "1T+3D",
    endTime: "2T+5D",
    standardCount: 5,
    configuredCount: 3,
    gap: 2,
    remark: "重点任务",
  },
  {
    key: "2",
    index: 2,
    taskType: "维护",
    department: "技术处",
    taskName: "设备检修2",
    window: "窗口2",
    stage: "阶段2",
    personnelRequirement: "中级工程师",
    startTime: "2T+0D",
    endTime: "3T+2D",
    standardCount: 4,
    configuredCount: 4,
    gap: 0,
    remark: "已完成分配",
  },
  {
    key: "3",
    index: 3,
    taskType: "安装",
    department: "安全处",
    taskName: "设备检修9",
    window: "窗口3",
    stage: "阶段1",
    personnelRequirement: "初级工程师",
    startTime: "3T+1D",
    endTime: "4T+0D",
    standardCount: 6,
    configuredCount: 2,
    gap: 4,
    remark: "紧急任务",
  },
]);

// 添加任务事件处理
const handleAddTask = () => {
  console.log("添加新任务");
  // 打开新增任务模态框
  addTaskModalRef.value.open();
};

// 处理新增任务成功
const handleAddSuccess = (newTask) => {
  // 将新任务添加到列表顶部
  newTask.index = taskList.value.length + 1;
  newTask.key = String(newTask.index);
  taskList.value.unshift(newTask);
  console.log("任务添加成功:", newTask);
};

// 编辑任务事件处理
const handleEdit = (record) => {
  // 打开编辑任务模态框
  addTaskModalRef.value.openEdit(record);
};

// 处理编辑任务成功
const handleEditSuccess = (updatedTask) => {
  console.log("任务更新成功:", updatedTask);
};

// 打开模态框的方法
const open = (repairCode = "") => {
  visible.value = true;
  currentRepairCode.value = repairCode;
  if (repairCode) {
    // 如果有传入大修编号，自动设置查询参数
    queryParams.repairId = repairCode;
    // 执行查询
    handleQuery();
  }
};

// 关闭模态框的方法
const close = () => {
  visible.value = false;
};

// 取消按钮事件处理
const handleCancel = () => {
  close();
};

// 删除任务事件处理
const handleDelete = (record) => {
  console.log("删除任务", record);
  // 这里可以添加删除任务的逻辑
  // 从列表中移除该任务
  taskList.value = taskList.value.filter((item) => item.key !== record.key);
};

// 暴露方法供父组件调用
defineExpose({
  open,
  close,
});
</script>

<style scoped>
.task-manage-container {
  padding: 16px;
  overflow: visible;
}

.query-area {
  background-color: #f8f8f8;
  padding: 12px 16px;
  border-radius: 4px;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.query-item {
  margin-right: 16px;
  display: flex;
  align-items: center;
}

.query-label {
  margin-right: 8px;
  white-space: nowrap;
  font-size: 13px;
}

.query-buttons {
  display: flex;
  align-items: center;
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.task-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
}
</style>
