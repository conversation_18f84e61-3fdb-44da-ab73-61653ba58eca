<template>
  <div class="change-info-container">
    <!-- 查询条件区域 -->
    <div class="query-card">
      <div class="card-header">
        <span class="title">变更信息查询</span>
        <a-button type="link" @click="expandForm = !expandForm">
          {{ expandForm ? '收起' : '展开' }}
          <template #icon>
            <down-outlined v-if="!expandForm" />
            <up-outlined v-else />
          </template>
        </a-button>
      </div>
      
      <a-form layout="horizontal" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <a-row :gutter="24">
          <a-col :span="8">
            <a-form-item label="变更标题">
              <a-input v-model:value="queryParams.title" placeholder="请输入变更标题" allow-clear />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="变更号">
              <a-input v-model:value="queryParams.changeNo" placeholder="请输入变更号" allow-clear />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="变更类型">
              <a-select v-model:value="queryParams.type" placeholder="请选择变更类型" allow-clear>
                <a-select-option value="常规变更">常规变更</a-select-option>
                <a-select-option value="紧急变更">紧急变更</a-select-option>
                <a-select-option value="临时变更">临时变更</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        
        <template v-if="expandForm">
          <a-row :gutter="24">
            <a-col :span="8">
              <a-form-item label="专业">
                <a-input v-model:value="queryParams.specialty" placeholder="请输入专业" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="责任部门">
                <a-input v-model:value="queryParams.department" placeholder="请输入责任部门" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="实施负责人">
                <a-input v-model:value="queryParams.responsiblePerson" placeholder="请输入实施负责人" allow-clear />
              </a-form-item>
            </a-col>
          </a-row>
          
          <a-row :gutter="24">
            <a-col :span="8">
              <a-form-item label="变更状态">
                <a-select v-model:value="queryParams.status" placeholder="请选择变更状态" allow-clear>
                  <a-select-option value="进行中">进行中</a-select-option>
                  <a-select-option value="已完成">已完成</a-select-option>
                  <a-select-option value="已暂停">已暂停</a-select-option>
                  <a-select-option value="已取消">已取消</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="开工日期">
                <a-range-picker v-model:value="dateRange" style="width: 100%" />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="电厂">
                <a-select v-model:value="queryParams.plant" placeholder="请选择电厂" allow-clear>
                  <a-select-option value="华能电厂">华能电厂</a-select-option>
                  <a-select-option value="大唐电厂">大唐电厂</a-select-option>
                  <a-select-option value="国电电厂">国电电厂</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
        </template>
        
        <a-row>
          <a-col :span="24" style="text-align: right">
            <a-space>
              <a-button @click="handleReset">重置</a-button>
              <a-button type="primary" @click="handleSearch">查询</a-button>
              <a-button type="primary" danger @click="handleDelete" :disabled="!hasSelected">
                <template #icon><delete-outlined /></template>删除
              </a-button>
              <a-button @click="showDeletedPreview">
                <template #icon><eye-outlined /></template>预览已删除数据
              </a-button>
            </a-space>
          </a-col>
        </a-row>
      </a-form>
    </div>

    <!-- 表格区域 -->
    <div class="table-card">
      <div class="table-operations">
        <a-space>
          <a-button type="primary">
            <template #icon><plus-outlined /></template>新增
          </a-button>
          <a-button>
            <template #icon><export-outlined /></template>导出
          </a-button>
        </a-space>
        
        <span class="selected-info" v-if="hasSelected">
          已选择 <a>{{ selectedRowKeys.length }}</a> 项
          <a @click="() => selectedRowKeys = []">清空</a>
        </span>
      </div>
      
      <a-table
        :columns="columns"
        :data-source="tableData"
        :pagination="pagination"
        :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
        :scroll="{ x: 1500 }"
        :loading="loading"
        bordered
        size="middle"
      >
        <template #headerCell="{ column }">
          <template v-if="column.key === 'action'">
            <span class="action-column-title">操作</span>
          </template>
        </template>
        
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-tag :color="getStatusColor(record.status)">{{ record.status }}</a-tag>
          </template>
          
          <template v-else-if="column.key === 'action'">
            <a-space>
              <!-- <a @click="handleView(record)">查看</a>
              <a-divider type="vertical" />
              <a class="edit-link" @click="handleEdit(record)">编辑</a>
              <a-divider type="vertical" />
              <a class="delete-link" @click="handleDeleteSingle(record)">删除</a> -->
            </a-space>
          </template>
        </template>
      </a-table>
    </div>

    <!-- 预览已删除数据的模态框 -->
    <a-modal
      v-model:visible="deletedPreviewVisible"
      title="预览已删除数据"
      width="1200px"
      :footer="null"
    >
      <div class="deleted-preview-container">
        <div class="deleted-preview-header">
          <span class="title">已删除数据列表</span>
          <a-space>
            <a-button type="primary" :disabled="!hasSelectedDeleted" @click="handleRestore">
              <template #icon><undo-outlined /></template>恢复选中
            </a-button>
            <a-button @click="deletedPreviewVisible = false">关闭</a-button>
          </a-space>
        </div>
        
        <a-alert
          v-if="hasSelectedDeleted"
          type="info"
          show-icon
          :message="`已选择 ${selectedDeletedRowKeys.length} 项数据，可点击「恢复选中」按钮恢复数据`"
          style="margin-bottom: 16px"
        />
        
        <a-table
          :columns="columns.filter(col => col.key !== 'action')"
          :data-source="deletedData"
          :row-selection="{ selectedRowKeys: selectedDeletedRowKeys, onChange: onDeletedSelectChange }"
          :pagination="{ pageSize: 5, showTotal: (total) => `共 ${total} 条` }"
          :scroll="{ x: 1500 }"
          bordered
          size="middle"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'status'">
              <a-tag color="default">{{ record.status }}</a-tag>
            </template>
          </template>
        </a-table>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue';
import { 
  DownOutlined, 
  UpOutlined, 
  DeleteOutlined, 
  EyeOutlined, 
  PlusOutlined, 
  ExportOutlined,
  UndoOutlined 
} from '@ant-design/icons-vue';

// 表单展开/收起状态
const expandForm = ref(false);

// 日期范围
const dateRange = ref([]);

// 加载状态
const loading = ref(false);

// 查询参数
const queryParams = reactive({
  title: '',
  changeNo: '',
  type: undefined,
  specialty: '',
  department: '',
  responsiblePerson: '',
  status: undefined,
  plant: undefined
});

// 表格列定义
const columns = [
  { title: '序号', dataIndex: 'index', key: 'index', width: 70, fixed: 'left' },
  { title: '变更标题', dataIndex: 'title', key: 'title', width: 180, ellipsis: true },
  { title: '类型', dataIndex: 'type', key: 'type', width: 100 },
  { title: '变更号', dataIndex: 'changeNo', key: 'changeNo', width: 120 },
  { title: '变更版本', dataIndex: 'version', key: 'version', width: 100 },
  { title: '电厂', dataIndex: 'plant', key: 'plant', width: 120 },
  { title: '变更状态', dataIndex: 'status', key: 'status', width: 100 },
  { title: '变更工单', dataIndex: 'workOrder', key: 'workOrder', width: 120 },
  { title: '最早开工时间', dataIndex: 'startTime', key: 'startTime', width: 150 },
  { title: '最新结束时间', dataIndex: 'endTime', key: 'endTime', width: 150 },
  { title: '机组', dataIndex: 'unit', key: 'unit', width: 100 },
  { title: '专业', dataIndex: 'specialty', key: 'specialty', width: 100 },
  { title: '责任工程师', dataIndex: 'engineer', key: 'engineer', width: 120 },
//   { title: '操作', key: 'action', fixed: 'right', width: 150 }
];

// 状态颜色映射
const getStatusColor = (status) => {
  const statusMap = {
    '进行中': 'processing',
    '已完成': 'success',
    '已暂停': 'warning',
    '已取消': 'error',
    '审批中': 'blue'
  };
  return statusMap[status] || 'default';
};

// 模拟表格数据
const tableData = ref([
  {
    key: '1',
    index: 1,
    title: '某设备更换计划',
    type: '设备更换',
    changeNo: 'CG2023001',
    version: 'V1.0',
    plant: '华能电厂',
    status: '进行中',
    workOrder: 'WO2023001',
    startTime: '2023-01-15',
    endTime: '2023-02-28',
    unit: '1号机组',
    specialty: '电气',
    engineer: '张工',
  },
  {
    key: '2',
    index: 2,
    title: '某系统升级改造',
    type: '系统升级',
    changeNo: 'CG2023002',
    version: 'V1.1',
    plant: '大唐电厂',
    status: '已完成',
    workOrder: 'WO2023002',
    startTime: '2023-02-10',
    endTime: '2023-03-15',
    unit: '2号机组',
    specialty: '热控',
    engineer: '李工',
  },
  {
    key: '3',
    index: 3,
    title: '某管道维修变更',
    type: '维修',
    changeNo: 'CG2023003',
    version: 'V1.0',
    plant: '国电电厂',
    status: '审批中',
    workOrder: 'WO2023003',
    startTime: '2023-03-20',
    endTime: '2023-04-10',
    unit: '3号机组',
    specialty: '机械',
    engineer: '王工',
  },
  {
    key: '4',
    index: 4,
    title: '电气系统备用电源优化',
    type: '系统优化',
    changeNo: 'CG2023004',
    version: 'V1.0',
    plant: '华能电厂',
    status: '已暂停',
    workOrder: 'WO2023004',
    startTime: '2023-03-05',
    endTime: '2023-04-30',
    unit: '2号机组',
    specialty: '电气',
    engineer: '李工',
  },
  {
    key: '5',
    index: 5,
    title: '汽机控制系统升级',
    type: '系统升级',
    changeNo: 'CG2023005',
    version: 'V1.2',
    plant: '大唐电厂',
    status: '进行中',
    workOrder: 'WO2023005',
    startTime: '2023-04-01',
    endTime: '2023-05-15',
    unit: '1号机组',
    specialty: '热控',
    engineer: '赵工',
  }
]);

// 已删除的数据
const deletedData = ref([
  {
    key: '101',
    index: 1,
    title: '已删除的变更计划1',
    type: '设备更换',
    changeNo: 'CG2022101',
    version: 'V2.0',
    plant: '华能电厂',
    status: '已取消',
    workOrder: 'WO2022101',
    startTime: '2022-11-10',
    endTime: '2022-12-25',
    unit: '1号机组',
    specialty: '电气',
    engineer: '张工',
  },
  {
    key: '102',
    index: 2,
    title: '已删除的变更计划2',
    type: '系统升级',
    changeNo: 'CG2022102',
    version: 'V1.0',
    plant: '大唐电厂',
    status: '已取消',
    workOrder: 'WO2022102',
    startTime: '2022-10-15',
    endTime: '2022-11-20',
    unit: '2号机组',
    specialty: '热控',
    engineer: '李工',
  },
]);

// 分页设置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 100,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条`,
});

// 选中的行
const selectedRowKeys = ref([]);
// 已删除数据中选中的行
const selectedDeletedRowKeys = ref([]);

// 是否有选中的行
const hasSelected = computed(() => selectedRowKeys.value.length > 0);
// 是否有选中的已删除数据行
const hasSelectedDeleted = computed(() => selectedDeletedRowKeys.value.length > 0);

// 已删除数据预览模态框显示状态
const deletedPreviewVisible = ref(false);

// 选择行变化时触发
const onSelectChange = (keys) => {
  selectedRowKeys.value = keys;
};

// 选择已删除数据行变化时触发
const onDeletedSelectChange = (keys) => {
  selectedDeletedRowKeys.value = keys;
};

// 查询按钮点击事件
const handleSearch = () => {
  loading.value = true;
  console.log('搜索参数:', queryParams, '日期范围:', dateRange.value);
  // 模拟请求延迟
  setTimeout(() => {
    loading.value = false;
    pagination.current = 1;
  }, 500);
};

// 重置按钮点击事件
const handleReset = () => {
  // 重置查询表单
  Object.keys(queryParams).forEach(key => {
    queryParams[key] = undefined;
  });
  dateRange.value = [];
};

// 删除按钮点击事件
const handleDelete = () => {
  if (!hasSelected.value) {
    return;
  }
  
  console.log('删除记录:', selectedRowKeys.value);
  // 实现实际删除逻辑
};

// 删除单条记录
const handleDeleteSingle = (record) => {
  console.log('删除单条记录:', record);
  // 实现实际删除逻辑
};

// 查看按钮点击事件
const handleView = (record) => {
  console.log('查看记录:', record);
  // 实现查看详情逻辑
};

// 编辑按钮点击事件
const handleEdit = (record) => {
  console.log('编辑记录:', record);
  // 实现编辑逻辑
};

// 显示已删除数据预览模态框
const showDeletedPreview = () => {
  deletedPreviewVisible.value = true;
  selectedDeletedRowKeys.value = [];
};

// 恢复选中的已删除数据
const handleRestore = () => {
  if (!hasSelectedDeleted.value) {
    return;
  }
  
  console.log('恢复已删除数据:', selectedDeletedRowKeys.value);
  // 实现恢复逻辑
  deletedPreviewVisible.value = false;
};
</script>

<style scoped>
.change-info-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px;
  height: 100%;
  background-color: #f0f2f5;
}

.query-card,
.table-card {
  background-color: #fff;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0,0,0,0.03);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.card-header .title {
  font-size: 16px;
  font-weight: 500;
  color: #000000d9;
}

.table-operations {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.selected-info {
  font-size: 14px;
}

.selected-info a {
  color: #1890ff;
  margin: 0 4px;
}

.action-column-title {
  color: #000000d9;
}

.edit-link {
  color: #1890ff;
}

.delete-link {
  color: #ff4d4f;
}

.deleted-preview-container {
  display: flex;
  flex-direction: column;
}

.deleted-preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.deleted-preview-header .title {
  font-size: 16px;
  font-weight: 500;
  color: #000000d9;
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
  .table-operations {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .selected-info {
    margin-top: 8px;
  }
}
</style> 