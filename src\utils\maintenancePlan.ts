import dayjs from 'dayjs';
import type { Dayjs } from 'dayjs';

// 日期相关方法
export const dateUtils = {
  getSpringFestivalDate: (year: number) => {
    const springFestivalDate = parseSpringFestivalDate(year)?.toLocaleDateString();
    if (!springFestivalDate) return null;
    return dayjs(springFestivalDate);
  },

  isPassingSpringFestival: (startTime: string, endTime: string, year: number) => {
    if (!startTime || !endTime) return false;

    const maintenanceStart = dayjs(startTime);
    const maintenanceEnd = dayjs(endTime);
    
    const springFestivalDate = dateUtils.getSpringFestivalDate(year);
    if (!springFestivalDate) return false;
    
    const springFestivalStart = springFestivalDate.subtract(15, 'day');
    const springFestivalEnd = springFestivalDate.add(15, 'day');

    return !(maintenanceEnd.isBefore(springFestivalStart) || 
             maintenanceStart.isAfter(springFestivalEnd));
  }
};

// 模态框相关方法
export const modalUtils = {
  handleClick: (modalRef: any, powerName: string, data: any) => {
    modalRef.value?.openModal({
      powerName,
      ...data
    });
  },

  handleTextClick: (modalRef: any, powerName: string, data: any) => {
    modalRef.value?.openModal({
      powerName,
      ...data
    });
  }
};

// 表单相关方法
export const formUtils = {
  handleSearch: (formState: any, statisticsYears: any) => {
    statisticsYears.start = formState.startTime?.format('YYYY') || '--';
    statisticsYears.end = formState.endTime?.format('YYYY') || '--';
  },

  handleReset: (formState: any, statisticsYears: any) => {
    formState.startTime = dayjs().startOf('year');
    formState.endTime = dayjs().add(4, 'year').endOf('year');
    statisticsYears.start = formState.startTime.format('YYYY');
    statisticsYears.end = formState.endTime.format('YYYY');
  }
};

// 常量配置
export const constants = {
  rowColors: ["#1890ff", "#52c41a", "#faad14", "#722ed1", "#eb2f96"]
}; 