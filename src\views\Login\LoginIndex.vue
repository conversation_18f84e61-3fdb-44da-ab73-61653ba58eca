<template>
  <el-container class="login-container">
    <el-card class="login-card">
      <h2 class="login-title">欢迎登录</h2>
      <el-form label-width="auto" :model="form" ref="formRef" status-icon>
        <el-form-item label="用户名" prop="username" :rules="usernameRules">
          <el-input v-model="form.username" placeholder="请输入用户名">
            <template #prefix>
              <i class="el-icon-user"></i>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item label="密码" prop="password" :rules="passwordRules">
          <el-input
            type="password"
            v-model="form.password"
            placeholder="请输入密码"
          >
            <template #prefix>
              <i class="el-icon-lock"></i>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleLogin">登录</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </el-container>
</template>

<script setup>
import { ref, toRefs, reactive } from "vue";
import { useRouter } from "vue-router";
import {
  ElForm,
  ElFormItem,
  ElInput,
  ElButton,
  ElCard,
  ElContainer,
  ElMessage,
} from "element-plus";

//定义表单数据
const form = reactive({
  username: "",
  password: "",
});

// 正则设置
const usernameRules = [
  { required: true, message: "请输入用户名", trigger: "blur" },
];
const passwordRules = [
  { required: true, message: "请输入密码", trigger: "blur" },
];
const router = useRouter();
const formRef = ref(null);
const handleLogin = async () => {
  if (!formRef.value) return;
  try {
    await formRef.value.validate();
    const { username, password } = toRefs(form);
    localStorage.setItem("User", username.value);
    localStorage.setItem("password", password.value);
    ElMessage({
      message: "登录成功",
      type: "success",
    });
    router.push("/menu");
  } catch (error) {
    ElMessage.error("登录失败");
  }
};
</script>

<style scoped>
.login-container {
  display: flex; /* 使用 Flexbox 对齐 */
  justify-content: center;
  align-items: center;
  height: 100vh; /* 使容器填满视口高度 */
  background-image: url("@/assets/bgc/Loginbgc.jpg");
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.login-card {
  flex-basis: 500px;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.title {
  text-align: center;
  margin-bottom: 20px;
  font-weight: bold;
  color: #35495e;
}

.el-form-item {
  margin-bottom: 20px;
}

.login-button {
  width: 100%;
}

.reset-button {
  width: 100%;
  margin-top: 10px;
}
</style>
