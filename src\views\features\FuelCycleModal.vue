<template>
  <a-modal
    v-model:open="visible"
    title="燃料循环参数设置"
    @ok="handleOk"
    @cancel="handleCancel"
    okText="确认"
    cancelText="取消"
    width="500px"
  >
    <div class="power-name-wrapper">
      <div class="power-name">{{ powerName }}</div>
    </div>
    <a-form
      :model="formState"
      layout="horizontal"
      :label-col="{ span: 6 }"
      class="fuel-cycle-form"
    >
      <!-- 标准燃料循环长度 -->
      <a-form-item label="标准燃料循环长度">
        <a-input v-model:value="formState.standardCycleLength" />
      </a-form-item>

      <!-- 需求燃料循环长度 -->
      <a-form-item label="需求燃料循环长度">
        <a-input v-model:value="formState.requiredCycleLength" />
      </a-form-item>

      <!-- 试验+升降功率损失 -->
      <a-form-item label="试验+升降功率损失">
        <a-input v-model:value="formState.powerLoss" />
      </a-form-item>

      <!-- 机组调停/调性 -->
      <a-form-item label="机组调停/调性">
        <div class="year-inputs">
          <span class="year-label">年前:</span>
          <a-input v-model:value="formState.beforeYearAdjust" />
          <span class="year-label">年后:</span>
          <a-input v-model:value="formState.afterYearAdjust" />
        </div>
      </a-form-item>

      <!-- 机组小修 -->
      <a-form-item label="机组小修">
        <div class="year-inputs">
          <span class="year-label">年前:</span>
          <a-input v-model:value="formState.beforeYearMaintenance" />
          <span class="year-label">年后:</span>
          <a-input v-model:value="formState.afterYearMaintenance" />
        </div>
      </a-form-item>

      <!-- 增/减燃料组件 -->
      <a-form-item label="增/减燃料组件">
        <a-input v-model:value="formState.fuelComponents" />
      </a-form-item>

      <!-- 降功率延伸运行 -->
      <a-form-item label="降功率延伸运行">
        <a-input v-model:value="formState.powerExtension" />
      </a-form-item>

      <!-- 机组循环内其他损失天数 -->
      <a-form-item label="机组循环内其他损失天数">
        <a-input v-model:value="formState.otherLossDays" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
import { ref } from "vue";

const visible = ref(false);
const powerName = ref("");
const formState = ref({
  standardCycleLength: "",
  requiredCycleLength: "",
  powerLoss: "",
  beforeYearAdjust: "",
  afterYearAdjust: "",
  beforeYearMaintenance: "",
  afterYearMaintenance: "",
  fuelComponents: "",
  powerExtension: "",
  otherLossDays: "",
});

// 打开模态框的方法
const openModal = (data: any) => {
  powerName.value = data.powerName;
  formState.value = {
    standardCycleLength: data.standardCycleLength || "",
    requiredCycleLength: data.requiredCycleLength || "",
    powerLoss: data.powerLoss || "",
    beforeYearAdjust: data.beforeYearAdjust || "",
    afterYearAdjust: data.afterYearAdjust || "",
    beforeYearMaintenance: data.beforeYearMaintenance || "",
    afterYearMaintenance: data.afterYearMaintenance || "",
    fuelComponents: data.fuelComponents || "",
    powerExtension: data.powerExtension || "",
    otherLossDays: data.otherLossDays || "",
  };
  visible.value = true;
};

// 处理确认
const handleOk = () => {
  visible.value = false;
};

// 处理取消
const handleCancel = () => {
  visible.value = false;
};

// 暴露方法给父组件
defineExpose({
  openModal,
});
</script>

<style scoped>
.power-name-wrapper {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
}

.power-name {
  font-size: 16px;
  font-weight: 500;
  padding: 8px 20px;
  background-color: #f5f5f5;
  border-radius: 4px;
  display: inline-block;
  width: 100%;
  text-align: center;
}

.fuel-cycle-form {
  padding: 0 4px;
}

.year-inputs {
  display: flex;
  align-items: center;
  gap: 8px;
}

.year-label {
  min-width: 36px;
}

:deep(.ant-modal-body) {
  padding: 24px 24px 0;
}

:deep(.ant-form-item) {
  margin-bottom: 24px;
}

:deep(.ant-form-item-label) {
  text-align: left;
}

:deep(.ant-modal-header) {
  border-bottom: none;
  padding-bottom: 0;
}

:deep(.ant-modal-title) {
  font-size: 18px;
  font-weight: 600;
}
</style>
