<template>
  <div class="stats-display">
    <div class="stat-item">
      <div class="top">
        <span>年度大修次数</span>
        <span class="value">6</span>
      </div>
      <div class="bottom">
        <span class="value">1A</span>
        <span class="value">2B</span>
        <span class="value">3C</span>
      </div>
    </div>
    <div class="stat-item">
      <div class="top">
        <span class="value">167/157</span>
      </div>
      <div class="bottom">
        <span class="description">计划/目标总工期</span>
      </div>
    </div>
    <div class="stat-item">
      <div class="sub-item">
        <span class="value">196</span>
        <span class="description">平均工期 (A)</span>
      </div>
      <div class="sub-item">
        <span class="value">196</span>
        <span class="description">平均工期 (B)</span>
      </div>
      <div class="sub-item">
        <span class="value">196</span>
        <span class="description">平均工期 (C)</span>
      </div>
    </div>
    <div class="stat-item">
      <div class="top">
        <span class="value">109.99</span>
      </div>
      <div class="bottom">
        <span class="description">已执行工期/天</span>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { defineComponent } from 'vue';
</script>

<style scoped>
.stats-display {
  display: flex;
  flex-direction: column;
  gap: 10px;
  background-color: #1d274b;
  padding: 10px;
  border-radius: 8px;
  color: #00eaff;
  height: 100%;
}

.stat-item {
  background-color: #162e5a;
  padding: 10px;
  border-radius: 4px;
  border: 1px dashed #00eaff;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.top,
.bottom {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
}

.sub-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 5px;
}

.value {
  font-weight: bold;
  color: #ffffff;
}

.description {
  font-size: 0.9em;
  color: #b0c4de;
}
</style>
