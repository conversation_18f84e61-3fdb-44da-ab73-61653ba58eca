<template>
    <div class="gantt-chart-container">
      <h2 class="gantt-title">大修项目甘特图</h2>
      <div class="year-label">2024年度</div>
      <div class="table-container">
        <table class="gantt-table">
          <thead>
            <tr class="year-header">
              <th class="category-cell"></th>
              <th>2024年度</th>
            </tr>
            <tr class="timeline-header">
              <th class="category-cell">类别</th>
              <th v-for="(label, index) in timeLabels" :key="index" class="time-cell">
                {{ label }}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="item in dataSource" :key="item.key" class="data-row">
              <td class="category-cell">{{ item.category }}</td>
              <td v-for="(label, index) in timeLabels" :key="index" class="timeline-cell">
                <div class="cell-container">
                  <!-- 如果该字段有数据则显示节点 -->
                  <div v-if="hasPointForLabel(item.points, index)" class="time-point">
                    <div class="point-marker" :style="getPointMarkerStyle(item.color)">
                      {{ getPointValueForLabel(item.points, index) }}
                    </div>
                    
                    <!-- 第一个数据点 - 只有右延伸线 -->
                    <div 
                      v-if="isFirstPoint(item.points, index)" 
                      class="right-extend-line" 
                      :style="getConnectorStyle(item.color)">
                    </div>
                    
                    <!-- 中间数据点 - 左右延伸线 -->
                    <div v-if="isMiddlePoint(item.points, index)" class="middle-line-container">
                      <div class="left-extend" :style="getConnectorStyle(item.color)"></div>
                      <div class="right-extend" :style="getConnectorStyle(item.color)"></div>
                    </div>
                    
                    <!-- 最后一个数据点 - 只有左延伸线 -->
                    <div 
                      v-if="isLastPoint(item.points, index)" 
                      class="left-extend-line" 
                      :style="getConnectorStyle(item.color)">
                    </div>
                  </div>
                  
                  <!-- 连接线和箭头 - 如果当前单元格有数据点且下一个单元格也有数据点，则显示连接线和箭头 -->
                  <div 
                    v-if="hasPointForLabel(item.points, index) && getNextPointIndex(item.points, index) !== -1" 
                    class="connector-line" 
                    :style="getConnectorStyle(item.color, getNextPointIndex(item.points, index) - index)">
                    <div class="timeline-arrow" :style="getArrowStyle(item.color)"></div>
                  </div>
                  
                  <!-- 如果当前格没有点，但是前后都有点，显示通过线 -->
                  <div 
                    v-if="!hasPointForLabel(item.points, index) && 
                        isPointAnyBefore(item.points, index) && 
                        isPointAnyAfter(item.points, index)" 
                    class="pass-through-line" 
                    :style="getConnectorStyle(item.color)">
                  </div>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </template>
  
  <script setup>
  import { ref, computed } from "vue";
  
  // 时间标签
  const timeLabels = [
    '-24M', '-22M', '-20M', '-14M', '-12M', '-11M', '-10M', '-9M', 
    '-8M', '-7M', '-6M', '-5M', '-4M', '-3M', '-2M', '-1M', 
    '-2W', '-3D', '-1D'
  ];
  
  // 表格列定义
  const columns = [
    {
      title: '类别',
      dataIndex: 'category',
      key: 'category',
    
    },
    {
      title: '时间线',
      dataIndex: 'timeline',
      key: 'timeline'
    }
  ];
  
  // 统一的数据源格式
  const dataSource = [
    {
      key: '1',
      category: '组织管理',
      color: '#00ccff',
      points: {
        '-20M': 4,
        '-11M': 1,  
        '-5M': 1,
        '-3M': 1,
        '-1M': 4,
        '-1D': 1
      }
    },
    {
      key: '2',
      category: '项目',
      color: '#00ff91',
      points: {
        '-14M': 1,
        '-9M': 1,
        '-6M': 1,
        '-2M': 1,
        '-3D': 1
      }
    },
    {
      key: '3',
      category: '物资',
      color: '#ffcc00',
      points: {
        '-24M': 12,
        '-12M': 122,
        '-7M': 1,
        '-4M': 1
      }
    },
    {
      key: '4',
      category: '合同',
      color: '#ca3cff',
      points: {
        '-22M': 12,
        '-10M': 121,
        '-6M': 1,
        '-2M': 1,
        '-2W': 1,
        '-1D': 4
      }
    },
    {
      key: '5',
      category: '工具箱',
      color: '#ff944d',
      points: {
        '-5M': 6,
        '-3M': 1,
        '-1M': 1,
        '-3D': 1
      }
    },
    {
      key: '6',
      category: '计划',
      color: '#00ccff',
      points: {
        '-9M': 1,
        '-7M': 1,
        '-5M': 1,
        '-2M': 1,
        '-1D': 1
      }
    },
    {
      key: '7',
      category: '文件',
      color: '#ff66cc',
      points: {
        '-11M': 2,
        '-4M': 1,
        '-2W': 5
      }
    }
  ];
  
  // 检查某个标签是否有对应的数据点
  const hasPointForLabel = (points, index) => {
    if (index < 0 || index >= timeLabels.length) return false;
    const label = timeLabels[index];
    return points[label] !== undefined;
  };
  
  // 获取某个标签对应的数据点值
  const getPointValueForLabel = (points, index) => {
    const label = timeLabels[index];
    return points[label];
  };
  
  // 检查特定索引是否有数据点
  const isPointAtIndex = (points, index) => {
    if (index < 0 || index >= timeLabels.length) return false;
    const label = timeLabels[index];
    return points[label] !== undefined;
  };
  
  // 检查索引之前是否有任何数据点
  const isPointAnyBefore = (points, index) => {
    for (let i = 0; i < index; i++) {
      if (isPointAtIndex(points, i)) {
        return true;
      }
    }
    return false;
  };
  
  // 检查索引之后是否有任何数据点
  const isPointAnyAfter = (points, index) => {
    for (let i = index + 1; i < timeLabels.length; i++) {
      if (isPointAtIndex(points, i)) {
        return true;
      }
    }
    return false;
  };
  
  // 检查是否是该数据行的第一个数据点
  const isFirstPoint = (points, index) => {
    if (!hasPointForLabel(points, index)) return false;
    
    // 检查是否有更早的数据点
    for (let i = 0; i < index; i++) {
      if (isPointAtIndex(points, i)) {
        return false;
      }
    }
    return true;
  };
  
  // 检查是否是该数据行的最后一个数据点
  const isLastPoint = (points, index) => {
    if (!hasPointForLabel(points, index)) return false;
    
    // 检查是否有更晚的数据点
    for (let i = index + 1; i < timeLabels.length; i++) {
      if (isPointAtIndex(points, i)) {
        return false;
      }
    }
    return true;
  };
  
  // 检查是否是该数据行的中间数据点(既不是第一个也不是最后一个)
  const isMiddlePoint = (points, index) => {
    return hasPointForLabel(points, index) && !isFirstPoint(points, index) && !isLastPoint(points, index);
  };
  
  // 获取节点标记样式
  const getPointMarkerStyle = (color) => {
    return {
      backgroundColor: color,
      boxShadow: `0 0 10px ${color}`
    };
  };
  
  // 获取连接线样式
  const getConnectorStyle = (color, span = 1) => {
    // 如果间隔超过1个单元格，则调整宽度
    const width = span > 1 ? `calc(${span * 100}% - 12px)` : 'calc(100% - 12px)';
    
    return {
      backgroundColor: color,
      boxShadow: `0 0 5px ${color}`,
      width: width
    };
  };
  
  // 获取箭头样式
  const getArrowStyle = (color) => {
    return {
      borderLeftColor: color
    };
  };
  
  // 获取下一个数据点的索引
  const getNextPointIndex = (points, currentIndex) => {
    for (let i = currentIndex + 1; i < timeLabels.length; i++) {
      if (isPointAtIndex(points, i)) {
        return i;
      }
    }
    return -1; // 没有找到下一个点
  };
  </script>
  
  <style scoped>
  .gantt-chart-container {
    padding: 0;
    border-radius: 0;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
    width: 100%;
    height: 100%;
  }
  
  .gantt-title {
    font-size: 22px;
    color: #ffffff;
    margin: 20px 20px 5px 20px;
    font-weight: 500;
  }
  
  .year-label {
    font-size: 16px;
    color: #9ce5ff;
    margin: 0 20px 15px 20px;
    font-weight: 400;
  }
  
  .table-container {
    position: relative;
    width: 100%;
    overflow: auto;
  }
  
  .gantt-table {
    width: 100%;
    border-collapse: collapse;
    background: #15193a;
    color: white;
    table-layout: fixed;
    border-spacing: 0;
  }
  
  /* 表头样式 */
  .timeline-header {
    height: 40px;
    border-bottom: 1px solid #11457b;
  }
  
  .category-cell {
    width: 50px;
    background-color: #141b36;
    text-align: center;
    padding: 10px;
    border-right: 2px solid #232b4d;
    vertical-align: middle;
    color: white;
    font-weight: bold;
  }
  
  .time-cell {
    background-color: transparent;
    text-align: center;
    color: #9ce5ff;
    font-size: 12px;
    padding: 8px 4px;
    width: 50px;
    /* border-right: 1px solid rgba(78, 160, 212, 0.15); */
    /* 移除底部边框 */
    border-bottom: none;
  }
  
  /* 数据行样式 */
  .data-row {
    height: 40px;
  }
  
  /* 时间单元格 */
  .timeline-cell {
    padding: 0;
    background-color: #192347;
    position: relative;
    width: 50px;
    height: 40px;
    border-right: 1px solid rgba(78, 160, 212, 0.15);
    border-bottom: 1px solid rgba(78, 160, 212, 0.15);
  }
  
  /* 单元格容器 */
  .cell-container {
    position: relative;
    width: 100%;
    height: 100%;
  }
  
  /* 连接线样式 */
  .connector-line {
    position: absolute;
    top: 50%;
    left: 50%; /* 从圆点半径位置开始(24px/2) */
    width: calc(100% - 12px); /* 减去起始偏移量 */
    height: 2px;
    background-color: #00ccff;
    transform: translateY(-50%);
    box-shadow: 0 0 5px rgba(0, 204, 255, 0.5);
    z-index: 1;
  }
  
  /* 通过线样式 */
  .pass-through-line {
    position: absolute;
    top: 50%;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: #00ccff;
    transform: translateY(-50%);
    box-shadow: 0 0 5px rgba(0, 204, 255, 0.5);
    opacity: 0.6;
    z-index: 1;
  }
  
  /* 时间线箭头 */
  .timeline-arrow {
    position: absolute;
    top: 50%;
    left: 50%; /* 箭头位置调整到线的中间 */
    transform: translate(-50%, -50%); /* 居中对齐 */
    width: 0;
    height: 0;
    border-top: 6px solid transparent;
    border-bottom: 6px solid transparent;
    border-left: 10px solid #00ccff;
    filter: drop-shadow(0 0 2px rgba(0, 204, 255, 0.5));
    animation: arrowPulse 1.5s infinite;
    z-index: 2;
  }
  
  /* 第一个点的右延伸线 */
  .right-extend-line {
    position: absolute;
    top: 50%;
    left: 100%;
    width: 25px; /* 延伸线宽度 */
    height: 2px;
    transform: translateY(-50%);
    z-index: 1;
  }
  
  /* 中间点的线容器 */
  .middle-line-container {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 2;
  }
  
  /* 左侧延伸 */
  .left-extend {
    position: absolute;
    top: 50%;
    right: 100%;
    width: 25px;
    height: 2px;
    transform: translateY(-50%);
  }
  
  /* 右侧延伸 */
  .right-extend {
    position: absolute;
    top: 50%;
    left: 100%;
    width: 25px;
    height: 2px;
    transform: translateY(-50%);
  }
  .year-cell{
    width: 120px;
  }
  /* 最后一个点的左延伸线 */
  .left-extend-line {
    position: absolute;
    top: 50%;
    right: 100%;
    width: 25px; /* 延伸线宽度 */
    height: 2px;
    transform: translateY(-50%);
    z-index: 1;
  }
  
  @keyframes arrowPulse {
    0% {
      opacity: 0.7;
      transform: translate(-50%, -50%) scale(0.9);
    }
    50% {
      opacity: 1;
      transform: translate(-50%, -50%) scale(1.1);
    }
    100% {
      opacity: 0.7;
      transform: translate(-50%, -50%) scale(0.9);
    }
  }
  
  /* 时间点样式 */
  .time-point {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 24px;
    height: 24px;
    z-index: 3;
  }
  
  .point-marker {
    width: 100%;
    height: 100%;
    background-color: #00ccff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 12px;
    box-shadow: 0 0 10px rgba(0, 204, 255, 0.7);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
  }
  
  .point-marker:hover {
    transform: scale(1.2);
    box-shadow: 0 0 15px rgba(0, 204, 255, 0.9);
    cursor: pointer;
    z-index: 10;
  }
  
  /* 中间点的左右延伸线 */
  .middle-extend-lines {
    position: absolute;
    width: 100%;
    height: 100%;
  }
  
  .middle-extend-lines::before, 
  .middle-extend-lines::after {
    content: "";
    position: absolute;
    top: 50%;
    height: 2px;
    transform: translateY(-50%);
    z-index: 1;
    background-color: inherit; /* 继承父元素的背景色 */
  }
  
  /* 左延伸线 */
  .middle-extend-lines::before {
    right: 50%;
    width: 25px; /* 左延伸线宽度 */
  }
  
  /* 右延伸线 */
  .middle-extend-lines::after {
    left: 50%;
    width: 25px; /* 右延伸线宽度 */
  }
  </style>