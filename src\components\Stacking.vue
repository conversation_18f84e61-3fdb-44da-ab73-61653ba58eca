<template>
  <div>1
    <!-- <div class="radioBox">
      <a-radio-group v-model:value="value" name="radioGroup">
        <a-radio
          v-for="item in RadioList"
          :key="item.value"
          :value="item.value"
        >
          <span style="color: #fff">{{ item.label }}</span>
        </a-radio>
      </a-radio-group>
    </div> -->
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
const RadioList = reactive([
  { label: "A", value: "1" },
  { label: "B", value: "2" },
  { label: "C", value: "3" },
  { label: "已完成", value: "4" },
  { label: "实施中", value: "5" },
  { label: "待施思", value: "6" },
]);

const value = ref("1");
</script>

<style scoped lang="scss">
.radioBox {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
