<template>
    <div class="gantt-container">
      <h2 class="gantt-title">大修项目甘特图</h2>
      <div class="table-container">
        <a-table
          :columns="columns"
          :data-source="data"
          :pagination="false"
          bordered
          size="middle"
          class="gantt-table"
          :custom-row="customRowClass"
        >
          <!-- 自定义列渲染 -->
          <template #bodyCell="{ column, record }">
            <!-- 渲染甘特图区域 -->
            <template v-if="column.dataIndex === 'gantt'">
              <div class="gantt-row">
                <div class="gantt-timeline">
                  <!-- 月份分隔线 -->
                  <div v-for="(month, index) in months" :key="`divider-${index}`" class="month-divider" :style="{ left: `${(index + 1) * (100/12)}%` }"></div>
                  
                  <!-- 甘特图条 -->
                  <div
                    v-if="shouldRenderBar(record)"
                    class="gantt-bar"
                    :style="getBarStyleNew(record)"
                  >
                    <span v-if="record.duration" class="bar-text">
                      {{ record.duration }}
                    </span>
                  </div>
                  
                  <!-- 超期/提前指示器，只对已完成项目（status=1）显示 -->
                  <div
                    v-if="record.status === 1 && record.num !== undefined"
                    class="delay-indicator"
                    :style="getDelayStyle(record)"
                  >
                    <span v-if="record.num" class="delay-text">
                      {{ record.num > 0 ? '+' + record.num + '天' : '-' + Math.abs(record.num) + '天' }}
                    </span>
                  </div>
                </div>
              </div>
            </template>
          </template>
  
          <!-- 自定义表头 -->
          <template #headerCell="{ column }">
            <template v-if="column.dataIndex === 'gantt'">
              <div class="gantt-header" id="gantt-header">
                <div class="month-row">
                  <div
                    v-for="(month, index) in months"
                    :key="index"
                    class="month-cell"
                  >
                    {{ month }}
                  </div>
                </div>
                <div class="repair-row">
                  <div
                    v-for="(count, index) in repairCounts"
                    :key="index"
                    class="repair-cell"
                  >
                    {{ count }}
                  </div>
                </div>
                <!-- 添加连贯的日期指示线 -->
                <div
                  class="continuous-today-line"
                  :style="todayLineStyle"
                  v-if="showTodayLine"
                ></div>
              </div>
            </template>
            <template v-else>
              {{ column.title }}
            </template>
          </template>
        </a-table>
      </div>
    </div>
  </template>
  
  <script setup>
  import { ref, computed } from "vue";
  
  // 定义月份
  const months = [
    "1月",
    "2月",
    "3月",
    "4月",
    "5月",
    "6月",
    "7月",
    "8月",
    "9月",
    "10月",
    "11月",
    "12月",
  ];
  
  // 生成大修次数数据（示例数据）
  const repairCounts = [
    "3次",
    "2次",
    "4次",
    "1次",
    "2次",
    "5次",
    "2次",
    "3次",
    "4次",
    "2次",
    "1次",
    "3次",
  ];
  
  // 自定义行属性，禁用行的hover状态
  const customRowClass = () => {
    return {
      class: 'no-hover-effect',
    };
  };
  
  // 基本列定义
  const baseColumns = [
    {
      title: "大修编号",
      dataIndex: "id",
      key: "id",
      width: 120,
      align: "center",
    },
    {
      title: "类型",
      dataIndex: "type",
      key: "type",
      width: 100,
      align: "center",
    },
    {
      title: "计划开始时间",
      dataIndex: "startDate",
      key: "startDate",
      width: 140,
      align: "center",
    },
    {
      title: "计划结束时间",
      dataIndex: "endDate",
      key: "endDate",
      width: 140,
      align: "center",
    },
    {
      title: "计划工期",
      dataIndex: "duration",
      key: "duration",
      width: 100,
      align: "center",
    },
  ];
  
  // 甘特图列 - 一个大列包含所有月份
  const ganttColumn = {
    title: "", // 使用自定义表头
    dataIndex: "gantt",
    key: "gantt",
    width: 800,
  };
  
  // 合并所有列
  const columns = [...baseColumns, ganttColumn];
  
  // 模拟数据
  const data = [
    {
      key: "1",
      id: "JH2023-001",
      title: "1号机组A级检修",
      type: "A级",
      startDate: "2023-03-10",
      endDate: "2023-05-20",
      duration: "70天",
      startMonth: 3,
      endMonth: 5,
      startOffset: 10,
      endOffset: 20,
      color: "#4285F4",
      status: 1, // 完整色块
      num: 10 // 超期10%
    },
    {
      key: "2",
      id: "JH2023-002",
      title: "2号机组B级检修",
      type: "B级",
      startDate: "2023-05-15",
      endDate: "2023-07-05",
      duration: "50天",
      startMonth: 5,
      endMonth: 7,
      startOffset: 15,
      endOffset: 5,
      color: "#EA4335",
      status: 1, // 完整色块
      num: -5 // 提前5%
    },
    {
      key: "3",
      id: "JH2023-003",
      title: "3号机组C级检修",
      type: "C级",
      startDate: "2023-06-08",
      endDate: "2023-07-28",
      duration: "50天",
      startMonth: 6,
      endMonth: 7,
      startOffset: 8,
      endOffset: 28,
      color: "#FBBC05",
      status: 0.5 // 50%进度
    },
    {
      key: "4",
      id: "JH2023-004",
      title: "4号机组A级检修",
      type: "A级",
      startDate: "2023-08-01",
      endDate: "2023-10-10",
      duration: "70天",
      startMonth: 8,
      endMonth: 10,
      startOffset: 1,
      endOffset: 10,
      color: "#34A853",
      status: 0.3 // 30%进度
    },
    {
      key: "5",
      id: "JH2023-005",
      title: "5号机组B级检修",
      type: "B级",
      startDate: "2023-09-10",
      endDate: "2023-11-20",
      duration: "70天",
      startMonth: 9,
      endMonth: 11,
      startOffset: 10,
      endOffset: 20,
      color: "#673AB7",
      status: 1, // 完整色块
      num: 15 // 超期15%
    },
  ];
  
  // 判断是否应该渲染甘特图条
  const shouldRenderBar = (record) => {
    return record.startMonth && record.endMonth;
  };
  
  // 新的获取条形样式方法，将甘特图整体计算
  const getBarStyleNew = (record) => {
    // 根据类型设置颜色
    let color;
    switch (record.type) {
      case "A级":
        color = "#1e88e5";
        break;
      case "B级":
        color = "#4caf50";
        break;
      case "C级":
        color = "#ffc107";
        break;
      default:
        color = "#673AB7"; // 默认颜色
    }
    
    // 计算在整个时间线中的位置百分比
    // 一年总共12个月，每个月按30天计算
    const totalDays = 12 * 30;
    
    // 计算起始位置（距离左边界的百分比）
    const startDayOfYear = (record.startMonth - 1) * 30 + record.startOffset;
    const leftPercent = (startDayOfYear / totalDays) * 100;
    
    // 计算结束位置
    const endDayOfYear = (record.endMonth - 1) * 30 + record.endOffset;
    
    // 计算宽度百分比
    const durationDays = endDayOfYear - startDayOfYear;
    const widthPercent = (durationDays / totalDays) * 100;
    
    let style = {
      left: `${leftPercent}%`,
      width: `${widthPercent}%`,
    };
    
    // 根据status设置背景样式
    if (record.status === 1) {
      // 完整显示色块
      style.backgroundColor = color;
    } else {
      // 按照比例显示色块，使用线性渐变
      const percentage = (record.status * 100) || 0;
      style.background = `linear-gradient(to right, ${color} ${percentage}%, rgba(255,255,255,0.3) ${percentage}%)`;
      // 为status不为1的添加虚线边框
      style.border = `1px dashed ${color}`;
    }
    
    return style;
  };
  
  // 计算今日日期线的位置
  const showTodayLine = computed(() => {
    const today = new Date();
    const currentYear = today.getFullYear();
    const currentMonth = today.getMonth() + 1; // JavaScript 月份是0-11
  
    // 检查当前月份是否在甘特图范围内
    return currentMonth >= 1 && currentMonth <= 12;
  });
  
  const todayLineStyle = computed(() => {
    const today = new Date();
    const currentMonth = today.getMonth() + 1; // JavaScript 月份是0-11
    const currentDate = today.getDate();
  
    // 找到当前月份在甘特图中的位置
    const monthIndex = currentMonth - 1;
  
    // 计算当前月的单元格宽度比例（1/12 = 8.333%）
    const cellWidth = 100 / 12;
  
    // 计算当天在本月的位置比例
    const daysInMonth = new Date(today.getFullYear(), currentMonth, 0).getDate();
    const dayRatio = currentDate / daysInMonth;
  
    // 计算最终位置：月份起始位置 + 月内的偏移
    const leftPosition = cellWidth * monthIndex + cellWidth * dayRatio;
  
    return {
      left: `${leftPosition}%`,
    };
  });
  
  // 获取延期/提前指示器的样式
  const getDelayStyle = (record) => {
    if (record.status !== 1 || record.num === undefined) return {};
    
    // 计算在整个时间线中的甘特图条位置
    const totalDays = 12 * 30;
    const startDayOfYear = (record.startMonth - 1) * 30 + record.startOffset;
    const endDayOfYear = (record.endMonth - 1) * 30 + record.endOffset;
    const durationDays = endDayOfYear - startDayOfYear;
    
    // 甘特图条的宽度和位置
    const widthPercent = (durationDays / totalDays) * 100;
    const leftPercent = (startDayOfYear / totalDays) * 100;
    
    // 将天数转换为百分比 (相对于365天)
    const daysInYear = 365;
    const dayPercentage = (Math.abs(record.num) / daysInYear) * 100;
    
    // 指示器的位置，无论是超期还是提前，都显示在右侧
    const delayLeft = leftPercent + widthPercent;
    
    // 设置渐变色
    const isDelay = record.num > 0; // 正数表示超期，负数表示提前
    const gradientColor = isDelay
      ? 'linear-gradient(to right, rgba(255,0,0,0.3), rgba(255,0,0,0.7))' // 超期：红色
      : 'linear-gradient(to right, rgba(0,150,0,0.3), rgba(0,150,0,0.7))'; // 提前：绿色
    
    return {
      left: `${delayLeft}%`,
      width: `${dayPercentage}%`,
      background: gradientColor,
    };
  };
  </script>
  
  <style scoped>
  .gantt-container {
    padding: 0;
    background-color: #f7f9fc;
    border-radius: 0;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  }
  
  .gantt-title {
    font-size: 22px;
    color: #333;
    margin: 20px;
    font-weight: 500;
  }
  
  .table-container {
    position: relative; /* 为连贯日期线提供定位上下文 */
  }
  
  .gantt-table {
    background: white;
    overflow: hidden;
  }
  
  /* 甘特图自定义表头 */
  .gantt-header {
    width: 100%;
    height: 100%;
    position: relative;
  }
  
  /* 月份行样式 */
  .month-row {
    display: flex;
    width: 100%;
    background-color: #0f5391;
    height: 32px;
    position: relative;
  }
  
  .month-cell {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #0ad0fd;
    font-weight: 500;
  }
  
  /* 大修次数行样式 */
  .repair-row {
    display: flex;
    width: 100%;
    background-color: #1a63a0;
    height: 24px;
    position: relative;
  }
  
  .repair-cell {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 12px;
  }
  
  /* 连贯的今日指示线 */
  .continuous-today-line {
    position: absolute;
    top: 56px; /* 56px是月份行(32px)和大修次数行(24px)的总高度 */
    bottom: -1000px; /* 设置一个足够大的值确保线延伸到整个表格 */
    width: 0;
    border-left: 2px dashed #ff4d4f; /* 红色虚线 */
    z-index: 999; /* 确保在最上层 */
    pointer-events: none; /* 确保不影响鼠标事件 */
  }
  
  /* 甘特图行样式 */
  .gantt-row {
    display: flex;
    width: 100%;
    height: 25px;
    position: relative;
    border: none !important;
  }
  
  .gantt-cell {
    flex: 1;
    height: 100%;
    position: relative;
    border: none !important;
  }
  
  .gantt-cell:last-child {
    border-right: none;
  }
  
  /* 甘特图条形样式 */
  .gantt-bar {
    position: absolute;
    top: 0;
    left: 0;
    height: 25px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 12px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    box-shadow: none;
    z-index: 1; /* 确保条形位于上层，但低于今日线 */
  }
  
  /* 调整首尾样式 */
  .bar-start {
    border-radius: 0;
    padding-left: 8px;
    justify-content: center;
  }
  
  .bar-middle {
    border-radius: 0;
    justify-content: center;
  }
  
  .bar-end {
    border-radius: 0;
    padding-right: 8px;
    justify-content: center;
  }
  
  .bar-full {
    border-radius: 0;
    padding: 0 8px;
    justify-content: center;
  }
  
  .bar-text {
    white-space: nowrap;
    overflow: visible;
    text-overflow: clip;
    font-weight: 500;
    text-align: center;
    width: auto;
    padding: 0 4px;
  }
  
  /* 禁用表格行悬停效果的自定义类 */
  :deep(.no-hover-effect) {
    background-color: inherit !important;
  }
  
  :deep(.no-hover-effect:hover) {
    background-color: inherit !important;
  }
  
  /* 禁用表格悬停引起的闪烁问题 */
  :deep(.ant-table-tbody > tr > td) {
    padding: 0;
    margin: 0;
    border: none !important; /* 移除所有边框 */
    height: 25px;
    color: white;
    transition: none !important; /* 禁用所有过渡动画 */
    animation: none !important; /* 禁用所有动画 */
  }
  
  /* 完全禁用所有hover效果 */
  :deep(.ant-table-tbody > tr:hover > td),
  :deep(.ant-table-tbody > tr:hover),
  :deep(.ant-table-tbody > tr.ant-table-row:hover > td),
  :deep(.ant-table-tbody > tr > td.ant-table-cell-hover),
  :deep(.ant-table-tbody > tr.ant-table-row-hover),
  :deep(.ant-table-row:hover),
  :deep(.ant-table-row-hover),
  :deep(.ant-table-cell-hover),
  :deep(.ant-table-tbody > tr.ant-table-row:hover),
  :deep(.ant-table-tbody > tr:hover td),
  :deep(.ant-table-tbody > tr.ant-table-row:focus),
  :deep(.ant-table-tbody > tr.ant-table-row:active),
  :deep(.ant-table-cell:focus),
  :deep(.ant-table-cell:active) {
    background-color: initial !important;
    background: none !important;
    transition: none !important;
    animation: none !important;
  }
  
  /* 强制斑马线样式 */
  :deep(.ant-table-tbody > tr:nth-child(odd)),
  :deep(.ant-table-tbody > tr:nth-child(odd)):hover,
  :deep(.ant-table-tbody > tr:nth-child(odd) td):hover,
  :deep(.ant-table-tbody > tr:nth-child(odd).ant-table-row):hover,
  :deep(.ant-table-tbody > tr:nth-child(odd) > td),
  :deep(.ant-table-tbody > tr:nth-child(odd) > td:hover) {
    background-color: #1d274b !important;
  }
  
  :deep(.ant-table-tbody > tr:nth-child(even)),
  :deep(.ant-table-tbody > tr:nth-child(even)):hover,
  :deep(.ant-table-tbody > tr:nth-child(even) td):hover,
  :deep(.ant-table-tbody > tr:nth-child(even).ant-table-row):hover,
  :deep(.ant-table-tbody > tr:nth-child(even) > td),
  :deep(.ant-table-tbody > tr:nth-child(even) > td:hover) {
    background-color: #162e5a !important;
  }
  
  /* 覆盖所有Ant Design CSS变量 */
  :deep(.ant-table) {
    --ant-table-row-hover-bg: unset !important;
    --ant-table-cell-hover-bg: unset !important;
  }
  
  :root {
    --ant-table-row-hover-bg: unset !important;
    --ant-table-cell-hover-bg: unset !important;
  }
  
  /* 表格样式调整 */
  :deep(.ant-table-tbody > tr > td) {
    padding: 0;
    margin: 0;
    border: none !important; /* 移除所有边框 */
    height: 25px;
    color: white;
  }
  
  /* 设置表格行高 */
  :deep(.ant-table-tbody > tr) {
    height: 25px;
    line-height: 25px;
  }
  
  /* 设置表格斑马条纹 */
  :deep(.ant-table-tbody > tr:nth-child(odd)),
  :deep(.ant-table-tbody > tr:nth-child(odd):hover),
  :deep(.ant-table-tbody > tr:nth-child(odd) > td:hover),
  :deep(.ant-table-tbody > tr:nth-child(odd).ant-table-row:hover),
  :deep(.ant-table-tbody > tr:nth-child(odd).ant-table-row-hover) {
    background-color: #1d274b !important;
  }
  
  :deep(.ant-table-tbody > tr:nth-child(even)),
  :deep(.ant-table-tbody > tr:nth-child(even):hover),
  :deep(.ant-table-tbody > tr:nth-child(even) > td:hover),
  :deep(.ant-table-tbody > tr:nth-child(even).ant-table-row:hover),
  :deep(.ant-table-tbody > tr:nth-child(even).ant-table-row-hover) {
    background-color: #162e5a !important;
  }
  
  /* 表头与表体之间的分隔线也可以移除 */
  :deep(.ant-table-thead > tr:last-child > th) {
    border-bottom: none !important; /* 移除底部边框 */
  }
  
  /* 移除表格所有边框 */
  :deep(.ant-table-container) {
    border: none !important;
  }
  
  :deep(.ant-table) {
    border: none !important;
  }
  
  :deep(.ant-table-container table) {
    border: none !important;
    border-collapse: collapse;
  }
  
  /* 基本信息列样式 */
  :deep(.ant-table-thead > tr > th) {
    background-color: #0f5391;
    color: #0ad0fd;
    font-weight: 500;
    padding: 8px 16px;
    border: none !important; /* 移除所有边框 */
  }
  
  /* 隐藏表头分割线 */
  :deep(.ant-table-thead > tr > th::before) {
    display: none !important; /* 完全隐藏伪元素分隔线 */
  }
  
  /* 表头单元格之间的间隔处理 */
  :deep(.ant-table-container table > thead > tr:first-child th) {
    border-top: none;
  }
  
  :deep(.ant-table-container table > thead > tr th) {
    border-right: none;
    border-bottom: none;
  }
  
  /* 处理甘特图表头特殊样式 */
  :deep(.ant-table-thead > tr > th:last-child) {
    padding: 0 !important; /* 移除甘特图表头内边距 */
    overflow: hidden; /* 确保内容不溢出 */
    border: none !important;
  }
  
  /* 调整甘特图表头高度以匹配基本列表头 */
  :deep(.ant-table-thead > tr > th.ant-table-cell) {
    height: auto;
    padding: 4px;
  }
  
  /* 确保表格行之间没有间隙 */
  :deep(.ant-table) {
    border-collapse: collapse;
    padding: 0;
    margin: 0;
  }
  
  :deep(.ant-table-container) {
    padding: 0;
    margin: 0;
  }
  
  /* 设置最后一列甘特图区域的相对定位 */
  :deep(.ant-table-thead > tr > th:last-child),
  :deep(.ant-table-tbody > tr > td:last-child) {
    position: relative;
    overflow: visible;
    border: none !important; /* 确保甘特图区域没有边框 */
  }
  
  /* 移除表格边框和悬停效果 */
  :deep(.ant-table-wrapper) {
    border: none !important;
  }
  
  :deep(.ant-table),
  :deep(.ant-table-container),
  :deep(.ant-table-wrapper) {
    border-radius: 0 !important;
  }
  
  /* 阻止鼠标事件处理 */
  :deep(.ant-table) {
    pointer-events: auto !important;
  }
  
  /* 为基本信息列添加竖向边框 */
  :deep(.ant-table-tbody > tr > td:nth-child(-n+5)) {
    border-right: 1px solid #38618d !important;
  }
  
  /* 确保表头单元格没有边框 */
  :deep(.ant-table-thead > tr > th) {
    border: none !important;
  }
  
  /* 为计划工期表头添加右侧边框 */
  :deep(.ant-table-thead > tr > th:nth-child(5)) {
    border-right: 1px solid #4882c0 !important;
  }
  
  /* 时间线整体容器 */
  .gantt-timeline {
    width: 100%;
    height: 100%;
    position: relative;
  }
  
  /* 月份分隔线 */
  .month-divider {
    position: absolute;
    top: 0;
    height: 100%;
    width: 1px;
    /* background-color: rgba(255, 255, 255, 0.1); */
    z-index: 1;
    pointer-events: none;
  }
  
  /* 延期/提前指示器样式 */
  .delay-indicator {
    position: absolute;
    top: 0;
    height: 25px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 12px;
    white-space: nowrap;
    /* overflow: hidden; */
    text-overflow: ellipsis;
    z-index: 3; /* 确保在甘特图条之上 */
    padding: 0 4px;
  }
  
  .delay-text {
    white-space: nowrap;
    overflow: visible;
    text-overflow: clip;
    font-weight: 500;
    text-align: center;
    width: auto;
    text-shadow: 0 0 2px rgba(0, 0, 0, 0.5);
    padding: 0 2px;
  }
  </style>
  