<template>
  <div class="planned-work-section">
    <div class="section-title">
      <span class="title-icon"></span>
      机械明日计划开工工作
    </div>
    <div class="table-wrapper">
      <el-table
        :data="plannedWorkData"
        border
        stripe
        style="width: 100%"
        :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
      >
        <el-table-column prop="workOrderNo" label="工单号" width="120" />
        <el-table-column prop="taskNo" label="任务号" width="120" />
        <el-table-column prop="title" label="工单任务标题" min-width="200" />
        <el-table-column prop="status" label="工单任务状态" width="120" />
        <el-table-column prop="plannedStartDate" label="计划开工" width="120" />
        <el-table-column prop="plannedEndDate" label="计划完工" width="120" />
        <el-table-column prop="maintenanceLevel" label="维修分级" width="100" />
        <el-table-column prop="workType" label="作业类型" width="120" />
        <el-table-column
          prop="responsiblePerson"
          label="工作负责人"
          width="120"
        />
        <el-table-column prop="responsibleTeam" label="责任班组" width="120" />
        <el-table-column
          prop="preparationPerson"
          label="工作准备人"
          width="120"
        />
        <el-table-column prop="system" label="系统" width="120" />
      </el-table>
    </div>
  </div>
</template>

<script>
export default {
  name: "PlannedWorkTable",
  data() {
    return {
      plannedWorkData: [
        {
          workOrderNo: "WO-2023-0020",
          taskNo: "T-0052",
          title: "6号机组冷却水泵检修",
          status: "已批准",
          plannedStartDate: "2023-10-22",
          plannedEndDate: "2023-10-26",
          maintenanceLevel: "B级",
          workType: "定期维修",
          responsiblePerson: "刘工",
          responsibleTeam: "机械一班",
          preparationPerson: "陈工",
          system: "冷却水系统",
        },
        {
          workOrderNo: "WO-2023-0021",
          taskNo: "T-0053",
          title: "3号锅炉脱硫系统检修",
          status: "已批准",
          plannedStartDate: "2023-10-22",
          plannedEndDate: "2023-10-29",
          maintenanceLevel: "A级",
          workType: "预防性维修",
          responsiblePerson: "李工",
          responsibleTeam: "机械二班",
          preparationPerson: "张工",
          system: "脱硫系统",
        },
        {
          workOrderNo: "WO-2023-0022",
          taskNo: "T-0054",
          title: "4号机组高压加热器更换密封",
          status: "待批准",
          plannedStartDate: "2023-10-22",
          plannedEndDate: "2023-10-24",
          maintenanceLevel: "C级",
          workType: "故障性维修",
          responsiblePerson: "王工",
          responsibleTeam: "机械三班",
          preparationPerson: "孙工",
          system: "加热系统",
        },
        {
          workOrderNo: "WO-2023-0023",
          taskNo: "T-0055",
          title: "2号机组磨煤机检修",
          status: "已批准",
          plannedStartDate: "2023-10-22",
          plannedEndDate: "2023-10-30",
          maintenanceLevel: "B级",
          workType: "定期维修",
          responsiblePerson: "郑工",
          responsibleTeam: "机械二班",
          preparationPerson: "洪工",
          system: "煤粉系统",
        },
        {
          workOrderNo: "WO-2023-0024",
          taskNo: "T-0056",
          title: "1号机组循环泵轴承更换",
          status: "待批准",
          plannedStartDate: "2023-10-22",
          plannedEndDate: "2023-10-25",
          maintenanceLevel: "B级",
          workType: "故障性维修",
          responsiblePerson: "吴工",
          responsibleTeam: "机械一班",
          preparationPerson: "林工",
          system: "循环水系统",
        },
      ],
    };
  },
  methods: {},
};
</script>

<style scoped>
/* 计划开工工作区域样式 */
.planned-work-section {
  margin-bottom: 30px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  width: 100%;
}

.table-wrapper {
  padding: 15px;
  overflow-x: auto;
}

/* 标题样式 */
.section-title {
  padding: 15px 20px;
  font-size: 15px;
  color: #666;
  font-weight: 500;
  display: flex;
  align-items: center;
  background: linear-gradient(to right, #f8f9fc, #ffffff);
  border-left: 3px solid #1890ff;
  letter-spacing: 0.5px;
  margin-bottom: 0;
  box-shadow: 0 1px 0 rgba(0, 0, 0, 0.05);
}

.title-icon {
  display: inline-block;
  width: 4px;
  height: 16px;
  margin-right: 8px;
  background-color: #1890ff;
  border-radius: 2px;
}
</style>
