<template>
  <div style="width: 100%; height: 100%; background-color: #192347;padding: 20px;box-sizing: border-box;">
  <div class="area2-component-content">
    <div class="title">Q2-OT117 (大修经理: 刘冬生)</div>
    <div class="countdown">
      距离大修开始 <span class="days">222</span> 天
    </div>
    <div class="details-grid">
      <div class="detail-item">计划开始: 2025-05-05</div>
      <div class="detail-item">计划完成: 2025-06-05</div>
      <div class="detail-item">计划工期: 30天</div>
      <div class="detail-item">大修类型: C类</div>
    </div>
  </div>  </div>
</template>

<script setup>
// 区域2组件的特定逻辑可以放在这里
</script>

<style scoped>
.area2-component-content {
 background-color: #2c85c6;  /* 背景色根据用户要求设置 */
  color: white;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center; /* 居中标题和倒计时 */
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; /* 使用常见的无衬线字体 */
  border-radius: 5px; /* 轻微的圆角 */
}

.title {
  font-size: 28px; /* 增加字体大小 */
  font-weight: bold;
  text-align: center;
}

.countdown {
  font-size: 25px; /* 增加字体大小 */
  display: flex;
  align-items: baseline;
  text-align: center;
}

.countdown .days {
  font-size: 42px; /* 增加字体大小 */
  font-weight: bold;
  margin: 0 8px; /* 微调水平外边距 */
  color: #90ee90; /* 图片中的浅绿色 */
}

.details-grid {
  display: grid;
  grid-template-columns: auto auto; /* 列宽根据内容自适应 */
  gap: 8px 25px; /* 行间隙 列间隙 */
  width: 100%; /* 容器宽度根据内容自适应 */
  font-size: 14px; /* 详情文字大小 */
  font-weight: 400;
  justify-content: space-evenly;
}

.detail-item {
  text-align: left;
  white-space: nowrap; /* 防止文字换行 */
}
</style> 