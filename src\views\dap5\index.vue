<template>
  <div class="dap5-container">
    <!-- 查询表单区域 -->
    <div class="query-form-container">
      <a-form
        :model="queryForm"
        layout="inline"
        class="query-form"
      >
        <a-form-item label="基地" name="base">
          <a-input
            v-model:value="queryForm.base"
            placeholder="请输入基地名称"
            allow-clear
            style="width: 180px"
          />
        </a-form-item>

        <a-form-item label="大修" name="overhaul">
          <a-input
            v-model:value="queryForm.overhaul"
            placeholder="请输入大修编号"
            allow-clear
            style="width: 180px"
          />
        </a-form-item>

        <a-form-item label="专业" name="specialty">
          <a-input
            v-model:value="queryForm.specialty"
            placeholder="请输入专业名称"
            allow-clear
            style="width: 180px"
          />
        </a-form-item>

        <a-form-item label="处室" name="department">
          <a-input
            v-model:value="queryForm.department"
            placeholder="请输入处室名称"
            allow-clear
            style="width: 180px"
          />
        </a-form-item>

        <a-form-item label="科室" name="section">
          <a-input
            v-model:value="queryForm.section"
            placeholder="请输入科室名称"
            allow-clear
            style="width: 180px"
          />
        </a-form-item>

        <a-form-item label="班组" name="team">
          <a-input
            v-model:value="queryForm.team"
            placeholder="请输入班组名称"
            allow-clear
            style="width: 180px"
          />
        </a-form-item>

        <a-form-item label="准备人" name="preparer">
          <a-input
            v-model:value="queryForm.preparer"
            placeholder="请输入准备人姓名"
            allow-clear
            style="width: 180px"
          />
        </a-form-item>

        <a-form-item label="配置状态" name="configuration">
          <a-select
            v-model:value="queryForm.configuration"
            placeholder="请选择配置状态"
            allow-clear
            style="width: 180px"
          >
            <a-select-option value="配置已满足">✅ 配置已满足</a-select-option>
            <a-select-option value="配置未满足">❌ 配置未满足</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item>
          <a-space>
            <a-button @click="handleReset">重置</a-button>
            <a-button type="primary" @click="handleSearch">查询</a-button>
          </a-space>
        </a-form-item>
      </a-form>

      <!-- 分隔线 -->
      <div class="divider"></div>

      <!-- 操作按钮区域 -->
      <div class="operation-buttons">
        <a-button type="primary" @click="handleImport">
          <template #icon><UploadOutlined /></template>
          导入需求及配置
        </a-button>
        <a-button
          danger
          :disabled="!selectedRowKeys.length"
          @click="handleBatchDelete"
        >
          <template #icon><DeleteOutlined /></template>
          批量删除配置
        </a-button>
        <a-button @click="handleExport">
          <template #icon><DownloadOutlined /></template>
          导出查询结果
        </a-button>
      </div>
    </div>



    <!-- 表格区域 -->
    <div class="table-container">
      <a-table
        :columns="columns"
        :data-source="tableData"
        :pagination="pagination"
        :loading="loading"
        :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
        :scroll="{ x: 1500 }"
        bordered
        size="middle"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import { message } from "ant-design-vue";
import {
  UploadOutlined,
  DeleteOutlined,
  DownloadOutlined,
} from "@ant-design/icons-vue";

// 查询表单数据
const queryForm = reactive({
  base: undefined,
  overhaul: undefined,
  specialty: undefined,
  department: undefined,
  section: undefined,
  team: undefined,
  preparer: undefined,
  configuration: undefined,
});

// 表格列定义
const columns = [
  { title: "序号", dataIndex: "index", width: 60, fixed: "left" },
  { title: "大修编号", dataIndex: "overhaulNo", width: 120 },
  { title: "工单任务号", dataIndex: "taskNo", width: 120 },
  { title: "工单任务标题", dataIndex: "taskTitle", width: 200, ellipsis: true },
  { title: "工单任务状态", dataIndex: "taskStatus", width: 120 },
  { title: "专业", dataIndex: "specialty", width: 100 },
  { title: "责任班组", dataIndex: "responsibleTeam", width: 120 },
  { title: "所属合同", dataIndex: "contract", width: 120 },
  { title: "所属专项", dataIndex: "specialProject", width: 120 },
  {
    title: "配置需求",
    dataIndex: "configurationRequirement",
    width: 100,
    align: "right",
    customRender: ({ text }: { text: number }) => (text ? `${text}` : "0"),
  },
  {
    title: "已配置",
    dataIndex: "configured",
    width: 100,
    align: "right",
    customRender: ({ text }: { text: number }) => (text ? `${text}` : "0"),
  },
  {
    title: "缺口",
    dataIndex: "gap",
    width: 100,
    align: "right",
    customRender: ({ text }: { text: number | null | undefined }) => {
      if (text === undefined || text === null) return "0";
      const value = Number(text);
      return value < 0 ? `${value}` : `${value}`;
    },
  },
  {
    title: "计划开始时间",
    dataIndex: "planStartTime",
    width: 150,
    sorter: (a: any, b: any) =>
      new Date(a.planStartTime).getTime() - new Date(b.planStartTime).getTime(),
  },
  {
    title: "计划完成时间",
    dataIndex: "planEndTime",
    width: 150,
    sorter: (a: any, b: any) =>
      new Date(a.planEndTime).getTime() - new Date(b.planEndTime).getTime(),
  },
];

// 初始化模拟数据
const initTableData = () => {
  const data = [];
  for (let i = 0; i < 10; i++) {
    data.push({
      key: i.toString(),
      index: i + 1,
      overhaulNo: `OH-2023-${1000 + i}`,
      taskNo: `TK-2023-${2000 + i}`,
      taskTitle: `测试工单任务标题 ${i + 1}`,
      taskStatus: i % 3 === 0 ? "进行中" : i % 3 === 1 ? "已完成" : "待处理",
      specialty: `专业${(i % 3) + 1}`,
      responsibleTeam: `班组${(i % 4) + 1}`,
      contract: `合同${(i % 3) + 1}`,
      specialProject: `专项${(i % 2) + 1}`,
      configurationRequirement: Math.floor(Math.random() * 100) + 10,
      configured: Math.floor(Math.random() * 80) + 5,
      gap: Math.floor(Math.random() * 30) - 15,
      planStartTime: `2023-${Math.floor(i / 3) + 1}-${i + 1}`,
      planEndTime: `2023-${Math.floor(i / 3) + 2}-${i + 15}`,
    });
  }
  return data;
};

// 表格数据
const tableData = ref(initTableData());
const loading = ref(false);

// 选中行的key
const selectedRowKeys = ref<string[]>([]);

// 表格分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 10,
  showTotal: (total: number) => `共 ${total} 条记录`,
});

// 处理表格选择变化
const onSelectChange = (keys: string[]) => {
  selectedRowKeys.value = keys;
};

// 查询方法
const handleSearch = () => {
  loading.value = true;
  // 这里添加实际的查询逻辑
  console.log("查询条件:", queryForm);

  // 模拟异步查询
  setTimeout(() => {
    // 模拟数据
    tableData.value = initTableData();
    loading.value = false;
  }, 500);
};

// 重置表单
const handleReset = () => {
  // 重置表单字段
  Object.keys(queryForm).forEach((key) => {
    queryForm[key] = undefined;
  });
};

// 导入需求及配置
const handleImport = () => {
  message.info("导入需求及配置功能待实现");
  // 这里可以添加导入逻辑，如打开文件选择对话框等
};

// 批量删除配置
const handleBatchDelete = () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning("请先选择要删除的记录");
    return;
  }

  // 这里可以添加确认对话框
  message.success(`已删除 ${selectedRowKeys.value.length} 条记录`);
  // 模拟删除后的数据刷新
  tableData.value = tableData.value.filter(
    (item) => !selectedRowKeys.value.includes(item.key)
  );
  selectedRowKeys.value = [];
};

// 导出查询结果
const handleExport = () => {
  if (tableData.value.length === 0) {
    message.warning("暂无数据可导出");
    return;
  }

  message.success("导出成功");
  // 这里可以添加实际的导出逻辑
};
</script>

<style lang="scss" scoped>
.dap5-container {
  padding: 16px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

/* 查询表单容器 - 原生a-form样式 */
.query-form-container {
  background: #fff;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  margin-bottom: 5px;
  padding: 18px 20px;
  border: 1px solid #d9d9d9;
}

/* 查询表单样式 */
.query-form {
  margin-bottom: 0;
}

/* 分隔线样式 */
.divider {
  height: 1px;
  background-color: #f0f0f0;
  margin: 16px 0;
}

/* 操作按钮区域样式 */
.operation-buttons {
  display: flex;
  gap: 12px;
  align-items: center;
  padding-top: 0;
}

/* 表格容器 */
.table-container {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

/* 表格样式优化 */
:deep(.ant-table-thead > tr > th) {
  background: #fafbfc;
  font-weight: 600;
  color: #333;
}

:deep(.ant-table-tbody > tr:hover > td) {
  background: #f0f9ff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dap5-container {
    padding: 12px;
  }

  .query-form-container {
    padding: 16px;
  }

  .operation-buttons {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;

    .ant-btn {
      width: 100%;
      justify-content: center;
    }
  }

  .table-container {
    padding: 16px;
  }

  /* 移动端表单项换行 */
  :deep(.ant-form-item) {
    margin-right: 0 !important;
    margin-bottom: 12px !important;
    width: 100%;

    .ant-form-item-label {
      width: 80px;
      text-align: left;
    }

    .ant-form-item-control {
      flex: 1;

      .ant-input,
      .ant-select {
        width: 100% !important;
      }
    }
  }
}
</style>
