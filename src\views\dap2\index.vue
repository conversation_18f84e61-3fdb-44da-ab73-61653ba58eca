<template>
  <div class="parent">
    <div class="div1">顶部标题栏</div>
    <div class="div2">
      <MiddleContent />
    </div>
    <div class="div3"></div>
    <div class="div4">
      <HistoricalMaintenanceCard />
    </div>
    <div class="div5">
      <MaintenanceDeviationCard />
    </div>
    <div class="div6">
      <AnnualMaintenanceChart />
    </div>
    <div class="div7">
      <AnnualDurationChart />
    </div>
  </div>
</template>

<script setup lang="ts">
import MiddleContent from "./cms/MiddleContent.vue";
import AnnualMaintenanceChart from "./cms/AnnualMaintenanceChart.vue";
import AnnualDurationChart from "./cms/AnnualDurationChart.vue";
import MaintenanceDeviationCard from "./cms/MaintenanceDeviationCard.vue";
import HistoricalMaintenanceCard from "./cms/HistoricalMaintenanceCard.vue";
</script>

<style scoped>
.parent {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  grid-template-rows: 6% repeat(3, 1fr) 0.5fr 1fr 0.5fr 1fr;
  grid-column-gap: 10px;
  grid-row-gap: 10px;
  height: 100vh;
  padding: 10px;
  box-sizing: border-box;
  background-color: #030d3a;
}

.div1 {
  grid-area: 1 / 1 / 2 / 8;
  background-color: #d79e9e;
  border-radius: 4px;
}

.div2 {
  grid-area: 2 / 3 / 9 / 6;
  border-radius: 4px;
  overflow: hidden;
}

.div3 {
  grid-area: 2 / 1 / 5 / 3;
  background-color: #d79e9e;
  border-radius: 4px;
}

.div4 {
  grid-area: 7 / 1 / 9 / 3;
  background-color: #d79e9e;
  border-radius: 4px;
}

.div5 {
  grid-area: 5 / 1 / 7 / 3;
  background-color: #d79e9e;
  border-radius: 4px;
}

.div6 {
  grid-area: 2 / 6 / 5 / 8;
  border-radius: 4px;
  overflow: hidden;
}

.div7 {
  grid-area: 5 / 6 / 9 / 8;
  border-radius: 4px;
  overflow: hidden;
}
</style>
