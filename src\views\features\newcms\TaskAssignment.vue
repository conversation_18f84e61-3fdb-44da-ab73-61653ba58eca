<template>
  <div>
    <a-form :model="formState">
      <div class="form-row">
        <a-form-item label="编号">
          <a-input v-model:value="formState.id" placeholder="请输入编号" />
        </a-form-item>
        <a-form-item label="任务名称">
          <a-input
            v-model:value="formState.taskName"
            placeholder="请输入任务名称"
          />
        </a-form-item>
        <a-form-item label="标志物">
          <a-input
            v-model:value="formState.marker"
            placeholder="请输入标志物"
          />
        </a-form-item>
      </div>

      <div class="form-row">
        <a-form-item label="计划开始时间">
          <a-date-picker
            placeholder="请选择计划开始时间"
            v-model:value="formState.planStartTime"
            format="YYYY-MM-DD"
            valueFormat="YYYY-MM-DD"
            style="width: 100%"
          />
        </a-form-item>
        <a-form-item label="计划完成时间">
          <a-date-picker
            placeholder="请选择计划完成时间"
            format="YYYY-MM-DD"
            v-model:value="formState.planEndTime"
            style="width: 100%"
            valueFormat="YYYY-MM-DD"
          />
        </a-form-item>
        <a-form-item>
          <a-space>
            <a-button type="primary" @click="handleSearch">查询</a-button>
            <a-button @click="handleReset">重置</a-button>
          </a-space>
        </a-form-item>
      </div>
    </a-form>

    <a-space>
      <a-button type="primary" @click="handleAdd">新增</a-button>
      <a-button type="primary" danger @click="handleDelete">删除</a-button>
      <a-button type="primary" @click="handleEdit">修改</a-button>
      <a-button type="primary" @click="handleAssign">下达</a-button>
    </a-space>

    <a-table
      :columns="columns"
      :data-source="tableData"
      :row-selection="{
        selectedRowKeys: selectedRowKeys,
        onChange: onSelectChange,
        type: 'checkbox',
      }"
      :pagination="false"
      :row-key="(record) => record.id"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'operation'">
          <a-space>
            <a-button type="link" @click="handleTaskAssign(record)"
              >下达</a-button
            >
            <a-button type="link" @click="handleTaskRecall(record)"
              >召回</a-button
            >
          </a-space>
        </template>
      </template>
    </a-table>

    <!-- 引入任务表单组件 -->
    <TaskForm
      ref="taskFormRef"
      @submit="handleFormSubmit"
      @cancel="handleFormCancel"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import { message } from "ant-design-vue";
import TaskForm from "./components/TaskForm.vue";

// 表单数据
const formState = reactive({
  id: "",
  taskName: "",
  marker: "",
  planStartTime: null,
  planEndTime: null,
});

// 表格列定义
const columns = [
  {
    title: "责任部门",
    dataIndex: "department",
    key: "department",
  },
  {
    title: "科室",
    dataIndex: "office",
    key: "office",
  },
  {
    title: "协调人",
    dataIndex: "coordinator",
    key: "coordinator",
  },
  {
    title: "责任人",
    dataIndex: "responsible",
    key: "responsible",
  },
  {
    title: "处室责任人",
    dataIndex: "officeResponsible",
    key: "officeResponsible",
  },
  {
    title: "任务操作",
    key: "operation",
    width: 150,
  },
  {
    title: "偏差反馈",
    dataIndex: "feedback",
    key: "feedback",
  },
];

// 表格数据
const tableData = ref([
  {
    id: 1,
    department: "技术部",
    office: "研发科",
    coordinator: "张三",
    responsible: "李四",
    officeResponsible: "王五",
    feedback: "进度正常",
  },
  {
    id: 2,
    department: "运营部",
    office: "运维科",
    coordinator: "赵六",
    responsible: "钱七",
    officeResponsible: "孙八",
    feedback: "略有延迟",
  },
  {
    id: 3,
    department: "质量部",
    office: "测试科",
    coordinator: "周九",
    responsible: "吴十",
    officeResponsible: "郑十一",
    feedback: "需要协调",
  },
]);

// 选择框相关
const selectedRowKeys = ref([]);

// 表单引用
const taskFormRef = ref(null);

// 表格选择方法
const onSelectChange = (keys, selectedRows) => {
  selectedRowKeys.value = keys;
  console.log("selectedRowKeys: ", keys);
  console.log("selectedRows: ", selectedRows);
};

// 方法定义
const handleSearch = () => {
  console.log("搜索", formState);
};

const handleReset = () => {
  Object.keys(formState).forEach((key) => {
    formState[key] = "";
  });
};

// 处理新增
const handleAdd = () => {
  taskFormRef.value.show();
};

// 处理表单提交
const handleFormSubmit = (data) => {
  // 添加新数据
  const newId =
    tableData.value.length > 0
      ? Math.max(...tableData.value.map((item) => item.id)) + 1
      : 1;

  tableData.value.push({
    id: newId,
    ...data,
  });

  message.success("添加成功");
};

// 处理表单取消
const handleFormCancel = () => {
  console.log("表单取消");
};

// 处理删除
const handleDelete = () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning("请选择要删除的数据");
    return;
  }
  console.log("删除数据", selectedRowKeys.value);
  message.success("删除成功");
};

// 处理修改
const handleEdit = () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning("请选择要修改的数据");
    return;
  }
  if (selectedRowKeys.value.length > 1) {
    message.warning("只能选择一条数据进行修改");
    return;
  }

  // 获取选中的数据
  const selectedData = tableData.value.find(
    (item) => item.id === selectedRowKeys.value[0]
  );
  if (selectedData) {
    // 显示表单并填充数据
    taskFormRef.value.show(selectedData);
  }
};

// 处理下达
const handleAssign = () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning("请选择要下达的数据");
    return;
  }
  console.log("下达数据", selectedRowKeys.value);
  message.success("下达成功");
};

// 单行操作
const handleTaskAssign = (record) => {
  console.log("下达任务", record);
  message.success("任务下达成功");
};

const handleTaskRecall = (record) => {
  console.log("召回任务", record);
  message.success("任务召回成功");
};
</script>

<style scoped>
.form-row {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;
  margin-bottom: 8px;
}

:deep(.ant-form-item) {
  margin-bottom: 0;
  display: flex;
  flex-direction: column;
}

:deep(.ant-form-item-label) {
  text-align: left;
}

:deep(.ant-form-item-control) {
  flex: 1;
  width: 100%;
}

:deep(.ant-table-wrapper) {
  margin-top: 8px;
}

:deep(.ant-table-thead > tr > th),
:deep(.ant-table-tbody > tr > td) {
  text-align: center;
}

:deep(.ant-checkbox-wrapper) {
  cursor: pointer;
}

:deep(.ant-table-row) {
  cursor: pointer;
}
</style>
