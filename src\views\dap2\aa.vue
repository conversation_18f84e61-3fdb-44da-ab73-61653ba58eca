<template>
  <div id="gantt_here" class="gantt-container"></div>
</template>

<script lang="ts" setup>
import { onMounted } from "vue";
import gantt from "dhtmlx-gantt";
import "dhtmlx-gantt/codebase/dhtmlxgantt.css";

function generateRandomTasks() {
  const types = ["A", "B", "C"];
  const tasks = [];
  for (let i = 1; i <= 100; i++) {
    const type = types[Math.floor(Math.random() * types.length)];
    const startMonth = Math.floor(Math.random() * 9); // 0 to 8
    const startDate = new Date(
      2025,
      startMonth,
      Math.floor(Math.random() * 28) + 1
    );
    const endDate = new Date(startDate);
    endDate.setMonth(startDate.getMonth() + 3 + Math.floor(Math.random() * 3)); // 3 to 5 months later
    tasks.push({
      id: i,
      text: `Task ${i}`,
      type: type,
      start_date: gantt.date.date_to_str("%Y-%m-%d")(startDate),
      end_date: gantt.date.date_to_str("%Y-%m-%d")(endDate),
      duration: Math.floor(
        (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)
      ),
    });
  }
  return tasks;
}

onMounted(() => {
  gantt.plugins({ marker: true });

  gantt.config.xml_date = "%Y-%m-%d %H:%i";
  gantt.config.min_column_width = 20;

  gantt.config.columns = [
    { name: "text", label: "大修编号", width: "*", tree: true },
    { name: "type", label: "类型", align: "center", width: 50 },
    { name: "start_date", label: "计划开始时间", align: "center", width: 100 },
    { name: "end_date", label: "计划结束时间", align: "center", width: 100 },
    { name: "duration", label: "计划工期", align: "center", width: 50 },
  ];

  gantt.config.scales = [{ unit: "month", step: 1, format: "%n月" }];

  gantt.config.scale_height = 50;
  gantt.config.start_date = new Date(2025, 0, 1);
  gantt.config.end_date = new Date(2025, 11, 31);
  gantt.config.fit_tasks = true;

  gantt.templates.task_class = function (start, end, task) {
    if (task.type === "A") {
      return "task-type-A";
    } else if (task.type === "B") {
      return "task-type-B";
    } else if (task.type === "C") {
      return "task-type-C";
    }
    return "";
  };
  // 设置表头高度为0以隐藏表头
  gantt.config.scale_height = 0;
  // 如果需要，也可以隐藏左侧表格的表头
  gantt.config.show_grid_header = false;
  // 隐藏时间轴
  gantt.config.show_task_cells = false;
  gantt.init("gantt_here");

  const tasks = { data: generateRandomTasks() };
  gantt.parse(tasks);

  const today = new Date();
  gantt.addMarker({
    start_date: new Date(
      today.getFullYear(),
      today.getMonth(),
      today.getDate()
    ),
    css: "current-month",
    text: "当前月份",
    title: "当前月份",
  });

  gantt.renderMarkers();
});
</script>

<style scoped>
.gantt-container {
  width: 100%;
  height: 100vh;
  overflow: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* Internet Explorer 10+ */
}

.gantt-container::-webkit-scrollbar {
  display: none; /* Safari and Chrome */
}

.gantt_grid_head_cell,
.gantt_task_head {
  background-color: #0f5391 !important;
  font-weight: bold;
  color: white !important;
}

.gantt_row:nth-child(odd) .gantt_cell,
.gantt_task_row:nth-child(odd) {
  background-color: #1d274b !important;
}

.gantt_row:nth-child(even) .gantt_cell,
.gantt_task_row:nth-child(even) {
  background-color: #162e5a !important;
}

.gantt_cell {
  color: white !important;
}

.gantt_task_line {
  border: none !important;
}

.current-month {
  border-left: 2px dashed #00ff00 !important;
  z-index: 1;
}

.task-type-A {
  background-color: #ff9999 !important; /* 红色 */
}

.task-type-B {
  background-color: #99ff99 !important; /* 绿色 */
}

.task-type-C {
  background-color: #99ccff !important; /* 蓝色 */
}

/* 设置表格和甘特图区分的线为红色 */
.gantt_splitter {
  background-color: rgb(0, 180, 42) !important;
}
</style>
