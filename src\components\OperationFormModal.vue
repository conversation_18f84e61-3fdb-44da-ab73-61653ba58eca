<template>
  <a-modal
    v-model:visible="visible"
    :title="title"
    :width="800"
    @ok="handleOk"
    @cancel="handleCancel"
    okText="确定"
    cancelText="取消"
  >
    <a-form ref="formRef" :model="formState" :rules="rules" layout="vertical">
      <a-row :gutter="16">
        <a-col :span="12">
          <!-- 评价岗位（始终只读） -->
          <a-form-item label="评价岗位" name="evaluation">
            <a-input v-model:value="formState.evaluation" disabled />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <!-- 大修组织机构岗位/处室（始终只读） -->
          <a-form-item label="大修组织机构岗位/处室" name="department">
            <a-input v-model:value="formState.department" />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="12">
          <!-- 处室 -->
          <a-form-item
            name="subDepartment"
            label="处室"
            :rules="[{ required: true, message: '请选择或输入处室' }]"
          >
            <a-auto-complete
              v-model:value="formState.subDepartment"
              :options="filteredSubDepartmentOptions"
              style="width: 100%"
              placeholder="请输入或选择处室"
              allow-clear
              @search="handleSubDepartmentACSearch"
              @focus="handleSubDepartmentACFocus"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <!-- 姓名 -->
          <a-form-item
            name="name"
            label="姓名"
            :rules="[{ required: true, message: '请选择或输入姓名' }]"
          >
            <a-auto-complete
              v-model:value="formState.name"
              :options="filteredNameOptions"
              style="width: 100%"
              placeholder="请输入或选择姓名"
              allow-clear
              @search="handleNameACSearch"
              @focus="handleNameACFocus"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="12">
          <!-- 工号 -->
          <a-form-item label="工号" name="staffId">
            <a-auto-complete
              v-model:value="formState.staffId"
              :options="filteredStaffIdOptions"
              style="width: 100%"
              placeholder="请输入或选择工号"
              allow-clear
              @search="handleStaffIdACSearch"
              @focus="handleStaffIdACFocus"
            ></a-auto-complete>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <!-- 大修结效 -->
          <a-form-item label="大修结效" name="score">
            <a-input
              v-model:value="formState.score"
              placeholder="请输入大修结效"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="12">
          <!-- 大修绩效加分 -->
          <a-form-item label="大修绩效加分" name="addScore">
            <a-input
              v-model:value="formState.addScore"
              placeholder="请输入大修绩效加分"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <!-- 大修绩效扣分 -->
          <a-form-item label="大修绩效扣分" name="finalScore">
            <a-input
              v-model:value="formState.finalScore"
              placeholder="请输入大修绩效扣分"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="12">
          <!-- 加分项说明 -->
          <a-form-item label="加分项说明" name="addScoreDesc">
            <a-textarea
              v-model:value="formState.addScoreDesc"
              placeholder="请输入加分项说明"
              :rows="2"
            ></a-textarea>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <!-- 扣分项说明 -->
          <a-form-item label="扣分项说明" name="deductScoreDesc">
            <a-textarea
              v-model:value="formState.deductScoreDesc"
              placeholder="请输入扣分项说明"
              :rows="2"
            ></a-textarea>
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="24">
          <!-- 绩效规则查看 -->
          <a-form-item label="绩效规则查看" name="ruleView">
            <a-textarea
              v-model:value="formState.ruleView"
              placeholder="请输入绩效规则"
              :rows="2"
            ></a-textarea>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </a-modal>
</template>

<script setup>
import { ref, reactive, defineEmits, defineExpose } from "vue";
import { Form, AutoComplete } from "ant-design-vue";

const emit = defineEmits(["submit"]);

// 表单状态
const visible = ref(false);
const title = ref("新增绩效评价");
const formRef = ref();

// 定义选项数据
const subDepartmentOptions = ref([
  { value: "综合科", label: "综合科" },
  { value: "政策法规科", label: "政策法规科" },
  { value: "行政审批科", label: "行政审批科" },
  { value: "督查科", label: "督查科" },
]);

const nameOptions = ref([
  { value: "胡小文", label: "胡小文" },
  { value: "李明", label: "李明" },
  { value: "张华", label: "张华" },
  { value: "王芳", label: "王芳" },
]);

// 为 AutoComplete 定义工号选项
const staffIdOptions = ref([
  { value: "10001" },
  { value: "10002" },
  { value: "10003" },
  { value: "10004" },
]);

// 用于 AutoComplete 显示的过滤后选项
const filteredSubDepartmentOptions = ref([]);
const filteredNameOptions = ref([]);
const filteredStaffIdOptions = ref([]);

// 初始化过滤列表
filteredSubDepartmentOptions.value = subDepartmentOptions.value;
filteredNameOptions.value = nameOptions.value;
filteredStaffIdOptions.value = staffIdOptions.value;

// AutoComplete 的过滤逻辑 (旧，将被替换)
const filterOption = (inputValue, option) => {
  return option.value.toUpperCase().indexOf(inputValue.toUpperCase()) !== -1;
};

// AutoComplete 的过滤逻辑 - 处室 (旧)
const filterOptionSubDepartment = (inputValue, option) => {
  // 基于 label 或 value 进行过滤，这里假设 label 和 value 相同或都可用于搜索
  return option.label.toUpperCase().indexOf(inputValue.toUpperCase()) !== -1;
};

// AutoComplete 的过滤逻辑 - 姓名 (旧)
const filterOptionName = (inputValue, option) => {
  return option.label.toUpperCase().indexOf(inputValue.toUpperCase()) !== -1;
};

// --- AutoComplete 事件处理 ---

// 搜索处理
const handleSubDepartmentACSearch = (inputValue) => {
  if (!inputValue) {
    filteredSubDepartmentOptions.value = subDepartmentOptions.value;
  } else {
    filteredSubDepartmentOptions.value = subDepartmentOptions.value.filter(
      (option) => option.label.toUpperCase().includes(inputValue.toUpperCase())
    );
  }
};

const handleNameACSearch = (inputValue) => {
  if (!inputValue) {
    filteredNameOptions.value = nameOptions.value;
  } else {
    filteredNameOptions.value = nameOptions.value.filter((option) =>
      option.label.toUpperCase().includes(inputValue.toUpperCase())
    );
  }
};

const handleStaffIdACSearch = (inputValue) => {
  if (!inputValue) {
    filteredStaffIdOptions.value = staffIdOptions.value;
  } else {
    filteredStaffIdOptions.value = staffIdOptions.value.filter((option) =>
      option.value.toUpperCase().includes(inputValue.toUpperCase())
    );
  }
};

// 聚焦处理 - 恢复完整列表
const handleSubDepartmentACFocus = () => {
  filteredSubDepartmentOptions.value = subDepartmentOptions.value;
};

const handleNameACFocus = () => {
  filteredNameOptions.value = nameOptions.value;
};

const handleStaffIdACFocus = () => {
  filteredStaffIdOptions.value = staffIdOptions.value;
};

// 表单数据
const formState = reactive({
  evaluation: "", // 评价岗位，来自传入参数
  department: "",
  subDepartment: "",
  name: "",
  staffId: "", // 添加工号字段
  score: "",
  addScore: "",
  addScoreDesc: "---", // 默认值
  finalScore: "",
  deductScoreDesc: "---", // 默认值
  ruleView: "",
  key: "",
  hasDetailsButton: false,
});

// 表单验证规则
const rules = {
  department: [
    { required: true, message: "请输入大修组织机构岗位/处室", trigger: "blur" },
  ],
  subDepartment: [{ required: true, message: "请选择处室", trigger: "change" }],
  name: [{ required: true, message: "请选择姓名", trigger: "change" }],
  staffId: [{ required: true, message: "请输入工号", trigger: "change" }], // 添加工号验证规则
  score: [{ required: true, message: "请输入大修结效", trigger: "blur" }],
  addScore: [
    { required: true, message: "请输入大修绩效加分", trigger: "blur" },
  ],
  finalScore: [
    { required: true, message: "请输入大修绩效扣分", trigger: "blur" },
  ],
};

// 重置表单
const resetForm = () => {
  formState.department = "";
  formState.subDepartment = "";
  formState.name = "";
  formState.staffId = ""; // 添加工号重置
  formState.score = "";
  formState.addScore = "";
  formState.addScoreDesc = "---";
  formState.finalScore = "";
  formState.deductScoreDesc = "---";
  formState.ruleView = "";
  formState.key = "";
  formState.hasDetailsButton = false;

  if (formRef.value) {
    formRef.value.resetFields();
  }
};

// 打开新增弹窗
const openModal = (evaluation) => {
  resetForm();
  formState.evaluation = evaluation;
  title.value = "新增绩效评价";
  visible.value = true;
};

// 确定按钮处理函数
const handleOk = () => {
  formRef.value
    .validate()
    .then(() => {
        emit("submit", {
          ...formState,
          key: Date.now().toString(),
        });
      visible.value = false;
    })
    .catch((error) => {
      console.log("Validation failed:", error);
    });
};

// 取消按钮处理函数
const handleCancel = () => {
  resetForm();
  visible.value = false;
};

// 向父组件暴露方法
defineExpose({
  openModal,
});
</script>

<style scoped>
/* 可以根据需要添加样式 */
/* 调整tag模式的Select样式 */
:deep(.ant-select-selection-overflow) {
  flex-wrap: nowrap;
}

:deep(.ant-select-selection-item) {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
