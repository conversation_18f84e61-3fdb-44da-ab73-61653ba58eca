import "./assets/main.css";

import {
  createApp
} from "vue";
import {
  createPinia
} from "pinia";

import App from "./App.vue";
const app = createApp(App);
import router from "./router";
import "element-plus/dist/index.css";
import ElementPlus from "element-plus";
// import GlobalFloatingButton from './components/global/GlobalFloatingButton.vue';

// --- 全局注册组件 ---
// app.component('GlobalFloatingButton', GlobalFloatingButton);

import Antd from 'ant-design-vue';


import * as ElementPlusIconsVue from "@element-plus/icons-vue";
import VueGridLayout from 'vue-grid-layout-v3';

// 使用 import.meta.glob 匹配 components 目录下的所有 .vue 文件
const components =
  import.meta.glob("./components/**/*.vue");
async function registerGlobalComponents(app) {
  let componentName = "";
  for (const [filePath, module] of Object.entries(components)) {
    // 提取组件名称，这里假设文件名是 PascalCase 的
    componentName = filePath
      .split("/")
      .pop() // 获取文件名
      .replace(/\.\w+$/, "") // 移除文件扩展名
      .replace(/-/g, ""); // 如果文件名是 kebab-case，可以将其转换为 camelCase（可选）
    // 动态引入组件
    const component = (await module()).default;
    // 注册全局组件
    app.component(componentName, component);
    // console.log(component, componentName, "componentName");
  }
}


// ----------
app.use(VueGridLayout);
app.use(createPinia());
app.use(router);
app.use(ElementPlus);
app.use(Antd);


for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component);
}
// 注册全局组件
registerGlobalComponents(app).then(() => {
  // 挂载 Vue 应用
  app.mount("#app");
});