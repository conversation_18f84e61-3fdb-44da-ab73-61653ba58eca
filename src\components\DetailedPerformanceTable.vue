<template>
  <div class="detailed-performance-table">
    <!-- 详细绩效评价表格 -->
    <a-table
      :dataSource="performanceData"
      :columns="performanceColumns"
      :pagination="false"
      :scroll="{ y: 450 }"
      bordered
      size="middle"
      class="performance-table"
      :customRow="
        (record) => ({
          onDblclick: () => handleRowDblClick(record),
        })
      "
    >
      <!-- 操作列 -->
      <template #bodyCell="{ column, text, record, index }">
        <template v-if="column.dataIndex === 'operation'">
          <div class="operation-buttons">
            <template v-if="isEditing(record)">
              <!-- 编辑状态下的保存和取消按钮 -->
              <a-button
                type="primary"
                shape="circle"
                size="small"
                @click="saveEdit(record)"
              >
                <template #icon><CheckOutlined /></template>
              </a-button>
              <a-button
                type="danger"
                shape="circle"
                size="small"
                @click="cancelEdit"
              >
                <template #icon><CloseOutlined /></template>
              </a-button>
            </template>
            <template v-else>
              <!-- 非编辑状态下的添加和删除按钮 -->
              <a-button
                type="primary"
                shape="circle"
                size="small"
                @click="handleAddOperation(record, index)"
              >
                <template #icon><PlusOutlined /></template>
              </a-button>
              <a-button type="danger" shape="circle" size="small">
                <template #icon><MinusOutlined /></template>
              </a-button>
            </template>
          </div>
        </template>

        <!-- 可编辑的列 -->
        <template
          v-else-if="
            isEditing(record) && editableFields.includes(column.dataIndex)
          "
        >
          <!-- 处室 -->
          <template v-if="column.dataIndex === 'subDepartment'">
            <a-select v-model:value="record.subDepartment" style="width: 100%">
              <a-select-option value="运行一处">运行一处</a-select-option>
              <a-select-option value="计划二处">计划二处</a-select-option>
              <a-select-option value="机械一处">机械一处</a-select-option>
              <a-select-option value="电器一处">电器一处</a-select-option>
              <a-select-option value="仪控一处">仪控一处</a-select-option>
            </a-select>
          </template>

          <!-- 姓名 -->
          <template v-else-if="column.dataIndex === 'name'">
            <a-select v-model:value="record.name" style="width: 100%">
              <a-select-option value="胡小文">胡小文</a-select-option>
              <a-select-option value="李明">李明</a-select-option>
              <a-select-option value="张华">张华</a-select-option>
              <a-select-option value="王芳">王芳</a-select-option>
            </a-select>
          </template>

          <!-- 多行文本 -->
          <template v-else-if="scoreDescFields.includes(column.dataIndex)">
            <a-textarea
              v-model:value="record[column.dataIndex]"
              :rows="1"
              style="width: 100%"
            />
          </template>

          <!-- 普通输入框 -->
          <template v-else>
            <a-input
              v-model:value="record[column.dataIndex]"
              style="width: 100%"
            />
          </template>
        </template>

        <!-- 加分项描述和扣分项描述列的"---"替换 -->
        <template v-else-if="isScoreDesc(column.dataIndex) && text === '---'">
          <span class="dash-text">---</span>
        </template>

        <!-- 绩效规则查看列 -->
        <template v-else-if="column.dataIndex === 'ruleView' && text">
          <a class="rule-link" @click="handleRuleViewClick(record)">
            {{ text }}
          </a>
        </template>

        <!-- 渲染其他所有非特殊处理列 -->
        <template
          v-else-if="
            !nonRenderColumns.includes(column.dataIndex) &&
            !(isScoreDesc(column.dataIndex) && text === '---')
          "
        >
          {{ text }}
        </template>
      </template>
    </a-table>

    <!-- 操作表单弹窗组件 -->
    <operation-form-modal ref="operationFormRef" @submit="handleFormSubmit" />

    <!-- 新模态框组件 -->
    <rule-view-modal ref="ruleViewModalRef" />
  </div>
</template>

<script setup>
import { ref } from "vue";
import {
  PlusOutlined,
  MinusOutlined,
  CheckOutlined,
  CloseOutlined,
} from "@ant-design/icons-vue";
import OperationFormModal from "./OperationFormModal.vue";
import RuleViewModal from "./RuleViewModal.vue";

// 常量定义 - 减少重复代码和类型断言错误
const editableFields = [
  "subDepartment",
  "name",
  "score",
  "addScore",
  "addScoreDesc",
  "finalScore",
  "deductScoreDesc",
];
const scoreDescFields = ["addScoreDesc", "deductScoreDesc"];
const nonRenderColumns = ["operation", "evaluation", "ruleView"];

// 内部状态数据
const performanceData = ref([
  {
    key: "1",
    evaluation: "大修执行指挥部",
    department: "大修执行指挥部",
    subDepartment: "运行一处",
    name: "胡小文",
    staffId: "10001",
    score: "89",
    addScore: "10",
    addScoreDesc: "---",
    finalScore: "10",
    deductScoreDesc: "---",
    ruleView: "中国核电绩效分值*0.5+中核运行指数*0.5",
  },
  {
    key: "2",
    evaluation: "大修执行指挥部",
    department: "大修执行指挥部",
    subDepartment: "计划二处",
    name: "李明",
    staffId: "10002",
    score: "92",
    addScore: "8",
    addScoreDesc: "提前完成计划编制",
    finalScore: "5",
    deductScoreDesc: "---",
    ruleView: "中国核电绩效分值*0.5+中核运行指数*0.5",
  },
  {
    key: "3",
    evaluation: "大修执行指挥部",
    department: "大修执行指挥部",
    subDepartment: "机械一处",
    name: "张华",
    staffId: "10003",
    score: "85",
    addScore: "12",
    addScoreDesc: "设备检修质量优秀",
    finalScore: "7",
    deductScoreDesc: "---",
    ruleView: "中国核电绩效分值*0.5+中核运行指数*0.5",
  },
  {
    key: "4",
    evaluation: "大修执行指挥部",
    department: "大修执行指挥部",
    subDepartment: "电器一处",
    name: "王芳",
    staffId: "10004",
    score: "88",
    addScore: "9",
    addScoreDesc: "---",
    finalScore: "6",
    deductScoreDesc: "部分设备维护不及时",
    ruleView: "中国核电绩效分值*0.5+中核运行指数*0.5",
  },
  {
    key: "5",
    evaluation: "大修执行指挥部",
    department: "大修执行指挥部",
    subDepartment: "仪控一处",
    name: "赵强",
    staffId: "10005",
    score: "90",
    addScore: "11",
    addScoreDesc: "控制系统优化效果显著",
    finalScore: "8",
    deductScoreDesc: "---",
    ruleView: "中国核电绩效分值*0.5+中核运行指数*0.5",
  },
  {
    key: "6",
    evaluation: "大修执行指挥部",
    department: "大修执行指挥部",
    subDepartment: "运行一处",
    name: "陈明",
    staffId: "10006",
    score: "87",
    addScore: "10",
    addScoreDesc: "---",
    finalScore: "9",
    deductScoreDesc: "交接班记录不完整",
    ruleView: "中国核电绩效分值*0.5+中核运行指数*0.5",
  },
  {
    key: "7",
    evaluation: "大修执行指挥部",
    department: "大修执行指挥部",
    subDepartment: "计划二处",
    name: "刘伟",
    staffId: "10007",
    score: "91",
    addScore: "7",
    addScoreDesc: "计划执行效率高",
    finalScore: "4",
    deductScoreDesc: "---",
    ruleView: "中国核电绩效分值*0.5+中核运行指数*0.5",
  },
  {
    key: "8",
    evaluation: "大修执行指挥部",
    department: "大修执行指挥部",
    subDepartment: "机械一处",
    name: "孙静",
    staffId: "10008",
    score: "86",
    addScore: "13",
    addScoreDesc: "设备维护及时性高",
    finalScore: "6",
    deductScoreDesc: "---",
    ruleView: "中国核电绩效分值*0.5+中核运行指数*0.5",
  },
  {
    key: "9",
    evaluation: "大修执行指挥部",
    department: "大修执行指挥部",
    subDepartment: "电器一处",
    name: "周涛",
    staffId: "10009",
    score: "89",
    addScore: "8",
    addScoreDesc: "---",
    finalScore: "7",
    deductScoreDesc: "部分设备检查不到位",
    ruleView: "中国核电绩效分值*0.5+中核运行指数*0.5",
  },
  {
    key: "10",
    evaluation: "大修执行指挥部",
    department: "大修执行指挥部",
    subDepartment: "仪控一处",
    name: "吴丽",
    staffId: "10010",
    score: "93",
    addScore: "12",
    addScoreDesc: "系统稳定性提升明显",
    finalScore: "5",
    deductScoreDesc: "---",
    ruleView: "中国核电绩效分值*0.5+中核运行指数*0.5",
  },
  {
    key: "11",
    evaluation: "维修部门",
    department: "维修部门",
    subDepartment: "运行一处",
    name: "郑强",
    staffId: "10011",
    score: "88",
    addScore: "9",
    addScoreDesc: "操作规范，无失误",
    finalScore: "7",
    deductScoreDesc: "---",
    ruleView: "中国核电绩效分值*0.5+中核运行指数*0.5",
  },
  {
    key: "12",
    evaluation: "维修部门",
    department: "维修部门",
    subDepartment: "计划二处",
    name: "冯勇",
    staffId: "10012",
    score: "90",
    addScore: "10",
    addScoreDesc: "---",
    finalScore: "6",
    deductScoreDesc: "计划调整响应稍慢",
    ruleView: "中国核电绩效分值*0.5+中核运行指数*0.5",
  },
  {
    key: "13",
    evaluation: "维修部门",
    department: "维修部门",
    subDepartment: "机械一处",
    name: "曹敏",
    staffId: "10013",
    score: "87",
    addScore: "11",
    addScoreDesc: "快速处理设备故障",
    finalScore: "8",
    deductScoreDesc: "---",
    ruleView: "中国核电绩效分值*0.5+中核运行指数*0.5",
  },
  {
    key: "14",
    evaluation: "维修部门",
    department: "维修部门",
    subDepartment: "电器一处",
    name: "邓琳",
    staffId: "10014",
    score: "92",
    addScore: "7",
    addScoreDesc: "---",
    finalScore: "4",
    deductScoreDesc: "巡检记录有遗漏",
    ruleView: "中国核电绩效分值*0.5+中核运行指数*0.5",
  },
  {
    key: "15",
    evaluation: "维修部门",
    department: "维修部门",
    subDepartment: "仪控一处",
    name: "石磊",
    staffId: "10015",
    score: "89",
    addScore: "10",
    addScoreDesc: "仪表校验准确",
    finalScore: "7",
    deductScoreDesc: "---",
    ruleView: "中国核电绩效分值*0.5+中核运行指数*0.5",
  },
  {
    key: "16",
    evaluation: "技术支持部",
    department: "技术支持部",
    subDepartment: "运行一处",
    name: "侯杰",
    staffId: "10016",
    score: "91",
    addScore: "8",
    addScoreDesc: "---",
    finalScore: "5",
    deductScoreDesc: "操作票填写不规范",
    ruleView: "中国核电绩效分值*0.5+中核运行指数*0.5",
  },
  {
    key: "17",
    evaluation: "技术支持部",
    department: "技术支持部",
    subDepartment: "计划二处",
    name: "彭宇",
    staffId: "10017",
    score: "86",
    addScore: "12",
    addScoreDesc: "物资协调保障有力",
    finalScore: "6",
    deductScoreDesc: "---",
    ruleView: "中国核电绩效分值*0.5+中核运行指数*0.5",
  },
  {
    key: "18",
    evaluation: "技术支持部",
    department: "技术支持部",
    subDepartment: "机械一处",
    name: "肖燕",
    staffId: "10018",
    score: "90",
    addScore: "9",
    addScoreDesc: "--- ",
    finalScore: "7",
    deductScoreDesc: "工具管理稍显混乱",
    ruleView: "中国核电绩效分值*0.5+中核运行指数*0.5",
  },
  {
    key: "19",
    evaluation: "技术支持部",
    department: "技术支持部",
    subDepartment: "电器一处",
    name: "袁波",
    staffId: "10019",
    score: "88",
    addScore: "11",
    addScoreDesc: "电路图绘制清晰",
    finalScore: "8",
    deductScoreDesc: "---",
    ruleView: "中国核电绩效分值*0.5+中核运行指数*0.5",
  },
  {
    key: "20",
    evaluation: "技术支持部",
    department: "技术支持部",
    subDepartment: "仪控一处",
    name: "龚雪",
    staffId: "10020",
    score: "92",
    addScore: "10",
    addScoreDesc: "--- ",
    finalScore: "5",
    deductScoreDesc: "参数记录偶有偏差",
    ruleView: "中国核电绩效分值*0.5+中核运行指数*0.5",
  },
  {
    key: "933",
    evaluation: "大修执行指挥部",
    department: "大修执行指挥部",
    subDepartment: "电器一处",
    name: "周涛",
    staffId: "10009",
    score: "89",
    addScore: "8",
    addScoreDesc: "---",
    finalScore: "7",
    deductScoreDesc: "部分设备检查不到位",
    ruleView: "中国核电绩效分值*0.5+中核运行指数*0.5",
  },
]);

// 按评价岗位排序，确保相同岗位的行连续，以便合并
performanceData.value.sort((a, b) => {
  // 主要按 evaluation 排序
  const evalCompare = a.evaluation.localeCompare(b.evaluation);
  if (evalCompare !== 0) {
    return evalCompare;
  }

  return parseInt(a.key) - parseInt(b.key); // 假设 key 是数字或数字字符串
});

// 操作表单弹窗引用
const operationFormRef = ref(null);

// 当前正在编辑的行key
const editingKey = ref("");

// 判断行是否处于编辑状态
const isEditing = (record) => record.key === editingKey.value;

// 处理双击行事件，开始编辑
const handleRowDblClick = (record) => {
  console.log("Row double clicked:", record);
  editingKey.value = record.key;
};

// 保存编辑后的行数据
const saveEdit = (record) => {
  console.log("Saving edited row:", record);

  // 获取并打印被编辑的字段数据
  const editedData = {};
  editableFields.forEach((field) => {
    editedData[field] = record[field];
  });

  console.log("编辑后的数据:", editedData);

  // 退出编辑状态
  editingKey.value = "";
};

// 取消编辑，恢复原始数据
const cancelEdit = () => {
  console.log("Canceling edit");
  // 直接退出编辑状态，不保存修改
  editingKey.value = "";
};

// 用于判断是否为得分描述列的辅助函数
const isScoreDesc = (dataIndex) => {
  return scoreDescFields.includes(dataIndex);
};

// 计算合并行数的辅助函数
const getRowSpan = (key, index, data) => {
  if (!data || data.length === 0) return 1;
  const currentValue = data[index]?.[key];
  if (currentValue === undefined) return 1;

  // 向上查找第一个不同的行或数组开头
  let firstIndex = index;
  while (firstIndex > 0 && data[firstIndex - 1]?.[key] === currentValue) {
    firstIndex--;
  }
  // 如果当前行不是分组的第一行，返回0
  if (index > firstIndex) {
    return 0;
  }
  // 向下计算连续相同行的数量
  let count = 1;
  while (
    index + count < data.length &&
    data[index + count]?.[key] === currentValue
  ) {
    count++;
  }
  return count;
};

// 新模态框组件的引用
const ruleViewModalRef = ref(null);

// 查看规则详情 (点击文本触发)
const handleRuleViewClick = (record) => {
  console.log("Rule view clicked:", record);
  // 调用子组件的 show 方法，传递 evaluation
  ruleViewModalRef.value?.show(record.evaluation);
};

// 处理点击加号按钮
const handleAddOperation = (record, index) => {
  console.log("Add operation clicked:", record, index);
  // 调用内部OperationFormModal组件的openModal方法
  operationFormRef.value.openModal(record.evaluation);
};

// 处理表单提交（处理OperationFormModal的提交事件）
const handleFormSubmit = (formData) => {
  console.log("Form submitted:", formData);

  // 处理表单提交后的逻辑
  if (formData.isEdit) {
    // 编辑模式 - 更新现有数据
    const index = performanceData.value.findIndex(
      (item) => item.key === formData.key
    );
    if (index !== -1) {
      // 更新除评价岗位和大修组织机构岗位/处室外的其他数据
      const updatedRecord = { ...performanceData.value[index], ...formData };
      performanceData.value[index] = updatedRecord;
    }
  } else {
    // 新增模式 - 添加新行
    performanceData.value.push({ ...formData });
  }
};

// 绩效评价表格列
const performanceColumns = [
  { title: "操作", dataIndex: "operation", width: 80, align: "center" },
  {
    title: "评价岗位",
    dataIndex: "evaluation",
    width: 150,
    align: "center",
    customCell: (record, index) => ({
      rowSpan: getRowSpan("evaluation", index, performanceData.value),
    }),
  },
  {
    title: "大修组织机构岗位/处室",
    dataIndex: "department",
    width: 200,
    align: "center",
  },
  {
    title: "处室",
    dataIndex: "subDepartment",
    width: 120,
    align: "center",
  },
  {
    title: "姓名",
    dataIndex: "name",
    width: 100,
    align: "center",
  },
  {
    title: "大修结效",
    dataIndex: "score",
    width: 100,
    align: "center",
  },
  {
    title: "大修绩效加分",
    dataIndex: "addScore",
    width: 120,
    align: "center",
  },
  {
    title: "加分项说明",
    dataIndex: "addScoreDesc",
    width: 120,
    align: "center",
  },
  {
    title: "大修绩效扣分",
    dataIndex: "finalScore",
    width: 120,
    align: "center",
  },
  {
    title: "扣分项说明",
    dataIndex: "deductScoreDesc",
    width: 120,
    align: "center",
  },
  {
    title: "绩效规则查看",
    dataIndex: "ruleView",
    width: 200,
    align: "center",
    customCell: (record, index) => ({
      rowSpan: getRowSpan("evaluation", index, performanceData.value),
    }),
  },
];
</script>

<style scoped>
.detailed-performance-table {
  width: 100%;
  display: flex;
  flex-direction: column;
}

.operation-buttons {
  display: flex;
  justify-content: space-around;
  gap: 4px;
}

.dash-text {
  color: #999;
}

.rule-link {
  color: #1890ff;
  cursor: pointer;
  text-decoration: none;
}

.rule-link:hover {
  text-decoration: underline;
}

/* 确保表格内容垂直居中 */
:deep(.ant-table-cell) {
  vertical-align: middle;
}
</style>
