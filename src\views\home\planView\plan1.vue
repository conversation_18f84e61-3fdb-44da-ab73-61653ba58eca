<template>
  <div class="form">
    <div v-for="item in formState" :key="item.id" class="form-item">
      <span class="title-num"> {{ item.num }}</span>
      <span> {{ item.Title }}</span>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { reactive } from "vue";

interface FormState {
  id: string;
  Title: string;
  num: string;
}
const formState = reactive<FormState[]>([
  {
    id: "1",
    Title: "年度大修次数",
    num: "6",
  },
  {
    id: "2",
    Title: "计划总工期",
    num: "6",
  },
  {
    id: "3",
    Title: "大修类型",
    num: "3A/2B/11C",
  },
  {
    id: "4",
    Title: "平均工期(A)",
    num: "3A/2B/11C",
  },
  {
    id: "5",
    Title: "平均工期(B)",
    num: "1",
  },
  {
    id: "6",
    Title: "平均工期(C)",
    num: "1",
  },
]);
</script>

<style lang="scss" scoped>
.form {
  display: flex;
  width: 15%;
  flex-direction: column;
  height: 100%;
  display: flex;
  justify-content: space-between;
}
.form-item {
  border: 1px dashed #f70404;
  color: #fff;

  display: flex;
  flex-direction: column;
  border-radius: 5px;

  span {
    display: inline-block;
    width: 100%;
    text-align: center;
    font-size: 13px;
    cursor: pointer;
  }
  .title-num {
    font-size: 22px;
    font-weight: 700;
    color: #f70404;
  }
}
</style>
