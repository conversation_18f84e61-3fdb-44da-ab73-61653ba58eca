<template>
  <a-modal
    v-model:open="visible"
    title="历史记录"
    :width="1000"
    @ok="handleOk"
    @cancel="handleCancel"
    okText="确认"
    cancelText="取消"
  >
    <a-table
      :columns="columns"
      :dataSource="tableData"
      :pagination="{ pageSize: 10 }"
      bordered
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <a @click="handleView(record)">查看</a>
        </template>
      </template>
    </a-table>
  </a-modal>
</template>

<script setup lang="ts">
import { ref } from "vue";

// 表格列定义
const columns = [
  {
    title: "版本号",
    dataIndex: "version",
    width: 180,
  },
  {
    title: "修改人",
    dataIndex: "modifier",
    width: 120,
  },
  {
    title: "修改时间",
    dataIndex: "modifyTime",
    width: 180,
  },
  {
    title: "状态",
    dataIndex: "status",
    width: 100,
  },
  {
    title: "备注",
    dataIndex: "remark",
  },
  {
    title: "操作",
    dataIndex: "action",
    width: 100,
    fixed: "right",
  },
];

// 表格数据
const tableData = ref([
  {
    key: "1",
    version: "241018-10cb53c9-01",
    modifier: "张三",
    modifyTime: "2024-01-18 10:30:00",
    status: "完布",
    remark: "更新2024年度大修计划",
  },
  // ... 其他数据
]);

const visible = ref(false);

// 打开模态框的方法
const openModal = () => {
  visible.value = true;
};

// 处理确认
const handleOk = () => {
  visible.value = false;
};

// 处理取消
const handleCancel = () => {
  visible.value = false;
};

// 查看详情
const handleView = (record: any) => {
  console.log("查看详情:", record);
};

// 暴露方法给父组件
defineExpose({
  openModal,
});
</script>

<style scoped>
:deep(.ant-table-wrapper) {
  margin: -24px;
}
</style>
