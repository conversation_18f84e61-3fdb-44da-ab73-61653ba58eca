<template>
  <div class="left-block">
    <!-- 上部：标题 -->
    <div class="title-section">
      <div class="title-icon">📊</div>
      <h3>年度人力资源配置需求</h3>
      <div class="title-icon">👤</div>
    </div>

    <!-- 中部：统计区域 -->
    <div class="stats-section">
      <div class="stat-item">
        <div class="stat-icon">
          <i class="icon-people">👥</i>
        </div>
        <div class="stat-info">
          <div class="stat-label">总配置人数</div>
        </div>
        <div class="stat-value">{{ totalConfig }}</div>
      </div>
      <div class="stat-item">
        <div class="stat-icon">
          <i class="icon-gap">⚠️</i>
        </div>
        <div class="stat-info">
          <div class="stat-label">当前缺口</div>
        </div>
        <div class="stat-value">{{ currentGap }}</div>
      </div>
    </div>

    <!-- 下部：堆叠柱状图 -->
    <div class="chart-section">
      <div ref="chartRef" class="chart-container"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from "vue";
import * as echarts from "echarts/core";
import { BarChart } from "echarts/charts";
import {
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
} from "echarts/components";
import { CanvasRenderer } from "echarts/renderers";


// 注册必要的组件
echarts.use([
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  BarChart,
  CanvasRenderer,
]);

// 图表引用
const chartRef = ref<HTMLElement | null>(null);
let chart: echarts.ECharts | null = null;

// 模拟数据
const totalConfig = ref(856);
const currentGap = ref(142);

// 图表数据使用reactive定义
const chartData = reactive({
  months: [
    "1月",
    "2月",
    "3月",
    "4月",
    "5月",
    "6月",
    "7月",
    "8月",
    "9月",
    "10月",
    "11月",
    "12月",
  ],
  // 调整数据规模，确保不超过750，堆叠后总和也在合理范围内
  internal: [300, 350, 400, 420, 450, 480, 500, 520, 400, 380, 350, 320],
  external: [200, 250, 280, 300, 250, 220, 200, 180, 300, 320, 350, 380],
  // 运维总缺口数据（正数表示富余，负数表示缺口）
  gap: [0, -180, 210, 280, -120, -250, 300, -220, -270, -190, 250, -230],
});

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return;

  // 创建图表实例
  chart = echarts.init(chartRef.value);

  // 设置图表配置项
  const option = {
    backgroundColor: "transparent",
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
      formatter: function (params: any) {
        let result = `${params[0].name}<br/>`;
        params.forEach((item: any) => {
          // 针对缺口数据特殊处理
          if (item.seriesName === "运维总缺口") {
            const value = item.value;
            const prefix = value >= 0 ? "富余: " : "缺口: ";
            result += `${item.seriesName}: ${Math.abs(value)}<br/>`;
          } else {
            result += `${item.seriesName}: ${item.value}<br/>`;
          }
        });
        return result;
      },
    },
    legend: {
      data: [
        { name: "内部总配置", icon: "circle" },
        { name: "外部总配置", icon: "circle" },
        { name: "运维总缺口", icon: "rect", itemStyle: { color: "#f6c021" } }
      ],
      textStyle: {
        color: "#ccc",
      },
      top: 0,
      itemWidth: 8,
      itemHeight: 8,
    },
    grid: {
      left: "3%",
      right: "3%", // 增加右侧空间，为第二个Y轴留出位置
      bottom: "3%",
      top: "15%",
      containLabel: true,
    },
    xAxis: [
      {
        type: "category",
        data: chartData.months,
        axisLine: {
          lineStyle: {
            color: "#0194f1",
          },
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          color: "#ccc",
          fontSize: 10,
        },
      },
      // 添加一个隐藏的X轴，用于确保右侧Y轴与下方X轴长度一致
      {
        type: "category",
        position: "top",
        show: false,
        data: chartData.months,
      },
    ],
    yAxis: [
      {
        type: "value",
        name: "",
        nameTextStyle: {
          color: "#ccc",
        },
        min: 0,
        max: 900, // 左侧最大值为900
        interval: 150, // 间隔150：0, 150, 300, 450, 600, 750, 900
        splitLine: {
          lineStyle: {
            color: "#11325b",
          },
        },
        axisLabel: {
          color: "#ccc",
          formatter: function (value: number) {
            // 隐藏0刻度
            return value === 0 ? "" : value;
          },
        },
      },
      {
        type: "value",
        name: "",
        nameTextStyle: {
          color: "#ccc",
        },
        position: "right",
        min: -300, // 右侧最小值-300
        max: 300, // 保持右侧最大值
        interval: 150, // 间隔150：-300, -150, 0, 150, 300
        splitLine: {
          show: false, // 隐藏右侧Y轴的刻度线
        },
        axisLine: {
          show: false, // 隐藏右侧Y轴线
        },
        axisTick: {
          show: false, // 隐藏右侧Y轴的刻度标记
        },
        axisLabel: {
          color: "#fff",
        },
        // 确保0刻度线与左侧Y轴对齐
        axisPointer: {
          label: {
            show: false,
          },
        },
      },
    ],
    series: [
      {
        name: "内部总配置",
        type: "bar",
        stack: "配置总量", // 堆叠组
        emphasis: {
          focus: "series",
        },
        barWidth: "40%",
        itemStyle: {
          color: "#61ddaa",
        },
        data: chartData.internal,
      },
      {
        name: "外部总配置",
        type: "bar",
        stack: "配置总量", // 与内部总配置堆叠
        emphasis: {
          focus: "series",
        },
        itemStyle: {
          color: "#009cff",
        },
        data: chartData.external,
      },
      {
        name: "运维总缺口",
        type: "bar",
        yAxisIndex: 1, // 使用第二个Y轴
        xAxisIndex: 0, // 确保使用相同的X轴
        barGap: "-100%", // 确保与其他系列水平对齐
        barCategoryGap: "0%", // 确保与其他系列水平对齐
        emphasis: {
          focus: "series",
        },
        barWidth: "40%", // 设置与其他柱状图相同的宽度
        // 运维总缺口数据，正数和负数都显示
        data: chartData.gap.map((value: number) => {
          return {
            value: value,
            itemStyle: {
              color: "#f6c021", // 统一颜色
              // 正值：上方圆角 [左上, 右上, 右下, 左下]
              // 负值：下方圆角 [左上, 右上, 右下, 左下]
              borderRadius: value >= 0 ? [4, 4, 0, 0] : [0, 0, 4, 4],
            },
          };
        }),
        // 添加标记线在0位置
        markLine: {
          silent: true,
          symbol: "none",
          lineStyle: {
            color: "#f6c021",
            width: 1,
            type: "solid",
            opacity: 1, // 设置透明度为0，隐藏标记线
          },
          label: {
            show: false, // 隐藏标记线的标签
          },
          data: [
            {
              yAxis: 0, // 在Y轴0位置画线
              lineStyle: {
                color: "#f6c021",
                width: 1,
                opacity: 1, // 设置透明度为0，隐藏标记线
              },
              label: {
                show: false, // 隐藏标记线的标签
              },
            },
          ],
        },

        // 添加标签配置，显示数值
        label: {
          show: true,
          position: "top",
          formatter: function (params: any) {
            // 当值为0时返回空字符串，不显示数值
            // 负数显示负号，正数不显示符号
            return params.value === 0 ? "" : params.value;
          },
          color: "#f6c021",
          fontSize: 10,
        },
        // 添加标记点，在值为0的位置显示小圆点
        markPoint: {
          symbol: "circle",
          symbolSize: 8,
          itemStyle: {
            color: "#f6c021", // 黄色小点
          },
          data: chartData.gap
            .map((value, index) => {
              if (value === 0) {
                return {
                  coord: [index, 0], // 使用坐标定位
                  value: value,
                };
              }
              return null;
            })
            .filter((item) => item !== null),
        },
      },
    ],
  };

  // 使用配置项设置图表
  chart.setOption(option);

  // 添加图例点击事件监听
  chart.on("legendselectchanged", function (params: any) {
    try {
      // 获取当前图例选中状态
      const selected = params.selected;

      // 检查运维总缺口是否被选中
      const gapSelected = selected["运维总缺口"];

      // 更新运维总缺口系列的数据显示
      const newData = chartData.gap.map((value: number) => ({
        value: value,
        itemStyle: {
          // 正数始终显示，负数根据图例选中状态决定是否显示
          color: value >= 0 ? "#f6c021" : gapSelected ? "#f6c021" : "transparent",
          // 保持圆角配置
          borderRadius: value >= 0 ? [4, 4, 0, 0] : [0, 0, 4, 4],
        },
      }));

      // 只更新运维总缺口系列的数据，不更新其他配置
      chart.setOption({
        series: [
          {}, // 内部总配置系列保持不变
          {}, // 外部总配置系列保持不变
          {
            data: newData,
          },
        ],
      });
    } catch (error) {
      console.error("图例交互处理错误:", error);
    }
  });

  // 响应窗口大小变化
  window.addEventListener("resize", handleResize);
};

// 处理窗口大小变化
const handleResize = () => {
  chart?.resize();
};

// 组件挂载时初始化图表
onMounted(() => {
  initChart();
});

// 组件卸载时清理资源
onUnmounted(() => {
  if (chart) {
    chart.dispose();
    chart = null;
  }
  window.removeEventListener("resize", handleResize);
});
</script>

<style scoped>
.left-block {
  background-color: #192347;
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  border-radius: 4px;
  color: white;
  box-sizing: border-box;
}

.title-section {
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 4px;
}

.title-icon {
  font-size: 16px;
  color: #37b1fe;
  margin: 0 10px;
}

.title-section h3 {
  margin: 0;
  font-size: 16px;
  color: #03d0f4;
}

.stats-section {
  height: 40px;
  display: flex;
  justify-content: space-between;
  gap: 10px;
}

.stat-item {
  flex: 1;
  display: flex;
  align-items: center;
  background-color: rgba(55, 177, 254, 0.1);
  border-radius: 4px;
  padding: 0;
  height: 100%;
}

.stat-icon {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: #37b1fe;
  height: 100%;
}

.stat-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.stat-label {
  font-size: 14px;
  color: #ccc;
  text-align: center;
}

.stat-value {
  flex: 1;
  font-size: 18px;
  font-weight: bold;
  color: #37b1fe;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.chart-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 10px 0 0 0;
}

.chart-container {
  flex: 1;
  width: 100%;
  border-radius: 4px;
  /* 移除背景色设置，使用ECharts API设置 */
}
</style>
