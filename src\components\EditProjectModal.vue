<template>
  <a-modal
    v-model:visible="visible"
    title="编辑重点项目"
    @ok="handleOk"
    @cancel="handleCancel"
    :maskClosable="false"
    width="500px"
  >
    <div class="project-list">
      <div v-for="(item, index) in projectItems" :key="index" class="project-item">
        <div class="item-id">{{ item.id }}</div>
        <a-input v-model:value="item.text" placeholder="请输入项目名称" />
      </div>
    </div>
    <template #footer>
      <a-button key="back" @click="handleCancel">取消</a-button>
      <a-button key="submit" type="primary" @click="handleOk">确定</a-button>
    </template>
  </a-modal>
</template>

<script setup>
import { ref, reactive, watch } from 'vue';

// 控制弹窗显示/隐藏
const visible = ref(false);

// 接收传入的项目数据
const props = defineProps({
  items: {
    type: Array,
    default: () => [],
  }
});

// 创建本地响应式项目数据
const projectItems = ref([]);

// 监听传入的项目数据变化
watch(() => props.items, (newItems) => {
  // 深拷贝数据，避免直接修改props
  projectItems.value = JSON.parse(JSON.stringify(newItems));
}, { immediate: true, deep: true });

// 暴露给父组件的事件
const emit = defineEmits(['update', 'cancel']);

// 显示弹窗的方法
const showModal = () => {
  visible.value = true;
  // 确保每次打开弹窗时都使用最新的数据
  projectItems.value = JSON.parse(JSON.stringify(props.items));
};

// 确定按钮处理
const handleOk = () => {
  // 向父组件发出更新事件，并传递编辑后的数据
  emit('update', projectItems.value);
  visible.value = false;
};

// 取消按钮处理
const handleCancel = () => {
  emit('cancel');
  visible.value = false;
};

// 暴露方法给父组件调用
defineExpose({
  showModal,
});
</script>

<style scoped>
.project-list {
  max-height: 400px;
  overflow-y: auto;
  padding: 0 10px;
}

.project-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  gap: 10px;
}

.item-id {
  min-width: 30px;
  height: 22px;
  background-color: transparent;
  border: 1px solid #2c85c6;
  color: #2c85c6;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 12px;
}

:deep(.ant-input) {
  background-color: #192347;
  border: 1px solid #335d8a;
  color: #ffffff;
}

:deep(.ant-input:focus) {
  border-color: #04c1e5;
}

:deep(.ant-modal-content) {
  background-color: #1b3863;
  border-radius: 8px;
  border: 1px solid #335d8a;
}

:deep(.ant-modal-header) {
  background-color: #1b3863;
  border-bottom: 1px solid #335d8a;
  border-radius: 8px 8px 0 0;
}

:deep(.ant-modal-title) {
  color: #04c1e5;
  font-weight: bold;
}

:deep(.ant-modal-close) {
  color: #ffffff;
}

:deep(.ant-modal-footer) {
  border-top: 1px solid #335d8a;
}

/* 自定义滚动条样式 */
.project-list::-webkit-scrollbar {
  width: 4px;
}

.project-list::-webkit-scrollbar-track {
  background: #192347;
}

.project-list::-webkit-scrollbar-thumb {
  background-color: #4b5e8e;
  border-radius: 10px;
}
</style> 