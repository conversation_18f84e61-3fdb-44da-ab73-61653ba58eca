<template>
  <div class="area4-container">
    <div class="area4-header">
      <span class="area4-title">大修准备文件</span>
      <span class="area4-more" @click="showFileModal">更多</span>
    </div>
    <div class="file-list-wrapper">
      <div class="file-list-container">
        <div v-for="(file, index) in fileList" :key="index" class="file-item">
          <div class="file-number">{{ index + 1 }}</div>
          <div class="file-name">{{ file.name }}</div>
        </div>
      </div>
    </div>
    
    <!-- 引入文件文档modal组件 -->
    <FileDocumentModal ref="fileModalRef" />
  </div>
</template>

<script setup>
import { ref } from 'vue';
import FileDocumentModal from './FileDocumentModal.vue';

// 文件文档modal引用
const fileModalRef = ref(null);

// 增加更多的文件项以测试滚动效果
const fileList = ref([
  { name: 'xxx-大修准备文件1.ppt' },
  { name: 'xxx-大修准备文件2.ppt' },
  { name: 'xxx-大修准备文件3.ppt' },
  { name: 'xxx-大修准备文件4.ppt' },
  { name: 'xxx-大修准备文件5.ppt' },
  { name: 'xxx-大修准备文件6.ppt' },
  { name: 'xxx-大修准备文件7.ppt' },
  { name: 'xxx-大修准备文件8.ppt' },
  { name: 'xxx-大修准备文件9.ppt' },
  { name: 'xxx-大修准备文件10.ppt' },
  { name: 'xxx-大修准备文件11.ppt' },
  { name: 'xxx-大修准备文件12.ppt' }
]);

// 显示文件文档modal
const showFileModal = () => {
  // 调用子组件的openModal方法
  fileModalRef.value.openModal();
};
</script>

<style scoped>
.area4-container {
  background-color: #192347;
  color: #fff;
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  border-radius: 8px;
  overflow: hidden;
  padding: 0;
}

.area4-header {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;

}

.area4-title {
  color: #02d9fd;
  font-weight: bold;
  font-size: 15px;
  text-align: center;
  padding: 5px 0;
}

.area4-more {
  color: #04c1e5;
  cursor: pointer;
  font-size: 12px;
  position: absolute;
  right: 15px;
}

/* 新增一个包装容器确保滚动区域正确约束 */
.file-list-wrapper {
  flex: 1;
  position: relative;
  overflow: hidden;
}

.file-list-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: auto;
  padding: 5px 0;
  scrollbar-width: thin;
  scrollbar-color: #4b5e8e #192347;
  background-color: #192347;
}

/* Webkit浏览器的滚动条样式 */
.file-list-container::-webkit-scrollbar {
  width: 4px;
}

.file-list-container::-webkit-scrollbar-track {
  background: #192347;
}

.file-list-container::-webkit-scrollbar-thumb {
  background-color: transparent;
  border-radius: 10px;
}

.file-item {
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s;
  background-color: rgba(27, 56, 99, 0.85); /* 带透明度的#1b3863 */
  margin: 0 5px 5px 5px; /* 四周留出间距 */
  border-radius: 4px; /* 添加圆角 */
  padding: 0px 10px;
  height: 25px; /* 固定高度使布局更整齐 */
}

.file-item:hover {
  filter: brightness(1.2);
  background-color: rgba(27, 56, 99, 0.95); /* hover时稍微不透明 */
}

.file-number {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: transparent; /* 透明背景 */
  border: 1.5px solid #04c1e5; /* 边框稍微细一些 */
  color: #fff; /* 文字改为白色 */
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 30px;
  font-size: 11px; /* 字体稍微小一点 */
  font-weight: bold;
  box-sizing: border-box; /* 确保边框不影响尺寸 */
  color: #02d9fd;
}

.file-name {
  color: #fff;
  font-size: 12px;
  font-weight: 400;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  letter-spacing: 0.5px; /* 增加字间距 */
}
</style> 