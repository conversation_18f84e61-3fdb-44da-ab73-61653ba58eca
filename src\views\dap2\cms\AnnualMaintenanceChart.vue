<template>
  <div class="annual-maintenance-chart">
    <div class="chart-title">年度大修统计</div>
    <div ref="chartRef" class="chart-container"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from "vue";
import * as echarts from "echarts/core";
import { BarChart } from "echarts/charts";
import {
  GridComponent,
  TooltipComponent,
  LegendComponent,
} from "echarts/components";
import { CanvasRenderer } from "echarts/renderers";

// 注册必要的组件
echarts.use([
  BarChart,
  GridComponent,
  TooltipComponent,
  LegendComponent,
  CanvasRenderer,
]);

const chartRef = ref<HTMLElement | null>(null);
let chart: echarts.ECharts | null = null;

const locations = [
  {
    name: "泰山",
    completed: 25,
    inProgress: 25,
    planned: 10,
    text: "1A/2B",
  },
  {
    name: "田湾",
    completed: 15,
    inProgress: 15,
    planned: 20,
    text: "1A/2B",
  },
  {
    name: "福清",
    completed: 5,
    inProgress: 35,
    planned: 25,
    text: "1A/2B",
  },
  {
    name: "海阳",
    completed: 0,
    inProgress: 25,
    planned: 0,
    text: "1A/2B",
  },
  {
    name: "三门",
    completed: 15,
    inProgress: 20,
    planned: 0,
  },
  {
    name: "漳州",
    completed: 10,
    inProgress: 0,
    planned: 0,
  },
];

const initChart = () => {
  if (!chartRef.value) return;

  // 初始化图表
  chart = echarts.init(chartRef.value);

  // 准备数据
  const locationNames = locations.map((item) => item.name);
  const completedData = locations.map((item) => item.completed);
  const inProgressData = locations.map((item) => item.inProgress);
  const plannedData = locations.map((item) => item.planned);

  // 准备带有标签的数据
  const completedDataWithLabels = locations.map((item, index) => {
    return {
      value: item.completed,
      label: {
        show: item.completed > 0,
        position: "insideRight",
        formatter: item.text || "2A/1C",
        fontSize: 10,
        color: "#fff",
        distance: 5,
        verticalAlign: "middle", // 垂直居中对齐
      },
    };
  });

  // 配置项
  const option = {
    legend: {
      data: ["已完成", "实施中", "待实施"],
      top: 5,
      left: "center",
      itemWidth: 10,
      itemHeight: 10,
      textStyle: {
        color: "#fff",
        fontSize: 12,
      },
      icon: "circle",
    },
    grid: {
      left: "8%",
      right: "5%",
      bottom: "10%",
      top: "15%",
      containLabel: false,
    },
    xAxis: {
      type: "value",
      max: 80,
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        color: "#aaa",
        fontSize: 10,
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: "#1a2649",
        },
      },
    },
    yAxis: {
      type: "category",
      data: locationNames,
      axisLine: {
        show: true,
        lineStyle: {
          color: "#224678",
        },
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        color: "#fff",
        fontSize: 12,
      },
      splitLine: {
        show: false,
      },
    },
    series: [
      {
        name: "已完成",
        type: "bar",
        stack: "total",
        data: completedDataWithLabels,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
            { offset: 0, color: "rgba(74, 154, 245, 0.1)" },
            { offset: 0.9, color: "#4a9af5" },
            { offset: 1, color: "rgba(74, 154, 245, 0.7)" },
          ]),
          borderRadius: [0, 4, 4, 0],
        },
        barWidth: 12,
      },
      {
        name: "实施中",
        type: "bar",
        stack: "total",
        data: inProgressData,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
            { offset: 0, color: "rgba(135, 214, 171, 0.1)" },
            { offset: 0.9, color: "#87d6ab" },
            { offset: 1, color: "rgba(135, 214, 171, 0.7)" },
          ]),
          borderRadius: [0, 4, 4, 0],
        },
        barWidth: 12,
      },
      {
        name: "待实施",
        type: "bar",
        stack: "total",
        data: plannedData,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
            { offset: 0, color: "rgba(218, 177, 54, 0.1)" },
            { offset: 0.9, color: "#dab136" },
            { offset: 1, color: "rgba(218, 177, 54, 0.7)" },
          ]),
          borderRadius: [0, 4, 4, 0],
        },
        barWidth: 12,
      },
    ],
  };

  // 应用配置项
  chart.setOption(option);

  // 监听窗口大小变化，调整图表大小
  window.addEventListener("resize", handleResize);
};

const handleResize = () => {
  chart?.resize();
};

onMounted(() => {
  initChart();
});

onUnmounted(() => {
  if (chart) {
    chart.dispose();
    chart = null;
  }
  window.removeEventListener("resize", handleResize);
});
</script>

<style scoped>
.annual-maintenance-chart {
  width: 100%;
  height: 100%;
  background-color: #030d3a;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

.chart-title {
  color: "#fff";
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 10px;
  position: relative;
  padding-left: 15px;
}

.chart-title::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 16px;
  background: linear-gradient(to bottom, #0066ff, #00ccff);
}

.chart-container {
  flex: 1;
  width: 100%;
}
</style>
