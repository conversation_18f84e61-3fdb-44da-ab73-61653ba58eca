<template>
  <a-card
    class="overview-card"
    :bordered="false"
    style="height: 100%; display: flex; flex-direction: column"
  >
    <template #title>
      <div class="card-title">
        <div class="title-icon"></div>
        <span>变更采购概览</span>
      </div>
    </template>

    <div class="divider"></div>

    <div class="filter-legend-row">
      <div class="card-filter">
        <div class="filter-item">
          <span>责任部门:</span>
          <a-select
            placeholder="请选择部门"
            size="small"
            style="width: 120px"
            allow-clear
          >
            <a-select-option value="dept1">运维部门</a-select-option>
            <a-select-option value="dept2">技术部门</a-select-option>
          </a-select>
        </div>
        <div class="filter-item">
          <span>变更类型:</span>
          <a-select
            placeholder="请选择类型"
            size="small"
            style="width: 120px"
            allow-clear
          >
            <a-select-option value="type1">普通变更</a-select-option>
            <a-select-option value="type2">紧急变更</a-select-option>
          </a-select>
        </div>
        <a-button type="primary" size="small">查询</a-button>
      </div>
    </div>

    <div
      class="chart-container"
      ref="pieChartRef"
      style="flex: 1; min-height: 0"
    ></div>
  </a-card>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from "vue";
import * as echarts from "echarts/core";
import { PieChart } from "echarts/charts";
import {
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
} from "echarts/components";
import { CanvasRenderer } from "echarts/renderers";

echarts.use([
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  PieChart,
  CanvasRenderer,
]);

const pieChartRef = ref(null);

let pieChart = null;

const pieData = [
  { name: "无需立项", value: 35 },
  { name: "立项未发起", value: 15 },
  { name: "立项已审批", value: 12 },
  { name: "已下达采购", value: 18 },
  { name: "已签约", value: 10 },
  { name: "已到货", value: 10 },
];

const initChart = () => {
  if (pieChartRef.value && !pieChart) {
    pieChart = echarts.init(pieChartRef.value);
    const pieOption = {
      tooltip: {
        trigger: "item",
        formatter: "{b}: {c} ({d}%)",
      },
      legend: {
        orient: "vertical",
        icon: "circle",
        right: "5%",
        top: "middle",
        itemWidth: 8,
        itemHeight: 8,
        itemGap: 12,
        formatter: function (name) {
          const target = pieData.find((item) => item.name === name);
          return `${name}: ${target ? target.value : 0}`;
        },
        textStyle: {
          fontSize: 16,
          color: "#666",
        },
      },
      color: ["#1890ff", "#52c41a", "#a9a9a9", "#ffc53d", "#40a9ff", "#ff7a45"],
      series: [
        {
          name: "采购状态",
          type: "pie",
          radius: ["50%", "80%"],
          center: ["40%", "50%"],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 2,
            borderWidth: 2,
            borderColor: "#fff",
          },
          label: { show: false },
          labelLine: { show: false },
          emphasis: {
            label: { show: false },
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: "rgba(0, 0, 0, 0.3)",
            },
          },
          data: pieData,
        },
      ],
    };
    pieChart.setOption(pieOption);
  }
};

const resizeChart = () => {
  pieChart?.resize();
};

onMounted(() => {
  nextTick(() => {
    initChart();
  });
  window.addEventListener("resize", resizeChart);
});

onUnmounted(() => {
  window.removeEventListener("resize", resizeChart);
  pieChart?.dispose();
  pieChart = null;
});
</script>

<style scoped>
.overview-card {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
  padding: 10px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.card-title {
  display: flex;
  align-items: center;
  font-size: 17px;
  font-weight: 600;
  color: #333;
  line-height: 1.2;
}

.title-icon {
  width: 3px;
  height: 14px;
  background-color: #1677ff;
  border-radius: 2px;
  margin-right: 6px;
  flex-shrink: 0;
}

.divider {
  height: 1px;
  background-color: #f0f0f0;
  margin-top: 8px;
  margin-bottom: 8px;
}

.filter-legend-row {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 8px;
  flex-wrap: wrap;
  gap: 10px;
}

.card-filter {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  align-items: center;
  margin-bottom: 0;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #666;
}

.chart-container {
  width: 100%;
  flex: 1;
  min-height: 0;
}

:deep(.ant-card-head) {
  min-height: auto;
  padding: 0;
  border-bottom: none;
}

:deep(.ant-card-head-title) {
  padding: 0;
}

:deep(.ant-card-body) {
  padding: 0;
  display: flex;
  flex-direction: column;
  height: 100%;
  flex: 1;
  min-height: 0;
}

:deep(.ant-select-selector) {
  font-size: 12px;
}
</style>
