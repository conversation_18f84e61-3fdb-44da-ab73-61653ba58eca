<template>
  <div class="t8-preparation-section">
    <div class="section-title">
      <span class="title-icon"></span>
      T-8周未完成准备共计33项，详见附件（处理要求：无制约的及时推完成准备备注无制约，有制约的及时更新维修备注）
    </div>
    <div class="table-wrapper">
      <el-table
        :data="preparationData"
        border
        stripe
        style="width: 100%"
        :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
      >
        <el-table-column prop="workOrderNo" label="工单号" width="120" />
        <el-table-column prop="taskNo" label="任务号" width="120" />
        <el-table-column prop="title" label="工单任务标题" min-width="200" />
        <el-table-column prop="plannedStartDate" label="计划开工" width="120" />
        <el-table-column prop="plannedEndDate" label="计划完工" width="120" />
        <el-table-column prop="status" label="工单任务状态" width="120" />
        <el-table-column prop="workType" label="作业类型" width="120" />
        <el-table-column
          prop="currentApprover"
          label="当前审批人"
          width="120"
        />
        <el-table-column prop="maintenanceLevel" label="维修分级" width="100" />
        <el-table-column prop="responsibleTeam" label="责任班组" width="120" />
        <el-table-column prop="teamCode" label="工作班组代码" width="120" />
        <el-table-column prop="unit" label="机组" width="100" />
        <el-table-column
          prop="preparationPerson"
          label="工作准备人"
          width="120"
        />
      </el-table>
    </div>
  </div>
</template>

<script>
export default {
  name: "T8WeekPreparationTable",

  data() {
    return {
      preparationData: [
        {
          workOrderNo: "WO-2023-0101",
          taskNo: "T-0101",
          title: "3号机组给水泵检修",
          plannedStartDate: "2023-12-15",
          plannedEndDate: "2023-12-20",
          status: "未开始",
          workType: "计划性维修",
          currentApprover: "王主任",
          maintenanceLevel: "B级",
          responsibleTeam: "机械一班",
          teamCode: "JX-01",
          unit: "3号机组",
          preparationPerson: "刘工",
        },
        {
          workOrderNo: "WO-2023-0102",
          taskNo: "T-0102",
          title: "1号锅炉脱硫系统检查",
          plannedStartDate: "2023-12-16",
          plannedEndDate: "2023-12-25",
          status: "未开始",
          workType: "预防性维修",
          currentApprover: "张主任",
          maintenanceLevel: "A级",
          responsibleTeam: "机械二班",
          teamCode: "JX-02",
          unit: "1号机组",
          preparationPerson: "张工",
        },
        {
          workOrderNo: "WO-2023-0103",
          taskNo: "T-0103",
          title: "4号机组汽轮机轴封更换",
          plannedStartDate: "2023-12-18",
          plannedEndDate: "2023-12-28",
          status: "未开始",
          workType: "计划性维修",
          currentApprover: "李主任",
          maintenanceLevel: "A级",
          responsibleTeam: "机械三班",
          teamCode: "JX-03",
          unit: "4号机组",
          preparationPerson: "王工",
        },
        {
          workOrderNo: "WO-2023-0104",
          taskNo: "T-0104",
          title: "5号机组凝汽器管道检修",
          plannedStartDate: "2023-12-20",
          plannedEndDate: "2023-12-30",
          status: "未开始",
          workType: "预防性维修",
          currentApprover: "赵主任",
          maintenanceLevel: "B级",
          responsibleTeam: "机械一班",
          teamCode: "JX-01",
          unit: "5号机组",
          preparationPerson: "李工",
        },
        {
          workOrderNo: "WO-2023-0105",
          taskNo: "T-0105",
          title: "2号机组除氧器水位控制系统检修",
          plannedStartDate: "2023-12-22",
          plannedEndDate: "2023-12-26",
          status: "未开始",
          workType: "计划性维修",
          currentApprover: "钱主任",
          maintenanceLevel: "C级",
          responsibleTeam: "机械二班",
          teamCode: "JX-02",
          unit: "2号机组",
          preparationPerson: "赵工",
        },
        {
          workOrderNo: "WO-2023-0106",
          taskNo: "T-0106",
          title: "6号锅炉引风机检修",
          plannedStartDate: "2023-12-25",
          plannedEndDate: "2024-01-05",
          status: "未开始",
          workType: "预防性维修",
          currentApprover: "孙主任",
          maintenanceLevel: "B级",
          responsibleTeam: "机械三班",
          teamCode: "JX-03",
          unit: "6号机组",
          preparationPerson: "钱工",
        },
      ],
    };
  },
  methods: {},
};
</script>

<style scoped>
/* T-8周未完成准备区域样式 */
.t8-preparation-section {
  margin-bottom: 30px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  width: 100%;
}

.table-wrapper {
  padding: 15px;
  overflow-x: auto;
}

/* 标题样式 */
.section-title {
  padding: 15px 20px;
  font-size: 15px;
  color: #666;
  font-weight: 500;
  display: flex;
  align-items: center;
  background: linear-gradient(to right, #f8f9fc, #ffffff);
  border-left: 3px solid #1890ff;
  letter-spacing: 0.5px;
  margin-bottom: 0;
  box-shadow: 0 1px 0 rgba(0, 0, 0, 0.05);
}

.title-icon {
  display: inline-block;
  width: 4px;
  height: 16px;
  margin-right: 8px;
  background-color: #1890ff;
  border-radius: 2px;
}
</style>
