<template>
  <div class="chart-container">
    <div class="chart-title">
      <div class="title-line"></div>
      <h2>大修工期详情</h2>
    </div>
    
    <!-- 添加筛选表单区域 -->
    <div class="filter-section">
      <el-form :inline="true" :model="filterForm" class="filter-form">
        <el-form-item label="设备编号">
          <el-input v-model="filterForm.deviceNo" placeholder="请输入设备编号" clearable />
        </el-form-item>
        <el-form-item label="计划工期">
          <el-select v-model="filterForm.planDuration" placeholder="请选择" clearable>
            <el-option label="30天以内" value="30" />
            <el-option label="30-40天" value="30-40" />
            <el-option label="40天以上" value="40+" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleFilter">查询</el-button>
          <el-button @click="resetFilter">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div ref="chartRef" class="chart"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, reactive } from "vue";
import * as echarts from "echarts";

const chartRef = ref(null);
let chartInstance = null;

// 筛选表单数据
const filterForm = reactive({
  deviceNo: "",
  planDuration: "",
});

// 原始数据
const originalData = {
  xAxisData: Array(18).fill("FQ-OT107"),
  indicatorData: [97, 94, 98, 98, 95, 93, 93, 96, 95, 92, 94, 91, 97, 96, 99, 97, 95, 100],
  planWorkData: [40, 35, 30, 25, 45, 40, 40, 35, 35, 30, 35, 35, 40, 25, 35, 45, 35, 40],
  actualWorkData: [30, 25, 20, 15, 35, 30, 30, 25, 25, 20, 25, 25, 30, 15, 25, 35, 25, 30],
};

// 定义颜色常量
const COLORS = {
  PLAN_WORK: "#0099fa", // 计划工期颜色
  ACTUAL_WORK: "#6dddb1", // 实际工期颜色
  C_MEAN: "#e6b117", // C均值颜色
  INDICATOR_100: "#61deaa", // 100%指标点颜色
  INDICATOR_BELOW_100: "#ff6262", // 低于100%指标点颜色 - 三星指标率
};

// 筛选后的数据
const filteredData = reactive({
  xAxisData: [...originalData.xAxisData],
  indicatorData: [...originalData.indicatorData],
  planWorkData: [...originalData.planWorkData],
  actualWorkData: [...originalData.actualWorkData],
});

// 处理筛选
const handleFilter = () => {
  // 重置数据
  filteredData.xAxisData = [...originalData.xAxisData];
  filteredData.indicatorData = [...originalData.indicatorData];
  filteredData.planWorkData = [...originalData.planWorkData];
  filteredData.actualWorkData = [...originalData.actualWorkData];

  // 根据设备编号筛选
  if (filterForm.deviceNo) {
    const filteredIndices = filteredData.xAxisData
      .map((item, index) => item.includes(filterForm.deviceNo) ? index : -1)
      .filter(index => index !== -1);

    filteredData.xAxisData = filteredIndices.map(index => filteredData.xAxisData[index]);
    filteredData.indicatorData = filteredIndices.map(index => filteredData.indicatorData[index]);
    filteredData.planWorkData = filteredIndices.map(index => filteredData.planWorkData[index]);
    filteredData.actualWorkData = filteredIndices.map(index => filteredData.actualWorkData[index]);
  }

  // 根据计划工期筛选
  if (filterForm.planDuration) {
    const filteredIndices = filteredData.planWorkData
      .map((value, index) => {
        if (filterForm.planDuration === "30" && value <= 30) return index;
        if (filterForm.planDuration === "30-40" && value > 30 && value <= 40) return index;
        if (filterForm.planDuration === "40+" && value > 40) return index;
        return -1;
      })
      .filter(index => index !== -1);

    filteredData.xAxisData = filteredIndices.map(index => filteredData.xAxisData[index]);
    filteredData.indicatorData = filteredIndices.map(index => filteredData.indicatorData[index]);
    filteredData.planWorkData = filteredIndices.map(index => filteredData.planWorkData[index]);
    filteredData.actualWorkData = filteredIndices.map(index => filteredData.actualWorkData[index]);
  }

  // 更新图表
  updateChart();
};

// 重置筛选
const resetFilter = () => {
  filterForm.deviceNo = "";
  filterForm.planDuration = "";
  
  // 恢复原始数据
  filteredData.xAxisData = [...originalData.xAxisData];
  filteredData.indicatorData = [...originalData.indicatorData];
  filteredData.planWorkData = [...originalData.planWorkData];
  filteredData.actualWorkData = [...originalData.actualWorkData];

  // 更新图表
  updateChart();
};

// 更新图表
const updateChart = () => {
  if (!chartInstance) return;

  // 生成垂直连接线数据
  const verticalLines = [];
  filteredData.indicatorData.forEach((value, index) => {
    if (value < 100) {
      const circleTopPosition = value + 0.7;
      verticalLines.push({
        coords: [
          [index, 100],
          [index, circleTopPosition],
        ],
        lineStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: "rgba(255, 255, 255, 0.1)" },
            { offset: 1, color: "rgba(255, 255, 255, 1)" },
          ]),
          width: 1,
        },
      });
    }
  });

  // 准备100%点和低于100%点的数据
  const points100 = [];
  const pointsBelow100 = [];

  filteredData.indicatorData.forEach((value, index) => {
    if (value === 100) {
      points100.push([index, value]);
    } else {
      pointsBelow100.push([index, value]);
    }
  });

  const option = {
    backgroundColor: "transparent",
    grid: {
      left: "3%",
      right: "3%",
      bottom: "8%",
      top: "20%",
      containLabel: true,
    },
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
    },
    legend: {
      show: true,
      itemWidth: 10,
      itemHeight: 10,
      itemGap: 40,
      top: "5%",
      left: "center",
      textStyle: {
        fontSize: 12,
        color: "#fff",
      },
      data: [
        {
          name: "计划工期",
          icon: "circle",
          itemStyle: {
            color: COLORS.PLAN_WORK,
          },
        },
        {
          name: "实际工期",
          icon: "circle",
          itemStyle: {
            color: COLORS.ACTUAL_WORK,
          },
        },
        {
          name: "C均值",
          icon: "circle",
          itemStyle: {
            color: COLORS.C_MEAN,
          },
          lineStyle: {
            type: "dashed",
            width: 2,
            color: COLORS.C_MEAN,
          },
        },
        {
          name: "三星指标率",
          icon: "circle",
          itemStyle: {
            color: COLORS.INDICATOR_BELOW_100,
          },
        },
      ],
    },
    xAxis: {
      type: "category",
      data: filteredData.xAxisData,
      axisLine: {
        lineStyle: {
          color: "#1E3FB1",
        },
      },
      axisLabel: {
        color: "#fff",
        fontSize: 10,
        interval: 0,
        rotate: 0,
      },
    },
    yAxis: {
      type: "value",
      max: 100,
      splitLine: {
        show: false,
      },
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        show: true,
        color: "#fff",
      },
    },
    series: [
      {
        name: "计划工期",
        type: "bar",
        barWidth: 10,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: COLORS.PLAN_WORK },
            { offset: 0.5, color: COLORS.PLAN_WORK },
            { offset: 1, color: "rgba(0, 153, 250, 0)" },
          ]),
        },
        data: filteredData.planWorkData,
      },
      {
        name: "实际工期",
        type: "bar",
        barWidth: 10,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: COLORS.ACTUAL_WORK },
            { offset: 0.5, color: COLORS.ACTUAL_WORK },
            { offset: 1, color: "rgba(109, 221, 177, 0)" },
          ]),
        },
        data: filteredData.actualWorkData,
      },
      {
        name: "C均值",
        type: "line",
        symbol: "none",
        zlevel: 2,
        z: 2,
        lineStyle: {
          color: COLORS.C_MEAN,
          width: 2,
          type: "dashed",
        },
        markLine: {
          silent: true,
          symbol: "none",
          lineStyle: {
            color: COLORS.C_MEAN,
            width: 2,
            type: "dashed",
          },
          label: {
            show: true,
            formatter: "C均值: {c}",
            position: "end",
            color: COLORS.C_MEAN,
            fontSize: 12,
          },
          data: [{ yAxis: 3 }],
        },
        data: [],
      },
      {
        name: "垂直连接线",
        type: "lines",
        coordinateSystem: "cartesian2d",
        zlevel: 1,
        z: 1,
        effect: {
          show: false,
        },
        data: verticalLines,
        silent: true,
      },
      {
        name: "100%线",
        type: "line",
        symbol: "none",
        zlevel: 2,
        z: 2,
        lineStyle: {
          color: "#085fa0",
          width: 1,
        },
        markLine: {
          silent: true,
          symbol: "none",
          lineStyle: {
            color: "#085fa0",
            width: 1,
            type: "solid",
          },
          data: [{ yAxis: 100 }],
        },
        data: [],
      },
      {
        name: "100%点外圈",
        type: "scatter",
        symbol: "circle",
        symbolSize: 14,
        zlevel: 3,
        z: 3,
        itemStyle: {
          color: "rgba(0,0,0,0)",
          borderColor: COLORS.INDICATOR_100,
          borderWidth: 2,
        },
        data: points100,
      },
      {
        name: "100%点",
        type: "scatter",
        symbol: "circle",
        symbolSize: 8,
        zlevel: 4,
        z: 4,
        itemStyle: {
          color: COLORS.INDICATOR_100,
        },
        data: points100,
      },
      {
        name: "低于100%点外圈",
        type: "scatter",
        symbol: "circle",
        symbolSize: 14,
        zlevel: 3,
        z: 3,
        itemStyle: {
          color: "rgba(0,0,0,0)",
          borderColor: COLORS.INDICATOR_BELOW_100,
          borderWidth: 2,
        },
        data: pointsBelow100,
      },
      {
        name: "三星指标率",
        type: "scatter",
        symbol: "circle",
        symbolSize: 8,
        zlevel: 4,
        z: 4,
        itemStyle: {
          color: COLORS.INDICATOR_BELOW_100,
        },
        data: pointsBelow100,
        label: {
          show: true,
          position: "bottom",
          formatter: "{@[1]}%",
          color: "#fff",
          fontSize: 12,
          distance: 5,
        },
      },
    ],
  };

  chartInstance.setOption(option);

  // 监听图例点击事件，同步显示/隐藏外圈和100%点
  chartInstance.on("legendselectchanged", function (params) {
    const isIndicatorVisible = params.selected["三星指标率"];

    // 更新选项，同步外圈和100%点的显示/隐藏状态
    const newOption = {
      series: [
        {
          name: "垂直连接线",
          silent: true,
          lineStyle: {
            opacity: isIndicatorVisible ? 1 : 0,
          },
        },
        {
          name: "100%点外圈",
          silent: true,
          itemStyle: {
            opacity: isIndicatorVisible ? 1 : 0,
          },
        },
        {
          name: "100%点",
          silent: true,
          itemStyle: {
            opacity: isIndicatorVisible ? 1 : 0,
          },
        },
        {
          name: "低于100%点外圈",
          silent: true,
          itemStyle: {
            opacity: isIndicatorVisible ? 1 : 0,
          },
        },
      ],
    };

    chartInstance.setOption(newOption);
  });

  window.addEventListener("resize", resizeChart);
};

const resizeChart = () => {
  if (chartInstance) {
    chartInstance.resize();
  }
};

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return;

  chartInstance = echarts.init(chartRef.value);
  updateChart();
};

onMounted(() => {
  initChart();
  window.addEventListener("resize", () => {
    chartInstance?.resize();
  });
});

onUnmounted(() => {
  window.removeEventListener("resize", () => {
    chartInstance?.resize();
  });
  chartInstance?.dispose();
});
</script>

<style scoped>
.chart-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #030929;
  border-radius: 4px;
  overflow: hidden;
}

.chart-title {
  display: flex;
  align-items: center;
  padding: 10px;
  flex-shrink: 0;
}

.title-line {
  width: 4px;
  height: 20px;
  background-color: #00f0ff;
  margin-right: 8px;
}

.chart-title h2 {
  color: #00f0ff;
  font-size: 20px;
  margin: 0;
  font-weight: normal;
}

/* 添加筛选区域样式 */
.filter-section {
  padding: 10px;
  background-color: rgba(255, 255, 255, 0.05);
  margin: 0 10px;
  border-radius: 4px;
}

.filter-form {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.filter-form :deep(.el-form-item) {
  margin-bottom: 0;
}

.filter-form :deep(.el-input__wrapper),
.filter-form :deep(.el-select) {
  background-color: rgba(255, 255, 255, 0.1);
}

.filter-form :deep(.el-input__inner) {
  color: #fff;
}

.filter-form :deep(.el-form-item__label) {
  color: #fff;
}

.chart {
  flex: 1;
  width: 100%;
}
</style>
