<template>
  <div class="statistics-container">
    <!-- 标题部分 -->
    <div class="section-header">
      <div class="header-line"></div>
      <h2 class="section-title">工作统计</h2>
    </div>

    <!-- 内容区域 -->
    <div class="content-area">
      <!-- 表格标题 -->
      <div class="table-header">
        <div class="header-item work-item">工作统计</div>
        <div class="header-item count-item">数量</div>
      </div>

      <!-- 表格内容 -->
      <div class="table-content">
        <div
          class="table-row"
          v-for="(item, index) in statisticsData"
          :key="index"
        >
          <div class="row-label">{{ item.label }}</div>
          <div
            class="row-value"
            :class="{
              editing: editingIndex === index,
              editable: item.editable,
            }"
            @click="item.editable && startEditing(index)"
          >
            <span v-if="editingIndex !== index">{{ item.value }}</span>
            <input
              v-else
              type="number"
              v-model="item.value"
              @blur="finishEditing(index)"
              @keyup.enter="finishEditing(index)"
              @input="resetDebounceTimer"
              ref="editInput"
              class="edit-input"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick, onBeforeUnmount } from "vue";

// 使用数组存储统计数据，只有特定的两行可编辑
const statisticsData = ref([
  { label: "总工单数", value: 128418, editable: true },
  { label: "变更项目数", value: 1195, editable: false },
  { label: "处理公司级十大缺陷", value: 12, editable: false },
  { label: "处理生产单元十大缺陷", value: 14, editable: false },
  { label: "处理公司级十大技术问题", value: 11, editable: true },
  { label: "处理生产单元十大技术问题", value: 42, editable: false },
  { label: "大修核安全排查和承诺", value: 221, editable: false },
]);

// 当前正在编辑的行索引
const editingIndex = ref(null);
const editInput = ref(null);
let debounceTimer = null;

// 开始编辑
const startEditing = (index) => {
  if (statisticsData.value[index].editable) {
    editingIndex.value = index;
    // 等待DOM更新后聚焦输入框
    nextTick(() => {
      const inputElement = document.querySelector('.edit-input');
      if (inputElement) {
        inputElement.focus();
        inputElement.select();
      }
    });
    
    // 设置防抖计时器
    startDebounceTimer(index);
  }
};

// 完成编辑
const finishEditing = (index) => {
  // 清除防抖计时器
  clearTimeout(debounceTimer);
  
  // 打印修改后的值
  console.log(
    `修改了 "${statisticsData.value[index].label}" 的值为: ${statisticsData.value[index].value}`
  );
  editingIndex.value = null;
};

// 开始防抖计时器
const startDebounceTimer = (index) => {
  clearTimeout(debounceTimer);
  debounceTimer = setTimeout(() => {
    if (editingIndex.value === index) {
      finishEditing(index);
    }
  }, 2000); // 2秒后自动完成编辑
};

// 重置防抖计时器
const resetDebounceTimer = () => {
  if (editingIndex.value !== null) {
    startDebounceTimer(editingIndex.value);
  }
};

// 组件销毁前清除计时器
onBeforeUnmount(() => {
  clearTimeout(debounceTimer);
});
</script>

<style scoped>
.statistics-container {
  width: 100%;
  height: 100%;
  display: flex;
  padding: 10px;
  flex-direction: column;
  border-radius: 4px;
  box-sizing: border-box;
  overflow: hidden;
}

/* 标题样式 */
.section-header {
  display: flex;
  align-items: center;
  height: 5%;
  min-height: 30px;
  margin-bottom: 15px;
  flex-shrink: 0;
}

.header-line {
  width: 4px;
  height: 25px;
  background-color: #00f0ff;
  margin-right: 8px;
}

.section-title {
  color: #00f0ff;
  font-size: 25px;
  margin: 0;
  font-weight: normal;
  white-space: nowrap;
}

/* 内容区域 */
.content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  border-radius: 4px;
  overflow: hidden;
}

/* 表格标题 */
.table-header {
  display: flex;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(0, 240, 255, 0.2);
}

.header-item {
  color: #00f0ff;
  font-size: 18px;
  font-weight: bold;
}

.work-item {
  flex: 2;
  text-align: left;
  padding-left: 15px;
}

.count-item {
  flex: 1;
  text-align: center;
}

/* 表格内容 */
.table-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.table-row {
  display: flex;
  padding: 12px 15px;
  border-bottom: 1px solid rgba(0, 240, 255, 0.1);
  background-color: transparent;
  margin-bottom: 8px;
  border-radius: 4px;
  border: 1px solid rgba(0, 240, 255, 0.2);
}

.table-row:last-child {
  margin-bottom: 0;
}

.row-label {
  flex: 3;
  color: #ffffff;
  font-size: 16px;
  display: flex;
  align-items: center;
  padding-left: 15px;
}

.row-value {
  flex: 1;
  color: #ffd700; /* 金黄色 */
  font-size: 18px;
  font-weight: bold;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid transparent;
  border-radius: 6px;
  transition: all 0.2s ease;
  background-color: transparent;
  height: 24px; /* 固定高度 */
  box-sizing: border-box; /* 确保padding和border不会增加元素尺寸 */
}

.row-value.editable {
  cursor: pointer;
}

.row-value.editable:hover {
  border-color: #ffd700;
  box-shadow: 0 0 8px rgba(255, 215, 0, 0.4);
}

.row-value.editing {
  padding: 0;
  border-color: #ffd700;
  box-shadow: 0 0 8px rgba(255, 215, 0, 0.4);
}

.edit-input {
  width: 100%;
  height: 100%;
  background: transparent;
  border: none;
  color: #ffd700;
  font-size: 18px;
  font-weight: bold;
  text-align: center;
  outline: none;
  padding: 0 5px;
  margin: 0;
  border-radius: 6px;
  box-sizing: border-box;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .section-title {
    font-size: 20px;
  }

  .header-item {
    font-size: 16px;
  }

  .row-label {
    font-size: 14px;
  }

  .row-value {
    font-size: 16px;
  }
}
</style>
