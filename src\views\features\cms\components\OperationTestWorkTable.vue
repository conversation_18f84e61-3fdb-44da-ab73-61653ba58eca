<template>
  <div class="operation-test-section">
    <div class="section-title">
      <span class="title-icon"></span>
      明日机械配合运行试验工作
    </div>
    <div class="table-wrapper">
      <el-table
        :data="operationTestData"
        border
        stripe
        style="width: 100%"
        :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
      >
        <el-table-column prop="executionTime" label="执行时间" width="150" />
        <el-table-column
          prop="testCode"
          label="定期实验编码及名称"
          min-width="230"
        />
        <el-table-column
          prop="cooperationContent"
          label="配合内容"
          min-width="250"
        />
        <el-table-column
          prop="contactInfo"
          label="配合人员联系方式"
          width="160"
        />
        <el-table-column prop="responsibleTeam" label="责任班组" width="120" />
      </el-table>
    </div>
  </div>
</template>

<script>
export default {
  name: "OperationTestWorkTable",
  data() {
    return {
      operationTestData: [
        {
          executionTime: "2023-10-22 09:00",
          testCode: "DQ-SY-001：3号机组凝汽器真空度测试",
          cooperationContent:
            "配合监测凝汽器运行参数，确保真空度稳定，做好抽气系统检查",
          contactInfo: "张工 (13812345678)",
          responsibleTeam: "机械一班",
        },
        {
          executionTime: "2023-10-22 10:30",
          testCode: "DQ-SY-005：5号机组排汽温度校验",
          cooperationContent: "配合安装临时测温点，记录数据，比对控制室数据",
          contactInfo: "李工 (13923456789)",
          responsibleTeam: "机械二班",
        },
        {
          executionTime: "2023-10-22 13:00",
          testCode: "DQ-SY-010：4号机组给水泵性能试验",
          cooperationContent: "配合调整流量，记录压力变化，确认泵的运行状态",
          contactInfo: "王工 (13501234567)",
          responsibleTeam: "机械三班",
        },
        {
          executionTime: "2023-10-22 15:30",
          testCode: "DQ-SY-015：锅炉安全阀定期校验",
          cooperationContent:
            "配合检查阀门动作情况，确保排放通道畅通，记录启闭压力",
          contactInfo: "刘工 (13698765432)",
          responsibleTeam: "机械二班",
        },
        {
          executionTime: "2023-10-22 16:45",
          testCode: "DQ-SY-020：冷却水系统压力测试",
          cooperationContent:
            "配合加压和泄压操作，监测管道和阀门状态，记录压力数据",
          contactInfo: "赵工 (13876543210)",
          responsibleTeam: "机械一班",
        },
      ],
    };
  },
  methods: {},
};
</script>

<style scoped>
/* 试验工作区域样式 */
.operation-test-section {
  margin-bottom: 30px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  width: 100%;
}

.table-wrapper {
  padding: 15px;
  overflow-x: auto;
}

/* 标题样式 */
.section-title {
  padding: 15px 20px;
  font-size: 15px;
  color: #666;
  font-weight: 500;
  display: flex;
  align-items: center;
  background: linear-gradient(to right, #f8f9fc, #ffffff);
  border-left: 3px solid #1890ff;
  letter-spacing: 0.5px;
  margin-bottom: 0;
  box-shadow: 0 1px 0 rgba(0, 0, 0, 0.05);
}

.title-icon {
  display: inline-block;
  width: 4px;
  height: 16px;
  margin-right: 8px;
  background-color: #1890ff;
  border-radius: 2px;
}
</style>
