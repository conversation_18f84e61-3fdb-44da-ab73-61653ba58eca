<template>
  <div class="headerBox">
    <el-row :gutter="0" style="height: 100%">
      <el-col :span="20" class="flex-container">
        <el-icon class="iconSize"><Clock /></el-icon>
        <span class="title">大修数智管控平台</span>
      </el-col>
      <el-col :span="4">个人信息 </el-col>
    </el-row>
  </div>
</template>

<script setup></script>

<style lang="scss" scoped>
.headerBox {
  background-color: #1f2d5c;
  height: 100%;
}
.flex-container {
  padding-left: 10px;
  height: 100%;
  display: flex; /* 启用 Flexbox */
  align-items: center; /* 垂直居中 */
}
.iconSize {
  font-size: 33px;
}
.title {
  padding-left: 10px;
  display: inline-block; /* 将 span 转换为行内块元素 */
  font-weight: 600;
  font-size: 33px;
  color: #fff;
}
</style>
