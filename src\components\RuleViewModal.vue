<template>
  <a-modal
    v-model:visible="isVisible"
    :title="currentTitle"
    @ok="handleOk"
    @cancel="handleCancel"
    :footer="null"
    width="850px"
  >
    <div v-if="displayedRules.length > 0" class="modal-content-scrollable">
      <template
        v-for="(rule, index) in displayedRules"
        :key="rule.mainItem || index"
      >
        <h4>{{ rule.mainItem }}</h4>
        <p>
          <strong>主要规则:</strong> {{ rule.mainRule }} (
          {{ rule.totalScore }}分)
        </p>
        <h5>具体细则:</h5>
        <ul v-if="rule.data && rule.data.length">
          <li v-for="item in rule.data" :key="item.item">
            <strong>{{ item.item }}:</strong> {{ item.rule }}
            <span v-if="item.score"> ({{ item.score }}分)</span>
          </li>
        </ul>
        <p v-else>该主项暂无具体细则。</p>
        <a-divider v-if="index < displayedRules.length - 1"></a-divider>
      </template>
    </div>
    <div v-else-if="!isVisible">
      <p>未找到评价岗位 "{{ evaluationForNotFound }}" 的详细规则信息。</p>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, defineProps, defineExpose } from "vue";

// 定义详细的绩效规则数据 (移入组件内部)
const detailedRuleData = [
  {
    totalScore: 45,
    groupName: "大修执行指挥部",
    mainItem: "指挥部整体绩效",
    mainRule: "中国核电绩效分值*0.5 + 中核运行指数*0.5",
    data: [
      { item: "DXZX-001", rule: "按时完成大修目标", score: 10 },
      { item: "DXZX-002", rule: "安全指标达标", score: 15 },
      { item: "DXZX-003", rule: "预算控制在范围内", score: 5 },
      { item: "DXZX-004", rule: "重大风险识别与控制有效", score: 8 },
      { item: "DXZX-005", rule: "外部协调沟通顺畅", score: 7 },
      { item: "DXZX-006", rule: "内部团队协作高效", score: 5 },
    ],
  },
  {
    totalScore: 45,
    groupName: "大修执行指挥部",
    mainItem: "专项任务完成度",
    mainRule: "专项任务评价得分 * 权重",
    data: [
      { item: "DXZX-S01", rule: "专项任务A按期完成", score: 10 },
      { item: "DXZX-S02", rule: "专项任务B质量达标", score: 10 },
      { item: "DXZX-S03", rule: "专项任务C创新性应用", score: 5 },
    ],
  },
  {
    totalScore: 45,
    groupName: "大修执行指挥部",
    mainItem: "应急响应预案",
    mainRule: "预案完整性 + 演练效果",
    data: [
      { item: "DXZX-YJ01", rule: "预案覆盖所有关键风险点", score: 10 },
      {
        item: "DXZX-YJ02",
        rule: "应急演练组织有序，暴露问题得到整改",
        score: 15,
      },
    ],
  },
  {
    totalScore: 45,
    groupName: "维修部门",
    mainItem: "设备维修绩效",
    mainRule: "维修及时率*0.4 + 维修质量*0.4 + 成本控制*0.2",
    data: [
      { item: "WXBM-001", rule: "平均故障响应时间 < 2小时", score: 8 },
      { item: "WXBM-002", rule: "返修率 < 1%", score: 12 },
      { item: "WXBM-003", rule: "备件消耗符合预算", score: 5 },
      { item: "WXBM-004", rule: "预防性维护计划执行率 > 98%", score: 10 },
    ],
  },
  {
    totalScore: 45,
    groupName: "技术支持部",
    mainItem: "技术支持绩效",
    mainRule: "问题解决率*0.6 + 用户满意度*0.4",
    data: [
      { item: "JSZC-001", rule: "一级问题解决率 > 95%", score: 10 },
      { item: "JSZC-002", rule: "用户满意度调研 > 4.5分", score: 10 },
      { item: "JSZC-003", rule: "技术文档更新及时准确", score: 5 },
    ],
  },
  {
    groupName: "安全质保部",
    mainItem: "安全质量监督绩效",
    totalScore: 45,
    mainRule: "安全事件发生率 + 质量问题关闭率 + 监督检查覆盖率",
    data: [
      { item: "AQZB-001", rule: "年度无重大安全责任事件", score: 20 },
      { item: "AQZB-002", rule: "质量问题按期关闭率 > 90%", score: 15 },
      { item: "AQZB-003", rule: "现场监督检查频次达标", score: 10 },
    ],
  },
];

const isVisible = ref(false);
const currentTitle = ref("");
const currentContent = ref("");
const displayedRules = ref([]);
const evaluationForNotFound = ref("");

const handleOk = () => {
  isVisible.value = false;
  displayedRules.value = [];
};

const handleCancel = () => {
  isVisible.value = false;
  displayedRules.value = [];
};

const show = (evaluation) => {
  currentTitle.value = `绩效规则详情 - ${evaluation || ""}`;
  const foundRules = detailedRuleData.filter(
    (rule) => rule.groupName === evaluation
  );
  if (foundRules.length > 0) {
    displayedRules.value = foundRules;
    evaluationForNotFound.value = "";
  } else {
    displayedRules.value = [];
    evaluationForNotFound.value = evaluation || "未知岗位";
  }
  isVisible.value = true;
};

defineExpose({
  show,
});
</script>

<style scoped>
/* Add any specific styles for the modal if needed */
ul {
  list-style-type: disc;
  padding-left: 20px;
}
li {
  margin-bottom: 8px;
}

.modal-content-scrollable {
  max-height: 550px;
  overflow-y: auto;
  padding-right: 10px;
}
</style>
