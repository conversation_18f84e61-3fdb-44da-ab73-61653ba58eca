<template>
  <div class="tomorrow-work-section">
    <div class="section-title">
      <span class="title-icon"></span>
      机械明日自主实施工作
    </div>
    <div class="table-wrapper">
      <el-table
        :data="tomorrowWorkData"
        border
        stripe
        style="width: 100%"
        :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
      >
        <el-table-column prop="workOrderNo" label="工单号" width="120" />
        <el-table-column prop="taskNo" label="任务号" width="120" />
        <el-table-column prop="title" label="工单任务标题" min-width="200" />
        <el-table-column prop="status" label="工单任务状态" width="120" />
        <el-table-column prop="plannedStartDate" label="计划开工" width="120" />
        <el-table-column prop="plannedEndDate" label="计划完工" width="120" />
        <el-table-column prop="maintenanceLevel" label="维修分级" width="100" />
        <el-table-column prop="workType" label="作业类型" width="120" />
        <el-table-column
          prop="responsiblePerson"
          label="工作负责人"
          width="120"
        />
        <el-table-column prop="responsibleTeam" label="责任班组" width="120" />
        <el-table-column
          prop="preparationPerson"
          label="工作准备人"
          width="120"
        />
        <el-table-column prop="system" label="系统" width="120" />
      </el-table>
    </div>
  </div>
</template>

<script>
export default {
  name: "TomorrowWorkTable",
  props: {
    queryParams: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      tomorrowWorkData: [
        {
          workOrderNo: "WO-2023-0012",
          taskNo: "T-0045",
          title: "汽轮发电机凝汽器检修",
          status: "已计划",
          plannedStartDate: "2023-10-22",
          plannedEndDate: "2023-10-30",
          maintenanceLevel: "B级",
          workType: "预防性维修",
          responsiblePerson: "赵工",
          responsibleTeam: "机械一班",
          preparationPerson: "孙工",
          system: "汽轮机系统",
        },
        {
          workOrderNo: "WO-2023-0013",
          taskNo: "T-0046",
          title: "7号机组主油泵密封更换",
          status: "已审核",
          plannedStartDate: "2023-10-22",
          plannedEndDate: "2023-10-23",
          maintenanceLevel: "C级",
          workType: "故障性维修",
          responsiblePerson: "王工",
          responsibleTeam: "机械二班",
          preparationPerson: "李工",
          system: "润滑系统",
        },
        {
          workOrderNo: "WO-2023-0014",
          taskNo: "T-0047",
          title: "8号锅炉给水泵检修",
          status: "已计划",
          plannedStartDate: "2023-10-22",
          plannedEndDate: "2023-10-28",
          maintenanceLevel: "A级",
          workType: "预防性维修",
          responsiblePerson: "张工",
          responsibleTeam: "机械三班",
          preparationPerson: "刘工",
          system: "给水系统",
        },
        {
          workOrderNo: "WO-2023-0015",
          taskNo: "T-0048",
          title: "3号机组凝结水泵轴封更换",
          status: "已审核",
          plannedStartDate: "2023-10-22",
          plannedEndDate: "2023-10-24",
          maintenanceLevel: "B级",
          workType: "预防性维修",
          responsiblePerson: "钱工",
          responsibleTeam: "机械二班",
          preparationPerson: "吴工",
          system: "凝结水系统",
        },
        {
          workOrderNo: "WO-2023-0016",
          taskNo: "T-0049",
          title: "除灰系统输送管道修复",
          status: "已计划",
          plannedStartDate: "2023-10-22",
          plannedEndDate: "2023-10-25",
          maintenanceLevel: "C级",
          workType: "故障性维修",
          responsiblePerson: "陈工",
          responsibleTeam: "机械一班",
          preparationPerson: "周工",
          system: "除灰系统",
        },
      ],
    };
  },
  mounted() {},
  watch: {
    queryParams: {
      handler(newVal) {
        this.handleQueryParamsChange(newVal);
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    // 处理查询参数变化
    handleQueryParamsChange(params) {
      console.log(`${this.$options.name}组件处理查询参数:`, params);
    },
  },
};
</script>

<style scoped>
/* 明日工作区域样式 */
.tomorrow-work-section {
  margin-bottom: 30px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  width: 100%;
}

.table-wrapper {
  padding: 15px;
  overflow-x: auto;
}

/* 标题样式 */
.section-title {
  padding: 15px 20px;
  font-size: 15px;
  color: #666;
  font-weight: 500;
  display: flex;
  align-items: center;
  background: linear-gradient(to right, #f8f9fc, #ffffff);
  border-left: 3px solid #1890ff;
  letter-spacing: 0.5px;
  margin-bottom: 0;
  box-shadow: 0 1px 0 rgba(0, 0, 0, 0.05);
}

.title-icon {
  display: inline-block;
  width: 4px;
  height: 16px;
  margin-right: 8px;
  background-color: #1890ff;
  border-radius: 2px;
}
</style>
