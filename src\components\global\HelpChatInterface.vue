<template>
  <div class="help-chat-container">
    <div class="messages-area" ref="messagesAreaRef">
      <div
        v-for="message in messages"
        :key="message.id"
        class="message-bubble"
        :class="['message-' + message.type, { streaming: message.streaming }]"
      >
        <div class="message-content">
          <p style="white-space: pre-wrap" v-text="message.text"></p>
          <span v-if="message.streaming" class="typing-indicator"></span>
        </div>
      </div>
      <div v-if="isLoading" class="loading-indicator">
        <a-spin size="small" /> 正在思考...
      </div>
    </div>

    <div class="input-area">
      <a-textarea
        v-model:value="userInput"
        placeholder="请描述您的问题或输入关键词..."
        :disabled="isLoading"
        :auto-size="{ minRows: 1, maxRows: 3 }"
        @keydown.enter.prevent="handleEnterKey"
        allow-clear
        class="chat-input"
      />
      <a-button
        type="primary"
        @click="sendMessage"
        :loading="isLoading"
        :disabled="!userInput.trim()"
        class="send-button"
        title="发送消息"
      >
        <template #icon><ArrowUpOutlined /></template>
      </a-button>
    </div>
  </div>
</template>

<script setup>
import { ref, nextTick, watch } from "vue";
import {
  Textarea as ATextarea,
  Button as AButton,
  Spin as ASpin,
  message as antMessage,
} from "ant-design-vue";
import { ArrowUpOutlined } from "@ant-design/icons-vue";

const messagesAreaRef = ref(null);
const userInput = ref("");
const isLoading = ref(false);
const messages = ref([
  {
    id: Date.now(),
    type: "bot",
    text: '您好！有什么可以帮助您的吗？\n可以问我关于"截图"、"导出"或"联系方式"等问题。',
  },
]);
let messageIdCounter = Date.now();

// --- 滚动逻辑 ---
const scrollToBottom = async () => {
  await nextTick();
  if (messagesAreaRef.value) {
    messagesAreaRef.value.scrollTop = messagesAreaRef.value.scrollHeight;
  }
};

// 监听消息数组变化以自动滚动
watch(
  messages,
  () => {
    scrollToBottom();
  },
  { deep: true }
);

// --- 消息处理 ---
const addMessage = (text, type, streaming = false) => {
  messageIdCounter++;
  const newMessage = {
    id: messageIdCounter,
    type, // 'user' 或 'bot'
    text,
    streaming,
  };
  messages.value.push(newMessage);
  return newMessage; // 返回消息引用以便后续更新
};

const updateMessageText = (id, newText) => {
  const messageIndex = messages.value.findIndex((m) => m.id === id);
  if (messageIndex !== -1) {
    messages.value[messageIndex].text = newText;
    // 如需强制更新响应式，可以使用下面的代码，但直接修改ref数组通常也能工作
    // messages.value = [...messages.value];
  }
};

const finalizeBotMessage = (id) => {
  const messageIndex = messages.value.findIndex((m) => m.id === id);
  if (messageIndex !== -1) {
    messages.value[messageIndex].streaming = false;
  }
};

// --- 发送消息 ---
const sendMessage = async () => {
  const text = userInput.value.trim();
  if (!text || isLoading.value) return;
  userInput.value = "";
  console.log("sendMessage", text);
  addMessage(text, "user");
  await fetchBotResponse(text);
};

// 处理Shift+Enter换行，Enter发送
const handleEnterKey = (event) => {
  if (event.key === "Enter" && !event.shiftKey) {
    event.preventDefault(); // 阻止Enter的默认换行行为
    sendMessage();
  }
  // Shift+Enter会自然创建换行
};

// --- API交互 (模拟) ---
const fetchBotResponse = async (userText) => {
  isLoading.value = true;
  scrollToBottom(); // 添加用户消息后滚动并显示加载指示器

  // 3. 添加机器人回复的占位符
  const botMessage = addMessage("", "bot", true);

  // =============================================================
  // == 将此部分替换为您的实际API调用和流式处理 ==
  // =============================================================
  try {
    // --- 模拟开始 ---
    await new Promise((resolve) => setTimeout(resolve, 800)); // 模拟网络延迟

    // 基于输入模拟不同的回复 (非常基础的实现)
    let fullResponseText = "感谢您的提问。";
    if (userText.toLowerCase().includes("截图")) {
      fullResponseText =
        "关于截图功能：请点击表格上方的按钮。如果遇到内容不全的问题，请确保没有隐藏列或行，或联系技术支持。";
    } else if (
      userText.toLowerCase().includes("帮助") ||
      userText.toLowerCase().includes("hello")
    ) {
      fullResponseText =
        "您好！我可以为您提供关于系统操作、常见问题解答等信息。请问您具体想了解哪方面的内容？";
    } else {
      fullResponseText =
        "我正在学习中，暂时无法回答这个问题。您可以尝试换个问法，或联系人工客服获取帮助。";
    }

    // 模拟流式显示
    await streamResponseToChat(botMessage.id, fullResponseText);
    // --- 模拟结束 ---
  } catch (error) {
    console.error("Failed to fetch bot response:", error);
    antMessage.error("获取回复失败: " + error.message);
    // 更新占位消息显示错误
    updateMessageText(botMessage.id, "[抱歉，处理请求时发生错误]");
    finalizeBotMessage(botMessage.id);
  } finally {
    // 确保仅在流式传输完成后将loading设为false
    // 如果使用真实的流式传输如SSE，这可能在'done'或'error'事件中处理
    if (!window.EventSource || !userText.includes("stream")) {
      // 为真实SSE调整条件
      isLoading.value = false;
      finalizeBotMessage(botMessage.id); // 确保消息标记为非流式传输状态
    }
  }
  // =============================================================
  // == 替换部分结束 ==
  // =============================================================
};

// --- a模拟流式显示函数 ---
const streamResponseToChat = (messageId, fullText) => {
  return new Promise((resolve) => {
    let index = 0;
    const intervalTime = 30; // 字符之间的毫秒间隔

    const intervalId = setInterval(() => {
      if (index < fullText.length) {
        const currentMessage = messages.value.find((m) => m.id === messageId);
        if (currentMessage) {
          updateMessageText(messageId, currentMessage.text + fullText[index]);
          // 可能只需每隔几个字符滚动一次以提高性能
          if (index % 5 === 0) scrollToBottom();
        }
        index++;
      } else {
        clearInterval(intervalId);
        finalizeBotMessage(messageId);
        scrollToBottom(); // 最终滚动
        resolve(); // 通知完成
      }
    }, intervalTime);
  });
};
</script>

<style scoped>
.help-chat-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  background-color: #f4f7f9;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    "Helvetica Neue", Arial, sans-serif;
}

.messages-area {
  flex-grow: 1;
  overflow-y: auto;
  padding: 20px;
}

.messages-area::-webkit-scrollbar {
  width: 6px;
}
.messages-area::-webkit-scrollbar-track {
  background: #f4f7f9;
  border-radius: 3px;
}
.messages-area::-webkit-scrollbar-thumb {
  background-color: #d1dbe3;
  border-radius: 3px;
}
.messages-area::-webkit-scrollbar-thumb:hover {
  background-color: #b0becb;
}

.message-bubble {
  margin-bottom: 18px;
  max-width: 80%;
  word-wrap: break-word;
  display: flex;
  clear: both;
}

.message-content {
  padding: 12px 16px;
  border-radius: 18px;
  font-size: 14px;
  line-height: 1.6;
  position: relative;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
}

.message-user {
  margin-left: auto;
  float: right;
}
.message-user .message-content {
  background: #007bff;
  color: white;
  border-radius: 18px 18px 4px 18px;
}

.message-bot {
  margin-right: auto;
  float: left;
}
.message-bot .message-content {
  background-color: #ffffff;
  color: #333;
  border: 1px solid #e8e8e8;
  border-radius: 18px 18px 18px 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.loading-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 15px;
  color: #999;
  font-style: italic;
  font-size: 13px;
}
.loading-indicator .ant-spin {
  margin-right: 10px;
}

.input-area {
  display: flex;
  align-items: flex-end;
  padding: 15px 20px;
  border-top: 1px solid #e8e8e8;
  background-color: #ffffff;
}

.chat-input {
  flex-grow: 1;
  margin-right: 12px;
  border-radius: 18px;
  padding: 8px 15px;
  line-height: 1.5;
  max-height: 80px;
  resize: none;
}
.chat-input:focus {
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  border-color: #40a9ff;
}

.send-button {
  border-radius: 50%;
  width: 38px;
  height: 38px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  flex-shrink: 0;
}
.send-button .anticon {
  font-size: 18px;
}

.typing-indicator {
  display: inline-block;
  width: 7px;
  height: 1.1em;
  background-color: currentColor;
  animation: blink 1s step-end infinite;
  vertical-align: baseline;
  margin-left: 4px;
  border-radius: 1px;
  opacity: 0.7;
}

@keyframes blink {
  50% {
    opacity: 0;
  }
}
</style>
