<template>
  <a-modal
    :open="isOpen"
    title="新增常用工具"
    @ok="handleOk"
    @cancel="handleCancel"
    width="800px"
    okText="确定"
    cancelText="取消"
  >
    <a-form ref="formRef" :model="formState">
      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="工具名称：" name="toolName">
            <a-input
              v-model:value="formState.toolName"
              placeholder="请输入工具名称"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="型号：" name="model">
            <a-input v-model:value="formState.model" placeholder="请输入型号" />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="用途：" name="purpose">
            <a-input
              v-model:value="formState.purpose"
              placeholder="请输入用途"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="数量：" name="quantity">
            <a-input-number
              v-model:value="formState.quantity"
              style="width: 100%"
              placeholder="请输入数量"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="需求仓库：" name="warehouse">
            <a-input
              v-model:value="formState.warehouse"
              placeholder="请输入需求仓库"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="需求时间：" name="requireTime">
            <a-date-picker
              v-model:value="formState.requireTime"
              style="width: 100%"
              format="YYYY-MM-DD"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="24">
          <a-form-item label="备注：" name="remarks">
            <a-textarea
              v-model:value="formState.remarks"
              :rows="4"
              placeholder="请输入备注"
              :resize="false"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="准备情况判断：" name="prepStatus">
            <a-input
              v-model:value="formState.prepStatus"
              placeholder="请输入准备情况判断"
            />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </a-modal>
</template>

<script setup>
import { ref, reactive } from "vue";

const isOpen = ref(false);
const formRef = ref(null);

const formState = reactive({
  toolName: "",
  model: "",
  purpose: "",
  quantity: undefined,
  warehouse: "",
  requireTime: null,
  remarks: "",
  prepStatus: "",
});

const handleOk = () => {
  console.log("表单数据：", formState);
  isOpen.value = false;
};

const handleCancel = () => {
  isOpen.value = false;
};

// 暴露方法给父组件
defineExpose({
  openModal: () => {
    isOpen.value = true;
  },
});
</script>
