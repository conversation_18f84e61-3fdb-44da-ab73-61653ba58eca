<template>
  <div class="container">
    <div class="content-wrapper">
      <div class="image-container">
        <img
          v-if="showOldImage"
          :src="oldImage"
          class="main-image zoom-fade"
          @animationend="onZoomOutEnd"
        />
        <img
          v-if="showNewImage"
          :src="currentImage"
          class="main-image zoom-in"
        />
      </div>

      <div class="icons-container">
        <div-
          v-for="(url, index) in imageUrls"
          :key="index"
          class="icon"
          :class="{
            active: currentImage === url,
            disabled: isAnimating,
          }"
          @click="!isAnimating && changeImage(url)"
        >
          <img :src="url" alt="thumbnail" />
        </div->
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from "vue";

const imageUrls = [
  "https://wimg.588ku.com/gif620/21/07/05/164f992ddeecbe668d593b6a47739fb2.gif",
  "https://wimg.588ku.com/gif620/25/01/17/cda6cc55641e3cf37ff09b1a89a35d4d.gif",
  "https://wimg.588ku.com/gif620/24/02/17/593be1d3d067e84a70916e6b315f26a2.gif",
  "https://img0.baidu.com/it/u=2915911931,592907800&fm=253&fmt=auto&app=138&f=GIF?w=560&h=315",
  "https://img.zcool.cn/community/0194e85efa1e73a801215aa0f6a0cc.gif",
];

const currentImage = ref(imageUrls[0]);
const oldImage = ref("");
const isAnimating = ref(false);
const showOldImage = ref(false);
const showNewImage = ref(true);
const autoplayTimer = ref(null);
const userInteracted = ref(false);

const changeImage = (url) => {
  if (currentImage.value !== url && !isAnimating.value) {
    isAnimating.value = true;
    oldImage.value = currentImage.value;
    showOldImage.value = true;
    showNewImage.value = false;

    // 标记用户交互，暂停自动轮播
    userInteracted.value = true;
    clearTimeout(autoplayTimer.value);

    // 减少延迟时间，使新图片更快出现，避免停顿感
    setTimeout(() => {
      currentImage.value = url;
      showNewImage.value = true;
    }, 700);

    // 10秒后重新开始自动轮播
    setTimeout(() => {
      userInteracted.value = false;
      startAutoplay();
    }, 10000);
  }
};

const onZoomOutEnd = () => {
  showOldImage.value = false;
  isAnimating.value = false;
};

const startAutoplay = () => {
  clearTimeout(autoplayTimer.value);

  autoplayTimer.value = setTimeout(() => {
    if (!isAnimating.value && !userInteracted.value) {
      const currentIndex = imageUrls.indexOf(currentImage.value);
      const nextIndex = (currentIndex + 1) % imageUrls.length;
      changeImage(imageUrls[nextIndex]);
    } else {
      // 如果当前正在动画或用户交互中，稍后再尝试
      startAutoplay();
    }
  }, 10000);
};

onMounted(() => {
  startAutoplay();
});

onUnmounted(() => {
  clearTimeout(autoplayTimer.value);
});
</script>

<style scoped>
.container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

.content-wrapper {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

.image-container {
  position: relative;
  width: 500px;
  height: 500px;
}

.main-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.zoom-in {
  animation: zoomIn 0.8s cubic-bezier(0.2, 0.8, 0.2, 1) forwards;
  opacity: 0;
  transform: scale(0.8);
}

.zoom-fade {
  animation: zoomFadeOut 1s cubic-bezier(0.2, 0.8, 0.2, 1) forwards;
}

.icons-container {
  display: flex;
  gap: 10px;
  margin-top: 20px;
}

.icon {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.icon:hover:not(.disabled) {
  transform: scale(1.1);
}

.icon.active {
  border-color: #1890ff;
}

.icon.disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.icon img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

@keyframes zoomIn {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes zoomFadeOut {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  20% {
    transform: scale(1.2);
    opacity: 0.8;
  }
  40% {
    transform: scale(1.4);
    opacity: 0.6;
  }
  60% {
    transform: scale(1.6);
    opacity: 0.4;
  }
  80% {
    transform: scale(1.8);
    opacity: 0.2;
  }
  100% {
    transform: scale(2);
    opacity: 0;
  }
}
</style> 