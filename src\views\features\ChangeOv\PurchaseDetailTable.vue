<template>
  <div class="purchase-detail-container">
    <div class="search-section">
      <div class="filter-row">
        <div class="filter-item">
          <span>立项编码:</span>
          <a-input placeholder="请输入" style="width: 160px" />
        </div>
        <div class="filter-item">
          <span>立项名称:</span>
          <a-input placeholder="请输入" style="width: 160px" />
        </div>
        <div class="filter-item">
          <span>需求部门:</span>
          <a-input placeholder="请输入" style="width: 160px" />
        </div>
        <div class="filter-item">
          <span>需求申请人:</span>
          <a-input placeholder="请输入" style="width: 160px" />
        </div>
        <a-button type="primary" icon="search">查询</a-button>
      </div>
    </div>

    <a-table
      :columns="columns"
      :data-source="tableData"
      :scroll="{ x: 2000, y: 400 }"
      bordered
      size="middle"
    >
    </a-table>
  </div>
</template>

<script setup>
import { ref } from "vue";

// 表格列定义
const columns = [
  {
    title: "需求类型",
    dataIndex: "requirementType",
    width: 100,
  },
  {
    title: "申请用途",
    dataIndex: "applicationPurpose",
    width: 120,
  },
  {
    title: "需求部门",
    dataIndex: "requiringDepartment",
    width: 120,
  },
  {
    title: "需求申请人",
    dataIndex: "requester",
    width: 120,
  },
  {
    title: "是否紧急采购",
    dataIndex: "isUrgent",
    width: 120,
  },
  {
    title: "公司描述",
    dataIndex: "companyDescription",
    width: 150,
  },
  {
    title: "变更单号",
    dataIndex: "changeOrderNumber",
    width: 120,
  },
  {
    title: "采购申请编号",
    dataIndex: "purchaseRequestNumber",
    width: 150,
  },
  {
    title: "采购申请项目编号",
    dataIndex: "purchaseRequestItemNumber",
    width: 160,
  },
  {
    title: "WBS元素",
    dataIndex: "wbsElement",
    width: 120,
  },
  {
    title: "WBS描述",
    dataIndex: "wbsDescription",
    width: 150,
  },
  {
    title: "采购申请交货日期",
    dataIndex: "purchaseRequestDeliveryDate",
    width: 150,
  },
  {
    title: "分交到处室时间",
    dataIndex: "deliveryToDepartmentTime",
    width: 150,
  },
  {
    title: "立项审批完成时间",
    dataIndex: "approvalCompletionTime",
    width: 160,
  },
];

// 表格数据
const tableData = [
  {
    key: "1",
    requirementType: "常规需求",
    applicationPurpose: "设备维护",
    requiringDepartment: "运维部",
    requester: "张三",
    isUrgent: "否",
    companyDescription: "XX电力公司",
    changeOrderNumber: "CG2023-001",
    purchaseRequestNumber: "PR2023-0058",
    purchaseRequestItemNumber: "PR2023-0058-01",
    wbsElement: "P-2023-0045",
    wbsDescription: "设备维护项目",
    purchaseRequestDeliveryDate: "2023-11-15",
    deliveryToDepartmentTime: "2023-10-20",
    approvalCompletionTime: "2023-09-30",
  },
  {
    key: "2",
    requirementType: "紧急需求",
    applicationPurpose: "设备更换",
    requiringDepartment: "技术部",
    requester: "李四",
    isUrgent: "是",
    companyDescription: "XX电力公司",
    changeOrderNumber: "CG2023-002",
    purchaseRequestNumber: "PR2023-0062",
    purchaseRequestItemNumber: "PR2023-0062-02",
    wbsElement: "P-2023-0048",
    wbsDescription: "设备更换项目",
    purchaseRequestDeliveryDate: "2023-10-30",
    deliveryToDepartmentTime: "2023-10-15",
    approvalCompletionTime: "2023-10-05",
  },
  {
    key: "3",
    requirementType: "常规需求",
    applicationPurpose: "系统升级",
    requiringDepartment: "信息部",
    requester: "王五",
    isUrgent: "否",
    companyDescription: "XX电力公司",
    changeOrderNumber: "CG2023-003",
    purchaseRequestNumber: "PR2023-0071",
    purchaseRequestItemNumber: "PR2023-0071-01",
    wbsElement: "P-2023-0052",
    wbsDescription: "系统升级项目",
    purchaseRequestDeliveryDate: "2023-12-20",
    deliveryToDepartmentTime: "2023-11-25",
    approvalCompletionTime: "2023-11-10",
  },
  {
    key: "4",
    requirementType: "计划采购",
    applicationPurpose: "设备采购",
    requiringDepartment: "生产部",
    requester: "赵六",
    isUrgent: "否",
    companyDescription: "XX电力公司",
    changeOrderNumber: "CG2023-004",
    purchaseRequestNumber: "PR2023-0085",
    purchaseRequestItemNumber: "PR2023-0085-03",
    wbsElement: "P-2023-0060",
    wbsDescription: "设备采购项目",
    purchaseRequestDeliveryDate: "2024-01-15",
    deliveryToDepartmentTime: "2023-12-20",
    approvalCompletionTime: "2023-12-05",
  },
];
</script>

<style scoped>
.purchase-detail-container {
  width: 100%;
}

.search-section {
  margin-bottom: 16px;
  background-color: #f0f2f5;
  padding: 16px;
  border-radius: 4px;
}

.filter-row {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;
}

.filter-item {
  display: flex;
  align-items: center;
}

.filter-item span {
  margin-right: 8px;
  white-space: nowrap;
}

:deep(.ant-table-thead > tr > th) {
  background-color: #1e3a8a;
  color: white;
  text-align: center;
  padding: 8px;
}

:deep(.ant-table-tbody > tr > td) {
  padding: 4px 8px;
  font-size: 14px;
}

:deep(.ant-table-tbody > tr:hover > td) {
  background-color: #e6f7ff;
}
</style> 