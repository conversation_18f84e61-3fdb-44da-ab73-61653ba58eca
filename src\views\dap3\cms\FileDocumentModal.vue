<template>
  <a-modal
    v-model:visible="visible"
    title="大修准备文件列表"
    width="900px"
    :bodyStyle="{
      padding: '12px',
      maxHeight: 'calc(100vh - 180px)',
      overflow: 'auto',
    }"
    :footer="null"
  >
    <!-- 搜索区域 -->
    <div class="search-area">
      <div class="search-item">
        <span class="search-label">文档名称：</span>
        <a-input
          v-model:value="searchFileName"
          placeholder="请输入文档名称"
          style="width: 250px"
        />
      </div>
      <a-button type="primary" @click="handleSearch">搜索</a-button>
    </div>

    <!-- 文件表格 -->
    <div class="table-container">
      <a-table
        :dataSource="allFileList"
        :columns="columns"
        :pagination="{ pageSize: 15 }"
        size="small"
        bordered
        :scroll="{ y: 400 }"
      >
      </a-table>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, defineExpose } from "vue";

// 控制弹窗显示/隐藏
const visible = ref(false);

// 搜索文件名
const searchFileName = ref("");

// 表格列定义
const columns = [
  {
    title: "文档类型",
    dataIndex: "type",
    key: "type",
    width: 120,
  },
  {
    title: "文档名称",
    dataIndex: "name",
    key: "name",
  },
  {
    title: "上传日期",
    dataIndex: "uploadDate",
    key: "uploadDate",
    width: 120,
  },
  {
    title: "上传人",
    dataIndex: "uploader",
    key: "uploader",
    width: 100,
  },
  {
    title: "备注",
    dataIndex: "remark",
    key: "remark",
    width: 200,
  },
];

// 完整的文件列表，包含更多信息
const allFileList = ref([
  {
    key: "1",
    type: "PPT",
    name: "xxx-大修准备文件1.ppt",
    uploadDate: "2023-10-15",
    uploader: "张三",
    remark: "一号机组检修计划",
  },
  {
    key: "2",
    type: "PDF",
    name: "xxx-大修准备文件2.pdf",
    uploadDate: "2023-10-16",
    uploader: "李四",
    remark: "检修安全规程",
  },
  {
    key: "3",
    type: "DOC",
    name: "xxx-大修准备文件3.doc",
    uploadDate: "2023-10-17",
    uploader: "王五",
    remark: "检修物资清单",
  },
  {
    key: "4",
    type: "XLS",
    name: "xxx-大修准备汇总表.xls",
    uploadDate: "2023-10-18",
    uploader: "赵六",
    remark: "检修进度统计",
  },
  {
    key: "5",
    type: "PPT",
    name: "xxx-大修培训材料.ppt",
    uploadDate: "2023-10-19",
    uploader: "孙七",
    remark: "人员培训材料",
  },
  {
    key: "6",
    type: "PDF",
    name: "xxx-大修技术规范.pdf",
    uploadDate: "2023-10-20",
    uploader: "周八",
    remark: "技术要求说明",
  },
  {
    key: "7",
    type: "DOC",
    name: "xxx-大修外委服务协议.doc",
    uploadDate: "2023-10-21",
    uploader: "吴九",
    remark: "外部合作单位协议",
  },
  {
    key: "8",
    type: "PPT",
    name: "xxx-大修总结报告.ppt",
    uploadDate: "2023-10-22",
    uploader: "郑十",
    remark: "前期工作总结",
  },
  {
    key: "9",
    type: "PDF",
    name: "xxx-设备检修方案.pdf",
    uploadDate: "2023-10-23",
    uploader: "张三",
    remark: "设备检修方案详情",
  },
  {
    key: "10",
    type: "XLS",
    name: "xxx-大修人员排班表.xls",
    uploadDate: "2023-10-24",
    uploader: "李四",
    remark: "人员排班安排",
  },
  {
    key: "11",
    type: "DOC",
    name: "xxx-大修安全措施.doc",
    uploadDate: "2023-10-25",
    uploader: "王五",
    remark: "安全防护措施",
  },
  {
    key: "12",
    type: "PPT",
    name: "xxx-大修启动会议.ppt",
    uploadDate: "2023-10-26",
    uploader: "赵六",
    remark: "启动会议材料",
  },
  {
    key: "13",
    type: "PDF",
    name: "xxx-大修验收标准.pdf",
    uploadDate: "2023-10-27",
    uploader: "孙七",
    remark: "验收标准文档",
  },
  {
    key: "14",
    type: "XLS",
    name: "xxx-大修物资采购清单.xls",
    uploadDate: "2023-10-28",
    uploader: "周八",
    remark: "物资采购明细",
  },
  {
    key: "15",
    type: "DOC",
    name: "xxx-大修技术交底.doc",
    uploadDate: "2023-10-29",
    uploader: "吴九",
    remark: "技术交底文档",
  },
]);

// 搜索处理
const handleSearch = () => {
  console.log("搜索文件名:", searchFileName.value);
  // 实际项目中这里可以调用后端API进行搜索
};

// 打开弹窗
const openModal = () => {
  visible.value = true;
};

// 关闭弹窗
const closeModal = () => {
  visible.value = false;
};

// 暴露给父组件的方法和属性
defineExpose({
  openModal,
  closeModal,
  searchFileName,
});
</script>

<style scoped>
/* 搜索区域样式 */
.search-area {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  background-color: #f8f8f8;
  padding: 12px;
  border-radius: 4px;
}

.search-item {
  display: flex;
  align-items: center;
  margin-right: 16px;
}

.search-label {
  margin-right: 8px;
  font-weight: bold;
}

/* 固定表格容器高度 */
.table-container {
  height: 450px;
  overflow: hidden;
}

/* 确保表格内容正确显示 */
.table-container :deep(.ant-table-wrapper) {
  height: 100%;
}

.table-container :deep(.ant-spin-nested-loading) {
  height: 100%;
}

.table-container :deep(.ant-spin-container) {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.table-container :deep(.ant-table) {
  flex: 1;
  overflow: hidden;
}

.table-container :deep(.ant-table-body) {
  height: calc(100% - 55px);
  overflow-y: auto !important;
}
</style>
