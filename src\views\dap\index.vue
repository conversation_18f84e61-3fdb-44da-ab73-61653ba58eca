<template>
  <div class="parent">
    <OverhaulDurationChart class="div1" />
  </div>
</template>

<script setup>
import HeaderComponent from "./components/HeaderComponent.vue";
import OverviewComponent from "./components/OverviewComponent.vue";
import WorkStatisticsComponent from "./components/WorkStatisticsComponent.vue";
import UnreachedIndicatorsComponent from "./components/UnreachedIndicatorsComponent.vue";
import OverhaulDurationChart from "./components/OverhaulDurationChart.vue";
</script>

<style scoped>
.parent {
  width: 100%;
  height: 100vh;
}

.div1 {
  width: 100%;
  height: 100%;
}

.div2 {
  grid-area: 2 / 1 / 7 / 3;
  border-radius: 4px;
  overflow: auto;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 18px;
  background-color: rgba(255, 255, 255, 0.05);
}

.div3 {
  grid-area: 2 / 3 / 7 / 7;
  border-radius: 4px;
  overflow: auto;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 18px;
  background-color: rgba(255, 255, 255, 0.05);
}

.div4 {
  grid-area: 2 / 7 / 7 / 9;
  border-radius: 4px;
  overflow: auto;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 18px;
  background-color: rgba(255, 255, 255, 0.05);
}

.div5 {
  grid-area: 7 / 1 / 9 / 9;
  border-radius: 4px;
  overflow: auto;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 18px;
  background-color: rgba(255, 255, 255, 0.05);
}
</style>
