<template>
  <a-modal
    :visible="visible"
    :title="title"
    @ok="handleSubmit"
    @cancel="handleCancel"
    :width="700"
  >
    <a-form
      :model="formState"
      :label-col="{ span: 3}"
    >
      <a-form-item
        label="责任部门"
        name="department"
        :rules="[{ required: true, message: '请输入责任部门' }]"
      >
        <a-input
          v-model:value="formState.department"
          placeholder="请输入责任部门"
        />
      </a-form-item>

      <a-form-item
        label="科室"
        name="office"
        :rules="[{ required: true, message: '请输入科室' }]"
      >
        <a-input v-model:value="formState.office" placeholder="请输入科室" />
      </a-form-item>

      <a-form-item
        label="协调人"
        name="coordinator"
        :rules="[{ required: true, message: '请输入协调人' }]"
      >
        <a-input
          v-model:value="formState.coordinator"
          placeholder="请输入协调人"
        />
      </a-form-item>

      <a-form-item
        label="责任人"
        name="responsible"
        :rules="[{ required: true, message: '请选择责任人' }]"
      >
        <a-input-search
          v-model:value="formState.responsible"
          placeholder="请选择责任人"
          @search="showUserSelect"
          readonly
        />
      </a-form-item>

      <a-form-item
        label="处室责任人"
        name="officeResponsible"
        :rules="[{ required: true, message: '请输入处室责任人' }]"
      >
        <a-input
          v-model:value="formState.officeResponsible"
          placeholder="请输入处室责任人"
        />
      </a-form-item>

      <a-form-item label="偏差反馈" name="feedback">
        <a-textarea
          v-model:value="formState.feedback"
          placeholder="请输入偏差反馈"
          :rows="3"
        />
      </a-form-item>
    </a-form>

    <!-- 用户选择弹窗 -->
    <a-modal
      v-model:visible="userSelectVisible"
      title="选择责任人"
      @ok="handleUserSelectOk"
      @cancel="handleUserSelectCancel"
      :width="800"
    >
      <div class="search-wrapper">
        <a-form
          layout="inline"
          :style="{ display: 'flex', alignItems: 'center' }"
        >
          <a-form-item-rest>
            <div class="search-item">
              <span class="search-label">姓名：</span>
              <a-input
                v-model:value="userSearchForm.name"
                placeholder="请输入姓名"
              />
            </div>
          </a-form-item-rest>
          <a-form-item-rest>
            <div class="search-item">
              <span class="search-label">工号：</span>
              <a-input
                v-model:value="userSearchForm.workNo"
                placeholder="请输入工号"
              />
            </div>
          </a-form-item-rest>
          <a-form-item-rest>
            <a-button type="primary" @click="handleUserSearch">查询</a-button>
          </a-form-item-rest>
        </a-form>
      </div>

      <a-table
        :columns="userColumns"
        :data-source="userData"
        :pagination="false"
        :row-selection="{
          type: 'radio',
          selectedRowKeys: selectedUserKeys,
          onChange: onUserSelectChange,
        }"
        :row-key="(record) => record.id"
      >
      </a-table>
    </a-modal>
  </a-modal>
</template>

<script setup>
import { ref, reactive, defineExpose, defineEmits } from "vue";
import { message } from "ant-design-vue";

const props = defineProps({
  title: {
    type: String,
    default: "新增任务",
  },
});

const emit = defineEmits(["submit", "cancel"]);

// 表单状态
const visible = ref(false);
const formState = reactive({
  department: "",
  office: "",
  coordinator: "",
  responsible: "",
  officeResponsible: "",
  feedback: "",
});

// 用户选择相关
const userSelectVisible = ref(false);
const userSearchForm = reactive({
  name: "",
  workNo: "",
});
const userData = ref([
  { id: 1, name: "张三", workNo: "ZH001" },
  { id: 2, name: "李四", workNo: "ZH002" },
  { id: 3, name: "王五", workNo: "ZH003" },
  { id: 4, name: "赵六", workNo: "ZH004" },
  { id: 5, name: "钱七", workNo: "ZH005" },
]);
const userColumns = [
  {
    title: "姓名",
    dataIndex: "name",
    key: "name",
  },
  {
    title: "工号",
    dataIndex: "workNo",
    key: "workNo",
  },
];
const selectedUserKeys = ref([]);
const selectedUser = ref(null);

// 显示弹窗
const show = (data = null) => {
  // 重置表单
  Object.keys(formState).forEach((key) => {
    formState[key] = "";
  });

  // 如果有数据，填充表单
  if (data) {
    Object.keys(data).forEach((key) => {
      if (key in formState) {
        formState[key] = data[key];
      }
    });
  }

  visible.value = true;
};

// 隐藏弹窗
const hide = () => {
  visible.value = false;
};

// 处理表单提交
const handleSubmit = () => {
  // 表单验证
  if (!formState.department) {
    message.error("请输入责任部门");
    return;
  }
  if (!formState.office) {
    message.error("请输入科室");
    return;
  }
  if (!formState.coordinator) {
    message.error("请输入协调人");
    return;
  }
  if (!formState.responsible) {
    message.error("请选择责任人");
    return;
  }
  if (!formState.officeResponsible) {
    message.error("请输入处室责任人");
    return;
  }

  // 提交表单数据
  emit("submit", { ...formState });
  hide();
};

// 处理取消
const handleCancel = () => {
  emit("cancel");
  hide();
};

// 显示用户选择弹窗
const showUserSelect = () => {
  userSelectVisible.value = true;
  selectedUserKeys.value = [];
  selectedUser.value = null;
};

// 处理用户选择
const onUserSelectChange = (keys, rows) => {
  selectedUserKeys.value = keys;
  selectedUser.value = rows.length > 0 ? rows[0] : null;
};

// 处理用户选择确认
const handleUserSelectOk = () => {
  if (selectedUser.value) {
    formState.responsible = selectedUser.value.name;
  }
  userSelectVisible.value = false;
};

// 处理用户选择取消
const handleUserSelectCancel = () => {
  userSelectVisible.value = false;
};

// 处理用户搜索
const handleUserSearch = () => {
  console.log("搜索条件：", userSearchForm);
};

// 暴露方法给父组件
defineExpose({
  show,
  hide,
});
</script>

<style scoped>
.search-wrapper {
  margin-bottom: 16px;
  padding: 16px;
  background: #f5f7fa;
  border-radius: 4px;
}

.search-item {
  display: flex;
  align-items: center;
  margin-right: 16px;
}

.search-label {
  margin-right: 8px;
  white-space: nowrap;
}
</style>
