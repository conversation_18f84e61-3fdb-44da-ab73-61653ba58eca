<template>
  <div class="container">
    <div class="header">{{ name }}</div>
    <div class="content" ref="componentRef">
      <component
        v-if="timeBoolean"
        :is="props.componentsName"
        :width="SunWidth"
        :height="SunHeight"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, ref, onMounted, nextTick } from "vue";
const props = defineProps({
  width: {
    type: String,
    required: true,
    default: "100%", // 如果未提供宽度，则默认为 100%
  },
  height: {
    type: String,
    required: true,
    default: "100%", // 如果未提供高度，则默认为 100%
  },
  name: {
    type: String,
    required: false,
    default: "默认名字", // 如果未提供名字，则默认为 '默认名字'
  },
  componentsName: {
    type: String,
    required: true,
    default: "null", // 必须提供组件名，否则无法使用'
  },
});
const NweWidth = ref(props.width);
const NweHeight = ref(props.height);
// 动态计算子组件的宽高
const componentRef = ref(null);
const SunWidth = ref(0);
const SunHeight = ref(0);
const timeBoolean = ref(true);
// 定义组件接受的 props
onMounted(async () => {
  timeBoolean.value = false;
  if (componentRef.value) {
    await nextTick();
    SunWidth.value = componentRef.value.clientWidth;
    SunHeight.value = componentRef.value.clientHeight;
    timeBoolean.value = true;
  }
});
</script>

<style scoped>
.container {
  width: v-bind(NweWidth);
  height: v-bind(NweHeight);
  display: flex;
  flex-direction: column;
  box-sizing: border-box; /* 确保 padding 和 border 不会影响元素的总宽度和高度 */
}
.header {
  padding: 10px;
  background-color: #23a4f5;
  color: #fff;
  font-weight: bold;
  font-size: 18px;
  border-radius: 10px 10px 0 0;
}
.content {
  width: 100%;
  height: 100%;
  flex: 1 1 auto;
  border-radius: 0 0 10px 10px;
  background-color: #1b2246;
}
</style>
