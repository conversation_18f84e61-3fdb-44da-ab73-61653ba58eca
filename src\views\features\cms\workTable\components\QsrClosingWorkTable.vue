<template>
  <div class="qsr-closing-work">
    <div class="section-title">QSR完工未关闭</div>
    <el-table
      :data="qsrClosingData"
      border
      style="width: 100%"
      header-row-class-name="table-header"
    >
      <el-table-column
        prop="workOrderNo"
        label="工单号"
        width="100"
      ></el-table-column>
      <el-table-column
        prop="taskNo"
        label="任务号"
        width="100"
      ></el-table-column>
      <el-table-column
        prop="title"
        label="工单任务标题"
        min-width="180"
      ></el-table-column>
      <el-table-column
        prop="actualStart"
        label="实际开工"
        width="120"
      ></el-table-column>
      <el-table-column
        prop="actualEnd"
        label="实际完工"
        width="120"
      ></el-table-column>
      <el-table-column
        prop="currentApprover"
        label="当前审批人"
        width="120"
      ></el-table-column>
      <el-table-column prop="orderStatus" label="工单状态" width="100">
      </el-table-column>
      <el-table-column prop="taskStatus" label="工单任务状态" width="120">
      </el-table-column>
      <el-table-column
        prop="teamCode"
        label="责任班组代码"
        width="120"
      ></el-table-column>
      <el-table-column
        prop="responsibleTeam"
        label="责任班组"
        width="120"
      ></el-table-column>
      <el-table-column
        prop="workType"
        label="作业类型"
        width="100"
      ></el-table-column>
      <el-table-column prop="unit" label="机组" width="80"></el-table-column>
      <el-table-column prop="system" label="系统" width="100"></el-table-column>
      <el-table-column
        prop="overhaulCode"
        label="大修代号"
        width="100"
      ></el-table-column>
    </el-table>
  </div>
</template>

<script>
export default {
  name: "QsrClosingWorkTable",

  data() {
    return {
      qsrClosingData: [
        {
          workOrderNo: "QSR-20230501",
          taskNo: "T-10001",
          title: "QSR系统1号泵检修",
          actualStart: "2023-04-20",
          actualEnd: "2023-04-25",
          currentApprover: "李工",
          orderStatus: "待关闭",
          taskStatus: "已完工",
          teamCode: "QSR-M01",
          responsibleTeam: "QSR专项组",
          workType: "QSR专项",
          unit: "1号机",
          system: "冷却系统",
          overhaulCode: "QSR-2023",
        },
        {
          workOrderNo: "QSR-20230502",
          taskNo: "T-10002",
          title: "QSR安全阀校验",
          actualStart: "2023-04-18",
          actualEnd: "2023-04-23",
          currentApprover: "王工",
          orderStatus: "待关闭",
          taskStatus: "已完工",
          teamCode: "QSR-M02",
          responsibleTeam: "QSR专项组",
          workType: "QSR专项",
          unit: "2号机",
          system: "安全系统",
          overhaulCode: "QSR-2023",
        },
        {
          workOrderNo: "QSR-20230503",
          taskNo: "T-10003",
          title: "QSR管道支架检查",
          actualStart: "2023-04-19",
          actualEnd: "2023-04-24",
          currentApprover: "张工",
          orderStatus: "待关闭",
          taskStatus: "已完工",
          teamCode: "QSR-M03",
          responsibleTeam: "QSR专项组",
          workType: "QSR专项",
          unit: "3号机",
          system: "管道系统",
          overhaulCode: "QSR-2023",
        },
        {
          workOrderNo: "QSR-20230504",
          taskNo: "T-10004",
          title: "QSR阀门维修",
          actualStart: "2023-04-21",
          actualEnd: "2023-04-26",
          currentApprover: "刘工",
          orderStatus: "待关闭",
          taskStatus: "已完工",
          teamCode: "QSR-M01",
          responsibleTeam: "QSR专项组",
          workType: "QSR专项",
          unit: "4号机",
          system: "控制系统",
          overhaulCode: "QSR-2023",
        },
        {
          workOrderNo: "QSR-20230505",
          taskNo: "T-10005",
          title: "QSR电机检修",
          actualStart: "2023-04-22",
          actualEnd: "2023-04-27",
          currentApprover: "赵工",
          orderStatus: "待关闭",
          taskStatus: "已完工",
          teamCode: "QSR-M02",
          responsibleTeam: "QSR专项组",
          workType: "QSR专项",
          unit: "公共",
          system: "电气系统",
          overhaulCode: "QSR-2023",
        },
      ],
    };
  },
};
</script>

<style scoped>
.qsr-closing-work {
  margin-bottom: 20px;
}
.section-title {
  background-color: #f2f6fc;
  padding: 10px;
  font-weight: bold;
  border-left: 4px solid #409eff;
  margin-bottom: 10px;
}
.table-header {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 500;
}
</style>
