<template>
  <a-modal
    v-model:visible="visible"
    :title="isEdit ? '编辑文档' : viewOnly ? '查看文档' : '新增文档'"
    width="1200px"
    @ok="handleSave"
    @cancel="closeModal"
    okText="确定"
    cancelText="取消"
    :footer="viewOnly ? null : undefined"
  >
    <!-- 基本信息 -->
    <div class="form-section">
      <div class="section-title">基本信息</div>
      <div class="form-content">
        <div class="form-row">
          <div class="form-item">
            <span class="form-label">文档类型：</span>
            <div class="form-input">
              <a-input
                v-model:value="docForm.docType"
                placeholder="请输入文档类型"
                :disabled="viewOnly"
              />
            </div>
          </div>
          <div class="form-item">
            <span class="form-label">文档名称：</span>
            <div class="form-input">
              <a-input
                v-model:value="docForm.docName"
                placeholder="请输入文档名称"
                :disabled="viewOnly"
              />
            </div>
          </div>
        </div>
        <div class="form-row">
          <div class="form-item full-width">
            <span class="form-label">备注：</span>
            <div class="form-input">
              <a-textarea
                v-model:value="docForm.remark"
                placeholder="请输入备注"
                :rows="4"
                class="no-resize"
                :disabled="viewOnly"
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 附件 -->
    <div class="form-section">
      <div class="section-title">附件</div>
      <div class="upload-area" v-if="!viewOnly">
        <a-upload name="file" :multiple="true" action="/upload">
          <a-button>上传文件</a-button>
        </a-upload>
      </div>
      <a-table
        :columns="viewOnly ? viewColumns : editColumns"
        :data-source="attachments"
        :pagination="false"
        size="small"
        bordered
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'action'">
            <a-button v-if="viewOnly" type="link" @click="handleDownloadAttachment(record)">下载</a-button>
            <a-button 
              v-if="!viewOnly"
              type="link" 
              danger 
              @click="handleDeleteAttachment(record)"
            >删除</a-button>
          </template>
        </template>
      </a-table>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, reactive, defineExpose, defineEmits, computed } from "vue";

const emit = defineEmits(["reload"]);

// 控制弹窗显示/隐藏
const visible = ref(false);
// 编辑模式标志
const isEdit = ref(false);
// 查看模式标志
const viewOnly = ref(false);

// 当前操作的文档数据
const docForm = reactive({
  id: "",
  docType: "",
  docName: "",
  remark: "",
});

// 附件列表数据
const attachments = ref([]);

// 查看模式下的附件表格列定义 - 包含下载操作
const viewColumns = [
  {
    title: "文件名称",
    dataIndex: "fileName",
    key: "fileName",
    align: "center",
  },
  {
    title: "文件大小(KB)",
    dataIndex: "fileSize",
    key: "fileSize",
    width: 120,
    align: "center",
  },
  {
    title: "上传时间",
    dataIndex: "uploadTime",
    key: "uploadTime",
    width: 180,
    align: "center",
  },
  {
    title: "操作",
    dataIndex: "action",
    key: "action",
    width: 100,
    align: "center",
  },
];

// 编辑模式下的附件表格列定义 - 包含删除操作
const editColumns = [
  {
    title: "文件名称",
    dataIndex: "fileName",
    key: "fileName",
    align: "center",
  },
  {
    title: "文件大小(KB)",
    dataIndex: "fileSize",
    key: "fileSize",
    width: 120,
    align: "center",
  },
  {
    title: "上传时间",
    dataIndex: "uploadTime",
    key: "uploadTime",
    width: 180,
    align: "center",
  },
  {
    title: "操作",
    dataIndex: "action",
    key: "action",
    width: 100,
    align: "center",
  },
];

// 打开模态框 - 编辑或新增模式
const openModal = (record) => {
  viewOnly.value = false;
  
  if (record) {
    // 编辑模式
    isEdit.value = true;
    // 复制记录数据到表单
    Object.assign(docForm, {
      id: record.id,
      docType: record.docType,
      docName: record.docName,
      remark: record.remark,
    });
    
    // 如果有附件数据，则直接使用记录中的附件数据
    if (record.attachments && Array.isArray(record.attachments)) {
      attachments.value = record.attachments;
    } else {
      // 没有附件数据时，创建一个示例附件（仅用于演示）
      attachments.value = [
        {
          id: "1",
          fileName: record.docName,
          fileSize: "258",
          uploadTime: record.uploadDate,
        },
      ];
    }
  } else {
    // 新增模式
    isEdit.value = false;
    // 重置表单
    Object.assign(docForm, {
      id: "",
      docType: "",
      docName: "",
      remark: "",
    });
    attachments.value = [];
  }

  visible.value = true;
};

// 打开查看模式
const openViewModal = (record) => {
  if (!record) return;
  
  // 设置为查看模式
  viewOnly.value = true;
  isEdit.value = false;
  
  // 复制记录数据到表单
  Object.assign(docForm, {
    id: record.id,
    docType: record.docType,
    docName: record.docName,
    remark: record.remark,
  });
  
  // 如果有附件数据，则直接使用记录中的附件数据
  if (record.attachments && Array.isArray(record.attachments)) {
    attachments.value = record.attachments;
  } else {
    // 没有附件数据时，创建一个示例附件（仅用于演示）
    attachments.value = [
      {
        id: "1",
        fileName: record.docName,
        fileSize: "258",
        uploadTime: record.uploadDate,
      },
    ];
  }
  
  visible.value = true;
};

// 关闭模态框
const closeModal = () => {
  visible.value = false;
};

// 保存文档
const handleSave = () => {
  console.log("保存文档:", docForm);
  console.log("附件:", attachments.value);
  // 实际项目中需要调用API保存数据
  
  // 保存成功后关闭弹窗并刷新数据
  visible.value = false;
  emit("reload");
};

// 下载附件
const handleDownloadAttachment = (attachment) => {
  console.log("下载附件:", attachment);
  // 实际项目中需要调用API或创建下载链接
  alert(`准备下载文件: ${attachment.fileName}`);
  // 实际下载逻辑应该是:
  // window.open(attachment.downloadUrl, '_blank');
};

// 删除附件
const handleDeleteAttachment = (attachment) => {
  console.log("删除附件:", attachment);
  // 实际项目中需要调用API删除附件
  const index = attachments.value.findIndex(
    (item) => item.id === attachment.id
  );
  if (index !== -1) {
    attachments.value.splice(index, 1);
  }
};

// 向父组件暴露方法
defineExpose({
  openModal,
  openViewModal,
  closeModal,
});
</script>

<style scoped>
/* 表单样式 */
.form-section {
  margin-bottom: 24px;
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 16px;
  color: #1890ff;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 8px;
}

.form-content {
  display: flex;
  flex-direction: column;
}

.form-row {
  display: flex;
  width: 100%;
  margin-bottom: 16px;
}

.form-item {
  display: flex;
  width: 50%;
}

.form-item.full-width {
  width: 100%;
}

.form-label {
  width: 80px;
  text-align: right;
  margin-right: 8px;
  line-height: 32px;
  flex-shrink: 0;
}

.form-input {
  flex: 1;
  /* max-width: 450px; */
}

.form-item.full-width .form-input {
  max-width: 100%;
}

.no-resize {
  resize: none;
}

/* 居中表头样式 */
:deep(.ant-table-thead > tr > th) {
  text-align: center !important;
}

.upload-area {
  margin-bottom: 16px;
}
</style>
