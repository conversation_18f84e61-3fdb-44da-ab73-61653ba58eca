<template>
    <div class="parent">
        <div class="div1">
            <div class="tabs">
                <button 
                  v-for="(tab, index) in tabs" 
                  :key="index"
                  :class="['tab-btn', { active: activeTab === index }]"
                  @click="activeTab = index"
                >
                  {{ tab }}
                </button>
            </div>
        </div>
        <div class="div2">
          <Area2Content />
        </div>
        <div class="div3">
          <Area3Content />
        </div>
        <div class="div4">
          <Area4Content />
        </div>
        <div class="div5">
          <Area5Content />
        </div>
        <div class="div6">
          <Area6Content />
        </div>
        <div class="div7">
            <Area7Content/>
        </div>
        <div class="div8">
            <Area8Content/>
        </div>
        <div class="div9">
            <Area9Content/>
        </div>
        <div class="div10">
            <Area10Content/>
        </div>
        <div class="div11">
            <Area11Content/>
        </div>
    </div>
</template>

<script setup>
import { ref } from 'vue';
import Area2Content from './cms/Area2Content.vue';
import Area3Content from './cms/Area3Content.vue';
import Area4Content from './cms/Area4Content.vue';
import Area5Content from './cms/Area5Content.vue';
import Area6Content from './cms/Area6Content.vue';
import Area7Content from './cms/Area7Content.vue';
import Area8Content from './cms/Area8Content.vue';
import Area9Content from './cms/Area9Content.vue';
import Area10Content from './cms/Area10Content.vue';
import Area11Content from './cms/Area11Content.vue';

const tabs = ['准备', '实施', '总计', '工单流程'];
const activeTab = ref(0);
</script>

<style lang="scss" scoped>
.parent {
    display: grid;
    grid-template-columns: repeat(4, 1fr) repeat(2, 0.5fr) 1fr;
    grid-template-rows: auto repeat(4, 1fr);
    grid-column-gap: 10px;
    grid-row-gap: 10px;
    height: 100%;
    width: 100%;
    box-sizing: border-box;
    background-color: #10152b;
    padding: 10px;
}

    .parent > div {
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    font-weight: bold;
}

.div1 { 
    grid-area: 1 / 1 / 2 / 8; 
    background-color: transparent;
    justify-content: flex-start;
    align-items: flex-end;
    padding: 0;
}

.tabs {
    display: flex;
    gap: 2px;
    margin-bottom: 0;
}

.tab-btn {
    padding: 7px 30px;
    background-color: #192347;
    color: #e0e0e0;
    border: 1px solid #303d5f;
    border-bottom: none;
    border-radius: 6px 6px 0 0;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.3s ease, color 0.3s ease;
    outline: none;
    margin-top: 0;
    margin-bottom: 0;
}

.tab-btn:hover:not(.active) {
    background-color: #2a3b67;
    color: white;
}

.tab-btn.active {
    background-image: linear-gradient(to bottom, #018be2, #153254);
    color: white;
    border-left: 1px solid #018be2;
    border-right: 1px solid #018be2;
    border-top: 1px solid #018be2;
    border-bottom: none;
    font-weight: bold;
    z-index: 1;
}

.div2 { 
    grid-area: 2 / 1 / 3 / 3; 
   
    margin-top: -10px;
}
.div3 { 
    grid-area: 2 / 3 / 4 / 7; 

    margin-top: -10px;
}
.div4 { 
    grid-area: 2 / 7 / 4 / 8; 
    margin-top: -10px;
}
.div5 { 
    grid-area: 3 / 1 / 4 / 3; 
}
.div6 { grid-area: 4 / 1 / 5 / 3; }
.div7 { grid-area: 4 / 3 / 5 / 5;  }
.div8 { grid-area: 4 / 5 / 5 / 8;  }
.div9 { grid-area: 5 / 1 / 6 / 3; }
.div10 { grid-area: 5 / 3 / 6 / 5; }
.div11 { grid-area: 5 / 5 / 6 / 8; }
</style>