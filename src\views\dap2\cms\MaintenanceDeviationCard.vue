<template>
  <div class="maintenance-deviation-card">
    <div class="card-title">大修偏差</div>
    <div class="deviation-items">
      <div
        v-for="(item, index) in deviationItems"
        :key="index"
        class="deviation-item"
      >
        <div class="item-count">{{ item.count }}个大修</div>
        <div class="item-desc">{{ item.description }}</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue";

// 使用数组存储偏差项目数据
const deviationItems = ref([
  {
    count: 66,
    description: "共6个工作物资大修前不能到货",
  },
  {
    count: 66,
    description: "共6个合同超期未签订",
  },
  {
    count: 66,
    description: "共6个指标未达标",
  },
]);
</script>

<style scoped>
.maintenance-deviation-card {
  width: 100%;
  height: 100%;
  background-color: #030d3a;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  padding: 10px;
  box-sizing: border-box;
  border: 1px solid rgba(0, 120, 255, 0.3);
  box-shadow: 0 0 10px rgba(0, 60, 120, 0.2);
}

.card-title {
  color: #fff;
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 15px;
  position: relative;
  padding-left: 15px;
  display: flex;
  align-items: center;
}

.card-title::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 16px;
  background: linear-gradient(to bottom, #0066ff, #00ccff);
}

.deviation-items {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.deviation-item {
  background: rgba(0, 60, 120, 0.3);
  border: 1px solid rgba(0, 120, 255, 0.3);
  border-radius: 4px;
  padding: 10px 15px;
  margin-bottom: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.deviation-item:last-child {
  margin-bottom: 0;
}

.item-count {
  color: #00ccff; /* 只有数字部分使用蓝色 */
  font-size: 14px;
  font-weight: bold;
}

.item-desc {
  color: #ffffff;
  font-size: 14px;
}
</style>
