{"name": "vue-demo", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test:unit": "vitest"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@infectoone/vue-ganttastic": "^2.3.2", "ant-design-vue": "^4.2.6", "chinese-lunar": "^0.1.4", "chinese-lunar-calendar": "^1.0.1", "dayjs": "^1.11.13", "dhtmlx-gantt": "^9.0.3", "echarts": "^5.5.1", "element-plus": "^2.9.0", "esbuild": "^0.25.4", "gif.js": "^0.2.0", "gifler": "^0.1.0", "html2canvas": "^1.4.1", "libgif": "^0.0.3", "pinia": "^2.2.6", "vue": "^3.5.13", "vue-ganttastic": "^0.9.34", "vue-grid-layout-v3": "^3.1.2", "vue-router": "^4.4.5"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.1", "@vue/test-utils": "^2.4.6", "jsdom": "^25.0.1", "sass-embedded": "^1.82.0", "unplugin-auto-import": "^0.18.6", "unplugin-vue-components": "^0.27.5", "vite": "^6.0.1", "vite-plugin-vue-devtools": "^7.6.5", "vitest": "^2.1.5"}}