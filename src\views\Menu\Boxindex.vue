<template>
  <div class="common-layout">
    <el-container style="height: 100vh">
      <!-- 表头 -->
      <el-header class="ElHeader">
        <headerIndex />
      </el-header>
      <el-container>
        <!-- 导航栏 -->
        <el-aside width="200px" class="ElAside">
          <menu-index />
        </el-aside>
        <!-- 容器 -->
        <el-main class="ElMain">
          <!-- <breadcrumb /> -->
          <router-view :key="key" />
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script setup>
import MenuIndex from "@/views/Menu/MenuIndex.vue";
import headerIndex from "@/views/header/index.vue";

import { useRoute } from "vue-router";
import { computed } from "vue";

const route = useRoute();
const key = computed(() => {
  return route.path;
});
</script>

<style lang="scss" scoped>
.ElAside {
  height: 100%;
  background-color: lightcoral;
}
.ElHeader {
  padding: 0px;
}

.ElMain {
  padding: 5px;
  box-sizing: border-box;
  background-color: rgb(233, 211, 211);
  height: calc(100vh - 60px);
  margin-bottom: 15px;
}
</style>
