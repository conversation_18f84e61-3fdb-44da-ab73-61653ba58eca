<template>
  <div class="historical-maintenance-card">
    <div class="card-header">
      <div class="card-title">历史大修</div>
      <div class="total-count">{{ totalCount }}次</div>
    </div>
    <div class="maintenance-items">
      <div
        v-for="(item, index) in maintenanceItems"
        :key="index"
        class="maintenance-item"
        :class="`type-${item.type}`"
      >
        <div class="item-main-info">
          <div class="item-type">
            {{ item.typeName }}大修: {{ item.count }}次
          </div>
          <div class="item-duration">周期: {{ item.duration }}天</div>
          <div class="item-code">大修: {{ item.code }}</div>
        </div>
        <div class="item-date-range">{{ item.dateRange }}</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from "vue";

// 使用数组存储大修记录数据
const maintenanceItems = ref([
  {
    type: "a",
    typeName: "A类",
    count: 231,
    duration: 33.75,
    code: "Q3-OT111",
    dateRange: "2021/04/01-2021/05/04",
  },
  {
    type: "b",
    typeName: "B类",
    count: 231,
    duration: 25.02,
    code: "Q3-OT111",
    dateRange: "2021/04/01-2021/05/04",
  },
  {
    type: "c",
    typeName: "C类",
    count: 73,
    duration: 15.12,
    code: "Q3-OT111",
    dateRange: "2021/04/01-2021/05/04",
  },
]);

// 计算总次数
const totalCount = computed(() => {
  return maintenanceItems.value.reduce((sum, item) => sum + item.count, 0);
});
</script>

<style scoped>
.historical-maintenance-card {
  width: 100%;
  height: 100%;
  background-color: #030d3a;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  padding: 10px;
  box-sizing: border-box;
  border: 1px solid rgba(0, 120, 255, 0.3);
  box-shadow: 0 0 10px rgba(0, 60, 120, 0.2);
}

.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.card-title {
  color: #fff;
  font-size: 16px;
  font-weight: bold;
  position: relative;
  padding-left: 15px;
  display: flex;
  align-items: center;
}

.card-title::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 16px;
  background: linear-gradient(to bottom, #0066ff, #00ccff);
}

.total-count {
  color: #00ccff;
  font-size: 18px;
  font-weight: bold;
  margin-left: 10px;
}

.maintenance-items {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 10px;
  height: calc(100% - 40px);
}

.maintenance-item {
  flex: 1;
  background: rgba(0, 60, 120, 0.3);
  border: 1px solid rgba(0, 120, 255, 0.3);
  border-radius: 4px;
  padding: 10px 15px;
  margin-bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.item-main-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
}

.item-type,
.item-duration,
.item-code {
  color: #fff;
  font-size: 14px;
}

.item-date-range {
  color: #8a9ab0;
  font-size: 12px;
  text-align: right;
}

/* 不同类型的大修可以有不同的颜色标识 */
.type-a {
  border-left: 3px solid #4794ed;
}

.type-b {
  border-left: 3px solid #82cea6;
}

.type-c {
  border-left: 3px solid #ff9f43;
}
</style>
