<template>
  <div class="component-section">
    <div class="section-title">组件标题</div>
    <div class="table-wrapper">
      <el-table
        :data="tableData"
        border
        style="width: 100%"
        size="small"
        header-row-class-name="table-header"
      >
        <!-- 表格列示例 -->
        <el-table-column prop="id" label="ID" width="80"></el-table-column>
        <el-table-column
          prop="name"
          label="名称"
          min-width="180"
        ></el-table-column>
        <el-table-column prop="date" label="日期" width="120"></el-table-column>
        <el-table-column prop="status" label="状态" width="100">
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
export default {
  name: "ComponentTemplate",
  data() {
    return {
      tableData: [
        {
          id: 1,
          name: "示例数据1",
          date: "2023-10-10",
          status: "正常",
        },
        {
          id: 2,
          name: "示例数据2",
          date: "2023-10-11",
          status: "异常",
        },
      ],
    };
  },
  methods: {},
};
</script>

<style scoped>
.component-section {
  margin-bottom: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.section-title {
  background-color: #f2f6fc;
  padding: 10px;
  font-weight: bold;
  border-left: 4px solid #409eff;
  margin-bottom: 10px;
}

.table-wrapper {
  padding: 0 15px 15px;
  overflow-x: auto;
}

.table-header {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 500;
}
</style>
