<template>
  <a-modal
    v-model:visible="visible"
    :title="isEdit ? '修改阶段' : '新增阶段'"
    width="900px"
    @ok="handleSubmit"
    @cancel="handleCancel"
    :okText="isEdit ? '保存修改' : '确认添加'"
    :cancelText="'取消'"
    :maskClosable="false"
  >
    <div class="add-algorithm-container">
      <a-form
        :model="formState"
        :rules="rules"
        ref="formRef"
        :label-col="{ span: 8 }"
        :wrapper-col="{ span: 14 }"
        layout="horizontal"
      >
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="阶段名称" name="stageName">
              <a-input
                v-model:value="formState.stageName"
                placeholder="请输入阶段名称"
                allow-clear
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="排序" name="order">
              <a-input-number
                v-model:value="formState.order"
                :min="1"
                placeholder="请输入排序号码"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="到场时间" name="arrivalTime">
              <a-input
                v-model:value="formState.arrivalTime"
                placeholder="请输入到场时间(T格式)"
                prefix="T"
                allow-clear
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="到场时间偏移量" name="arrivalOffset">
              <a-input
                v-model:value="formState.arrivalOffset"
                placeholder="请输入到场时间偏移量"
                prefix="T+"
                suffix="D"
                allow-clear
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="离场时间" name="departureTime">
              <a-input
                v-model:value="formState.departureTime"
                placeholder="请输入离场时间(T格式)"
                prefix="T"
                allow-clear
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="离场时间偏移量" name="departureOffset">
              <a-input
                v-model:value="formState.departureOffset"
                placeholder="请输入离场时间偏移量"
                prefix="T+"
                suffix="D"
                allow-clear
              />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, defineEmits, defineExpose } from "vue";

const emit = defineEmits(["add-success", "edit-success"]);
const visible = ref(false);
const isEdit = ref(false);
const editingRecord = ref<any>(null);
const formRef = ref();

// 表单状态和规则
const formState = reactive({
  stageName: "",
  order: 1,
  arrivalTime: "",
  arrivalOffset: "",
  departureTime: "",
  departureOffset: "",
});

const rules = {
  stageName: [{ required: true, message: "请输入阶段名称", trigger: "blur" }],
  order: [{ required: true, message: "请输入排序", trigger: "change" }],
  arrivalTime: [{ required: true, message: "请输入到场时间", trigger: "blur" }],
  arrivalOffset: [
    { required: true, message: "请输入到场时间偏移量", trigger: "blur" },
  ],
  departureTime: [
    { required: true, message: "请输入离场时间", trigger: "blur" },
  ],
  departureOffset: [
    { required: true, message: "请输入离场时间偏移量", trigger: "blur" },
  ],
};

// 重置表单
const resetForm = () => {
  formState.stageName = "";
  formState.order = 1;
  formState.arrivalTime = "";
  formState.arrivalOffset = "";
  formState.departureTime = "";
  formState.departureOffset = "";

  if (formRef.value) {
    formRef.value.resetFields();
  }
};

// 打开添加模态框
const openAdd = (defaultOrder: number = 1) => {
  isEdit.value = false;
  resetForm();
  formState.order = defaultOrder;
  visible.value = true;
};

// 打开编辑模态框
const openEdit = (record: any) => {
  isEdit.value = true;
  editingRecord.value = { ...record };

  // 填充表单数据
  formState.stageName = record.stageName;
  formState.order = record.order;
  formState.arrivalTime = record.arrivalTime.replace("T", ""); // 移除前缀T以适配输入框
  formState.arrivalOffset = record.arrivalOffset
    .replace("T+", "")
    .replace("D", ""); // 移除前后缀
  formState.departureTime = record.departureTime.replace("T", ""); // 移除前缀T以适配输入框
  formState.departureOffset = record.departureOffset
    .replace("T+", "")
    .replace("D", ""); // 移除前后缀

  visible.value = true;
};

// 提交表单
const handleSubmit = () => {
  formRef.value
    .validate()
    .then(() => {
      // 格式化数据
      const formData = {
        stageName: formState.stageName,
        order: formState.order,
        arrivalTime: `T${formState.arrivalTime}`,
        arrivalOffset: `T+${formState.arrivalOffset}D`,
        departureTime: `T${formState.departureTime}`,
        departureOffset: `T+${formState.departureOffset}D`,
      };

      if (isEdit.value) {
        // 编辑现有记录
        emit("edit-success");
      } else {
        // 添加新记录
        emit("add-success");
      }

      // 关闭模态框
      resetForm();
      visible.value = false;
    })
    .catch((error) => {
      console.log("验证失败", error);
    });
};

// 取消
const handleCancel = () => {
  resetForm();
  visible.value = false;
};

// 暴露方法供父组件调用
defineExpose({
  openAdd,
  openEdit,
});
</script>

<style scoped>
.add-algorithm-container {
  padding: 30px 20px;
}

:deep(.ant-form-item) {
  margin-bottom: 28px;
}

:deep(.ant-form-item-label > label) {
  color: #333;
  font-weight: 500;
}

:deep(.ant-input-number-handler-wrap) {
  opacity: 1;
}
</style>
