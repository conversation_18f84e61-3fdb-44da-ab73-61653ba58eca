<template>
  <div class="area5-container">
    <div class="area5-main-title">
      重点项目
      <span class="edit-button" @click="openEditModal">修改</span>
    </div>
    <div class="category-titles">
      <div 
        v-for="(category, index) in categories" 
        :key="category"
        :class="['category-item', { active: activeCategory === index }]"
        @click="handleCategoryClick(category, index)"
      >
        {{ category }}
      </div>
    </div>
    <div class="items-wrapper">
      <div class="items-area">
        <div v-for="item in allItems" :key="item.id" class="item-block">
          <div class="item-id-circle">{{ item.id }}</div>
          <div class="item-description-box">{{ item.text }}</div>
        </div>
      </div>
    </div>
    
    <!-- 引入项目编辑弹框组件 -->
    <ProjectEditModal 
      ref="editModalRef" 
      @update="handleProjectUpdate"

    />
  </div>
</template>

<script setup>
import { ref } from 'vue';
import ProjectEditModal from './ProjectEditModal.vue';

const categories = ['核岛', '常规岛', '重大变更'];
const activeCategory = ref(0); // 默认选中"核岛"

// 弹框ref
const editModalRef = ref(null);

// 处理分类点击
const handleCategoryClick = (category, index) => {
  console.log('点击分类:', category);
  activeCategory.value = index; // 更新高亮状态
};

// 打开编辑弹框
const openEditModal = () => {
  editModalRef.value.showModal();
};

// 处理项目更新
const handleProjectUpdate = (updatedItems) => {
  console.log('更新项目:', updatedItems);
  // 更新项目数据
  allItems.value = updatedItems;
};

// 处理弹框取消
const handleModalCancel = () => {
  console.log('取消编辑');
};

// 项目数据
const allItems = ref([
  { id: '1', text: '主泵A/B轴承箱解体' },
  { id: '2', text: '02#海水穿墙管更换' },
  { id: '3', text: '停冷热交换器B解体' },
  { id: '4', text: '主泵B电机10C解体' },
  { id: '5', text: '停冷热交换器B解体' },
  { id: '6', text: '安全注射泵D解体检查' },
  { id: '7', text: '主管道阀门维修' },
  { id: '8', text: '控制棒驱动机构检修' },
  { id: '9', text: '蒸汽发生器二次侧检查' },
  { id: '10', text: '反应堆冷却泵维护' },
  { id: '11', text: '主蒸汽隔离阀检查' },
  { id: '12', text: '稳压器喷淋阀更换' },
  { id: '13', text: '余热排出系统检修' },
  { id: '14', text: '应急柴油发电机测试' },
  { id: '15', text: '核动力整定与校准' },
  { id: '16', text: '一回路管道超声检测' },
  { id: '17', text: '安全壳密封性测试' },
  { id: '18', text: '反应堆系统整体试验' },
  { id: '19', text: '非能动安全系统验证' },
  { id: '20', text: '常规岛凝汽器管道检修' }
]);
</script>

<style scoped>
.area5-container {
  background-color: #192347; 
  color: #fff;
  height: 100%;
  width: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  border-radius: 8px;
  overflow: hidden;
}

.area5-main-title {
  height: 30px;
  background-color: #61aef9;
  color: #ffffff;
  font-size: 16px;
  font-weight: 500;
  display: flex;
  align-items: center;
  padding-left: 15px;
  text-align: left;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  flex-shrink: 0;
  position: relative;
  justify-content: space-between;
  padding-right: 15px;
}

.edit-button {
  font-size: 12px;
  cursor: pointer;
  color: #ffffff;
  opacity: 0.8;
  transition: opacity 0.2s;
}

.edit-button:hover {
  opacity: 1;
}

/* 添加左侧的垂直圆柱装饰 */
.area5-main-title::before {
  content: '';
    position: absolute;
    left: 0px;
    top: 5px;
    bottom: 6px;
    width: 4px;
    border-radius: 3px;
    background: linear-gradient(
      to bottom,
     rgba(255, 255, 255, 0.9), /* 顶部接近不透明白色 */
     rgba(255, 255, 255, 0.3) /* 底部更透明 */);
}

.category-titles {
  display: flex;
  justify-content: space-around;
  text-align: center;
  font-size: 14px;
  font-weight: bold;
  padding: 0;
  height: 30px;
  flex-shrink: 0;
  position: relative;
  background-color: #192347; /* 深蓝色背景，与原型图一致 */
  /* border-bottom: 1px solid rgba(255, 255, 255, 0.1); 底部边框 */
}

.category-item {
  position: relative;
  cursor: pointer;
  color: #fff;
  padding: 0 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  flex: 1;
}

/* 分隔线 - 与原型图一致的垂直分隔线 */
.category-item:not(:last-child)::after {
  content: '';
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 1px;
  height: 40%;
  background-color: #4794ed;
}

/* 高亮样式 */
.category-item.active {
  color: #6cd6fb;
  position: relative;
}

/* 创建两端透明的底部边框 - 使用 ::before 而不是 ::after */
.category-item.active::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(to right, 
    rgba(71, 148, 237, 0), /* 左侧透明 */
    rgba(71, 148, 237, 1) 20%, /* 20%处开始全不透明 */
    rgba(71, 148, 237, 1) 80%, /* 80%处保持全不透明 */
    rgba(71, 148, 237, 0) /* 右侧透明 */
  );
}

/* 新增包装容器确保滚动区域正确约束 */
.items-wrapper {
  flex: 1;
  position: relative;
  overflow: hidden; /* 确保不会有意外的滚动 */
}

.items-area {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: grid;
  grid-template-columns: 1fr 1fr;
  overflow-y: auto;
  box-sizing: border-box;
  padding: 10px 30px;
  
  /* 滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: #4b5e8e #192347;
}

/* Webkit浏览器的滚动条样式 */
.items-area::-webkit-scrollbar {
  width: 4px;
}

.items-area::-webkit-scrollbar-track {
  background: #192347;
}

.items-area::-webkit-scrollbar-thumb {
  background-color: transparent;
  border-radius: 10px;
}

.item-block {
  display: flex;
  align-items: center;
  min-height: 30px;
  width: 85%;
  margin-bottom: 10px; /* 增加项目间距 */
  gap: 8px; /* 圆点和文本之间的间距 */
}

.item-id-circle {
  background-color: transparent; /* 镂空背景 */
  border: 1px solid #2c85c6; /* 蓝色边框 */
  color: #2c85c6; /* 字体颜色与边框一致 */
  width: 20px; 
  height: 20px; 
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 12px;
  flex-shrink: 0;
}

.item-description-box {
  /* 渐变背景 - 从左边透明到右边实色 */
  background: linear-gradient(to right, rgba(42, 86, 143, 0.1), rgba(42, 86, 143, 1));
  color: #fff;
  font-size: 12px;
  text-align: center; 
  width: 100%; 
  font-weight: 400;
  box-sizing: border-box;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 2px; /* 添加轻微圆角 */
}
</style> 