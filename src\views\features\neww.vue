<template>
  <div class="dynamic-header-table">
    <!-- 添加下载按钮 -->
    <div class="table-header">
      <a-button type="primary" @click="downloadTableImage" :loading="loading">
        导出完整表格图片
      </a-button>
    </div>
    
    <!-- 使用简单结构 -->
    <div class="table-container">
      <a-table 
        ref="tableRef"
        :dataSource="tableData" 
        :columns="columns" 
        :scroll="scrollConfig"
        :pagination="paginationConfig"
        bordered
      >
        <template #bodyCell="{ column, index }">
          <template v-if="column.dataIndex === 'index'">
            {{ index + 1 }}
          </template>
        </template>
      </a-table>
    </div>

    <!-- 专门用于截图的隐藏表格容器 -->
    <div id="hiddenTableContainer" style="display: none;"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive } from "vue";
import { Table, Button, message } from "ant-design-vue";
import html2canvas from "html2canvas";

// 生成模拟数据
const generateMockData = (count: number) => {
  const data = [];
  for (let i = 0; i < count; i++) {
    data.push({
      key: i,
      name: `用户${i}`,
      id: `ID${i}`,
      age: Math.floor(Math.random() * 50) + 20,
      address: `地址 ${i}`,
      phone: `1${Math.floor(Math.random() * 1000000000)}`,
      email: `user${i}@example.com`,
      department: `部门${i % 5}`,
      position: `职位${i % 8}`,
      salary: Math.floor(Math.random() * 10000) + 5000,
      status: i % 2 === 0 ? "在职" : "离职",
      entryDate: `2024-${String(Math.floor(Math.random() * 12) + 1).padStart(
        2,
        "0"
      )}-${String(Math.floor(Math.random() * 28) + 1).padStart(2, "0")}`,
      level: `Level ${Math.floor(Math.random() * 5) + 1}`,
      performance: Math.floor(Math.random() * 100),
      projects: Math.floor(Math.random() * 10),
      skills: `技能${i % 10}`,
      education: `学历${i % 4}`,
      experience: `${Math.floor(Math.random() * 10)}年`,
      certificate: `证书${i % 6}`,
      remarks: `备注信息${i}`,
    });
  }
  return data;
};

// 定义列配置
const columns = [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    width: 80,
    fixed: "left",
    align: "center",
  },
  {
    title: "姓名",
    dataIndex: "name",
    key: "name",
    width: 120,
    fixed: "left",
  },
  { title: "ID", dataIndex: "id", key: "id", width: 100 },
  { title: "年龄", dataIndex: "age", key: "age", width: 80 },
  { title: "地址", dataIndex: "address", key: "address", width: 200 },
  { title: "电话", dataIndex: "phone", key: "phone", width: 150 },
  { title: "邮箱", dataIndex: "email", key: "email", width: 200 },
  { title: "部门", dataIndex: "department", key: "department", width: 120 },
  { title: "职位", dataIndex: "position", key: "position", width: 120 },
  { title: "薪资", dataIndex: "salary", key: "salary", width: 120 },
  { title: "状态", dataIndex: "status", key: "status", width: 100 },
  { title: "入职日期", dataIndex: "entryDate", key: "entryDate", width: 120 },
  { title: "级别", dataIndex: "level", key: "level", width: 100 },
  { title: "绩效", dataIndex: "performance", key: "performance", width: 100 },
  { title: "项目数", dataIndex: "projects", key: "projects", width: 100 },
  { title: "技能", dataIndex: "skills", key: "skills", width: 120 },
  { title: "学历", dataIndex: "education", key: "education", width: 120 },
  { title: "工作经验", dataIndex: "experience", key: "experience", width: 120 },
  { title: "证书", dataIndex: "certificate", key: "certificate", width: 120 },
  { title: "备注", dataIndex: "remarks", key: "remarks", width: 150 },
];

const tableData = ref([]);
const tableRef = ref(null);
const loading = ref(false);

// 响应式配置
const scrollConfig = reactive({ x: '2400px', y: '600px' });
const paginationConfig = reactive({
  total: 0,
  pageSize: 50,
  current: 1,
  showSizeChanger: true,
  showQuickJumper: true,
  pageSizeOptions: ["50", "100", "200", "500"],
  showTotal: (total: number) => `共 ${total} 条数据`,
});

// 全新的下载函数 - 创建纯HTML表格
const downloadTableImage = async () => {
  loading.value = true;
  message.loading("准备表格数据...", 0);
  
  try {
    // 0. 获取容器用于放置临时表格
    const hiddenContainer = document.getElementById('hiddenTableContainer');
    if (!hiddenContainer) {
      throw new Error("无法找到隐藏容器");
    }
    
    // 1. 准备数据和列信息
    const data = tableData.value;
    const cols = columns;
    
    // 2. 创建原生HTML表格
    message.loading("创建HTML表格...", 0);
    let htmlTable = document.createElement('table');
    htmlTable.className = 'export-table';
    htmlTable.border = '1';
    htmlTable.style.width = '100%';
    htmlTable.style.borderCollapse = 'collapse';
    
    // 3. 创建表头
    let thead = document.createElement('thead');
    let headerRow = document.createElement('tr');
    
    cols.forEach(col => {
      let th = document.createElement('th');
      th.textContent = col.title as string;
      th.style.padding = '8px';
      th.style.backgroundColor = '#f5f5f5';
      th.style.border = '1px solid #ddd';
      th.style.textAlign = 'center';
      th.style.fontWeight = 'bold';
      headerRow.appendChild(th);
    });
    
    thead.appendChild(headerRow);
    htmlTable.appendChild(thead);
    
    // 4. 创建表体
    let tbody = document.createElement('tbody');
    
    data.forEach((row, rowIndex) => {
      let tr = document.createElement('tr');
      tr.style.backgroundColor = rowIndex % 2 === 0 ? '#fff' : '#f9f9f9';
      
      cols.forEach((col, colIndex) => {
        let td = document.createElement('td');
        td.style.padding = '8px';
        td.style.border = '1px solid #ddd';
        
        // 处理序号列
        if (col.dataIndex === 'index') {
          td.textContent = String(rowIndex + 1);
          td.style.textAlign = 'center';
        } 
        // 处理普通数据列
        else if (col.dataIndex && col.dataIndex in row) {
          td.textContent = String(row[col.dataIndex]);
        }
        
        tr.appendChild(td);
      });
      
      tbody.appendChild(tr);
    });
    
    htmlTable.appendChild(tbody);
    
    // 5. 将表格添加到隐藏容器
    hiddenContainer.innerHTML = '';
    hiddenContainer.style.display = 'block'; // 必须可见才能截图
    hiddenContainer.style.position = 'fixed';
    hiddenContainer.style.top = '0';
    hiddenContainer.style.left = '0';
    hiddenContainer.style.width = '3000px'; // 足够宽以容纳所有列
    hiddenContainer.style.height = 'auto';
    hiddenContainer.style.zIndex = '-9999'; // 确保在其他元素下方
    hiddenContainer.style.backgroundColor = '#fff';
    hiddenContainer.style.padding = '20px';
    hiddenContainer.style.overflow = 'visible';
    
    hiddenContainer.appendChild(htmlTable);
    
    // 6. 等待表格渲染
    message.loading("等待表格渲染...", 0);
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 7. 截图
    message.loading("正在生成图片...", 0);
    const canvas = await html2canvas(hiddenContainer, {
      backgroundColor: "#ffffff",
      useCORS: true,
      scale: 1.5,
      logging: true,
      width: htmlTable.offsetWidth + 40, // 加上padding
      height: htmlTable.offsetHeight + 40,
      allowTaint: true,
    });
    
    // 8. 下载
    const imageUrl = canvas.toDataURL("image/png");
    const link = document.createElement("a");
    link.download = `表格导出_${new Date().getTime()}.png`;
    link.href = imageUrl;
    link.click();
    
    message.success("导出成功！");
    
  } catch (error) {
    console.error("导出失败:", error);
    message.error(`导出失败: ${error.message}`);
  } finally {
    // 9. 清理
    const hiddenContainer = document.getElementById('hiddenTableContainer');
    if (hiddenContainer) {
      hiddenContainer.style.display = 'none';
      hiddenContainer.innerHTML = '';
    }
    
    loading.value = false;
    message.destroy();
  }
};

onMounted(() => {
  // 生成测试数据
  tableData.value = generateMockData(500);
  paginationConfig.total = tableData.value.length;
});
</script>

<style>
/* 导出表格样式 */
.export-table {
  width: 100%;
  border-collapse: collapse;
  font-family: Arial, sans-serif;
  font-size: 14px;
}

.export-table th, 
.export-table td {
  border: 1px solid #ddd;
  padding: 8px;
  text-align: left;
}

.export-table th {
  background-color: #f5f5f5;
  font-weight: bold;
  text-align: center;
}

.export-table tr:nth-child(even) {
  background-color: #f9f9f9;
}
</style>

<style scoped>
.dynamic-header-table {
  padding: 16px;
  height: calc(100vh - 100px);
  background: #fff;
}

.table-header {
  margin-bottom: 16px;
}

.table-container {
  width: 100%;
  height: calc(100% - 50px);
  overflow: hidden;
}
</style>
