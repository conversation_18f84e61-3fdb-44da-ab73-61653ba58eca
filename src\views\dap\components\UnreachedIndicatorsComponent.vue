<template>
  <div class="indicators-container">
    <!-- 标题部分 -->
    <div class="section-header">
      <div class="header-line"></div>
      <h2 class="section-title">未达三星指标</h2>
    </div>

    <!-- 内容区域 -->
    <div class="content-area">
      <!-- 顶部统计 -->
      <div class="stats-section">
        <div class="chart-container">
          <div class="chart-icon">
            <div class="chart-inner"></div>
          </div>
        </div>
        <div class="stats-summary">
          <div class="stats-item">
            <div class="star-icon">★★</div>
            <div class="stats-count">
              {{ stats.twoStar }}<span class="stats-unit">个</span>
            </div>
          </div>
          <div class="stats-item">
            <div class="star-icon">★</div>
            <div class="stats-count">
              {{ stats.oneStar }}<span class="stats-unit">个</span>
            </div>
          </div>
          <div class="stats-item">
            <div class="star-icon no-star">无星</div>
            <div class="stats-count">
              {{ stats.noStar }}<span class="stats-unit">个</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 详细指标列表 -->
      <div class="indicators-list">
        <div
          v-for="(indicator, index) in indicators"
          :key="index"
          class="indicator-item"
        >
          <div class="indicator-header">{{ indicator.code }}</div>
          <div class="indicator-content">
            <div
              v-for="(event, eventIndex) in indicator.events"
              :key="eventIndex"
              class="indicator-row"
            >
              <div class="indicator-name">{{ event.name }}</div>
              <div class="indicator-value">
                {{ event.value }}
                <span class="star-level"
                  >{{ event.unit }}
                  <span v-if="event.stars !== undefined"
                    >，
                    <span v-if="event.stars > 0" class="indicator-unit">{{
                      event.stars
                    }}</span>
                    <span v-if="event.stars === 0" class="indicator-unit"
                      >无</span
                    >
                    星
                  </span>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue";

// 统计数据
const stats = ref({
  twoStar: 8,
  oneStar: 2,
  noStar: 0,
});

// 指标数据
const indicators = ref([
  {
    code: "QF-OT107",
    events: [
      { name: "中国核电A类人因事件", value: "1", unit: "起", stars: 0 },
      { name: "大修工期偏差", value: "7.48", unit: "天", stars: 0 },
    ],
  },
  {
    code: "QF-OT207",
    events: [
      { name: "集体计量", value: "739.78", unit: "man.mSv", stars: 1 },
      { name: "集体计量", value: "739.78", unit: "man.mSv", stars: 1 },
      { name: "集体计量", value: "739.78", unit: "man.mSv", stars: 1 },
    ],
  },
  {
    code: "HN-OT206",
    events: [
      { name: "走错间隔", value: "1", unit: "起" },
      { name: "大修工期偏差", value: "1.09", unit: "天", stars: 2 },
    ], 
  },
  {
    code: "QF-OT307",
    events: [
      { name: "设备故障", value: "2", unit: "次", stars: 1 },
      { name: "安全系统可用性", value: "98.5", unit: "%", stars: 2 },
    ],
  },
]);
</script>

<style lang="scss" scoped>
.indicators-container {
  width: 100%;
  height: 100%;
  display: flex;
  padding: 10px;
  flex-direction: column;
  border-radius: 4px;
  box-sizing: border-box;
  overflow: hidden;
}

/* 标题样式 */
.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  flex-shrink: 0;
}

.header-line {
  width: 4px;
  height: 25px;
  background-color: #00f0ff;
  margin-right: 8px;
}

.section-title {
  color: #00f0ff;
  font-size: 25px;
  margin: 0;
  font-weight: normal;
  white-space: nowrap;
}

/* 内容区域 */
.content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  height: 100%;
}

/* 顶部统计部分 */
.stats-section {
  display: flex;
  height: 30%;
  flex-shrink: 0; /* 防止压缩 */
  margin-bottom: 10px;
}

.chart-container {
  flex: 2;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.chart-icon {
  width: 80px; /* 减小尺寸 */
  height: 80px; /* 减小尺寸 */
  background: radial-gradient(
    circle,
    rgba(0, 240, 255, 0.3) 0%,
    rgba(0, 240, 255, 0.1) 70%,
    transparent 100%
  );
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.chart-icon::before,
.chart-icon::after {
  content: "";
  position: absolute;
  width: 100px; /* 减小尺寸 */
  height: 100px; /* 减小尺寸 */
  border: 2px solid rgba(0, 240, 255, 0.2);
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.chart-icon::after {
  animation-delay: 1s;
}

@keyframes pulse {
  0% {
    transform: scale(0.8);
    opacity: 0.8;
  }
  70% {
    transform: scale(1.1);
    opacity: 0;
  }
  100% {
    transform: scale(1.2);
    opacity: 0;
  }
}

.chart-inner {
  width: 50px; /* 减小尺寸 */
  height: 50px; /* 减小尺寸 */
  background-color: rgba(0, 240, 255, 0.2);
  border: 2px solid rgba(0, 240, 255, 0.5);
  border-radius: 12px;
  transform: rotate(45deg);
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.chart-inner::before {
  content: "";
  position: absolute;
  width: 35px; /* 减小尺寸 */
  height: 6px; /* 减小尺寸 */
  background-color: #00f0ff;
  border-radius: 3px;
  box-shadow: 0 0 10px #00f0ff;
}

.chart-inner::after {
  content: "";
  position: absolute;
  width: 6px; /* 减小尺寸 */
  height: 35px; /* 减小尺寸 */
  background-color: #00f0ff;
  border-radius: 3px;
  box-shadow: 0 0 10px #00f0ff;
}

.stats-summary {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding-right: 10px;
}

.stats-item {
  display: flex;
  align-items: center;
  justify-content: space-around;
  margin-bottom: 8px; /* 减小间距 */
}

.stats-item:last-child {
  margin-bottom: 0;
}

.star-icon {
  color: #ffd700; /* 金黄色 */
  font-size: 18px;
}

.no-star {
  color: #ffffff;
  font-size: 16px;
}

.stats-count {
  color: #ffd700; /* 金黄色 */
  font-size: 22px;
  font-weight: bold;
}

.stats-unit {
  font-size: 16px;
  margin-left: 2px;
}

/* 指标列表 */
.indicators-list {
  height: 70%;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  padding-right: 5px; /* 为滚动条留出空间 */
}

.indicators-list::-webkit-scrollbar {
  width: 6px;
}

.indicators-list::-webkit-scrollbar-track {
  background: rgba(0, 240, 255, 0.05);
  border-radius: 3px;
}

.indicators-list::-webkit-scrollbar-thumb {
  background: rgba(0, 240, 255, 0.3);
  border-radius: 3px;
}

.indicators-list::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 240, 255, 0.5);
}

.indicator-item {
  margin-bottom: 10px;
  border-radius: 4px;
  overflow: hidden;
  flex-shrink: 0; /* 防止项目被压缩 */
}

.indicator-header {
  background-color: rgba(0, 240, 255, 0.2);
  color: #00f0ff;
  font-size: 16px;
  font-weight: bold;

  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
  text-align: center; /* 居中显示 */
}

.indicator-content {
  background-color: rgba(0, 240, 255, 0.1);
  padding: 10px 15px;
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
}

.indicator-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
}

.indicator-row:last-child {
  margin-bottom: 0;
}

.indicator-name {
  color: #ffffff;
  font-size: 14px;
}

.indicator-value {
  color: #ffd700; /* 金黄色 */
  font-size: 16px;
  font-weight: bold;
}

.indicator-unit {
  font-size: 14px;
  color: #ffd700;
}

.star-level {
  color: #ffffff;
  font-size: 14px;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .section-title {
    font-size: 20px;
  }

  .star-icon {
    font-size: 16px;
  }

  .stats-count {
    font-size: 18px;
  }

  .indicator-header {
    font-size: 14px;
  }

  .indicator-name {
    font-size: 12px;
  }

  .indicator-value {
    font-size: 14px;
  }
}
</style>
