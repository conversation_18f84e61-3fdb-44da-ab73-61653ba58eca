<template>
  <a-modal
    v-model:visible="visible"
    :title="isEdit ? '修改大修任务' : '新增大修任务'"
    width="850px"
    @ok="handleSubmit"
    @cancel="handleCancel"
    :confirmLoading="submitLoading"
  >
    <a-form
      ref="formRef"
      :model="addForm"
      layout="vertical"
      :rules="rules"
      @finish="onFinish"
    >
      <div class="form-row">
        <a-form-item class="form-item" label="任务属性" name="taskType">
          <a-select
            v-model:value="addForm.taskType"
            placeholder="请选择任务属性"
            size="small"
          >
            <a-select-option value="标准大修任务">标准大修任务</a-select-option>
            <a-select-option value="非标准大修任务"
              >非标准大修任务</a-select-option
            >
            <a-select-option value="其他任务">其他任务</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item class="form-item" label="处室" name="department">
          <a-select
            v-model:value="addForm.department"
            placeholder="请选择处室"
            size="small"
          >
            <a-select-option
              v-for="item in departmentOptions"
              :key="item.value"
              :value="item.label"
            >
              {{ item.label }}
            </a-select-option>
          </a-select>
        </a-form-item>
      </div>

      <div class="form-row">
        <a-form-item class="form-item" label="任务名称" name="taskName">
          <a-input
            v-model:value="addForm.taskName"
            placeholder="请输入任务名称"
            size="small"
          />
        </a-form-item>

        <a-form-item class="form-item" label="窗口" name="window">
          <a-input
            v-model:value="addForm.window"
            placeholder="请输入窗口"
            size="small"
          />
        </a-form-item>
      </div>

      <div class="form-row">
        <a-form-item
          class="form-item"
          label="人员要求"
          name="personnelRequirement"
        >
          <a-input
            v-model:value="addForm.personnelRequirement"
            placeholder="请输入人员要求"
            size="small"
          />
        </a-form-item>

        <a-form-item class="form-item" label="到场时间" name="startTime">
          <a-input
            v-model:value="addForm.startTime"
            placeholder="例如: 1T+3D"
            size="small"
          />
        </a-form-item>
      </div>

      <div class="form-row">
        <a-form-item class="form-item" label="离场时间" name="endTime">
          <a-input
            v-model:value="addForm.endTime"
            placeholder="例如: 2T+5D"
            size="small"
          />
        </a-form-item>

        <a-form-item class="form-item" label="标准人数" name="standardCount">
          <a-input-number
            v-model:value="addForm.standardCount"
            :min="1"
            placeholder="请输入标准人数"
            style="width: 100%"
            size="small"
          />
        </a-form-item>
      </div>

      <div class="form-row">
        <a-form-item class="form-item" label="已配置" name="configuredCount">
          <a-input-number
            v-model:value="addForm.configuredCount"
            :min="0"
            placeholder="请输入已配置人数"
            style="width: 100%"
            size="small"
          />
        </a-form-item>

        <div class="form-item"></div>
        <!-- 空占位 -->
      </div>

      <a-form-item label="备注" name="remark">
        <a-textarea
          v-model:value="addForm.remark"
          placeholder="请输入备注信息"
          :rows="3"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, defineExpose, reactive, defineEmits } from "vue";

// 定义事件
const emit = defineEmits(["add-success", "edit-success"]);

// 表单引用
const formRef = ref(null);

// 模态框可见性状态
const visible = ref(false);
const submitLoading = ref(false);
const isEdit = ref(false);
const editId = ref("");

// 处室选项
const departmentOptions = [
  { value: "dept1", label: "运行处" },
  { value: "dept2", label: "技术处" },
  { value: "dept3", label: "安全处" },
];

// 验证规则
const rules = {
  taskType: [{ required: true, message: "请选择任务属性" }],
  department: [{ required: true, message: "请选择处室" }],
  taskName: [{ required: true, message: "请输入任务名称" }],
  window: [{ required: true, message: "请输入窗口" }],
  personnelRequirement: [{ required: true, message: "请输入人员要求" }],
  startTime: [{ required: true, message: "请输入到场时间" }],
  endTime: [{ required: true, message: "请输入离场时间" }],
  standardCount: [
    { required: true, message: "请输入标准人数" },
    { type: "number", min: 1, message: "标准人数必须大于0" },
  ],
  configuredCount: [
    { required: true, message: "请输入已配置人数" },
    { type: "number", min: 0, message: "已配置人数不能为负" },
  ],
};

// 任务表单数据
const addForm = reactive({
  taskType: "",
  department: "",
  taskName: "",
  window: "",
  personnelRequirement: "",
  startTime: "",
  endTime: "",
  standardCount: 1,
  configuredCount: 0,
  remark: "",
});

// 打开模态框的方法 - 新增模式
const open = () => {
  isEdit.value = false;
  editId.value = "";
  resetForm();
  visible.value = true;
};

// 打开模态框的方法 - 编辑模式
const openEdit = (record) => {
  isEdit.value = true;
  editId.value = record.key;

  // 填充表单数据
  addForm.taskType = record.taskType;
  addForm.department = record.department;
  addForm.taskName = record.taskName;
  addForm.window = record.window;
  addForm.personnelRequirement = record.personnelRequirement;
  addForm.startTime = record.startTime;
  addForm.endTime = record.endTime;
  addForm.standardCount = record.standardCount;
  addForm.configuredCount = record.configuredCount;
  addForm.remark = record.remark || "";

  visible.value = true;
};

// 关闭模态框的方法
const close = () => {
  visible.value = false;
};

// 表单完成回调
const onFinish = (values) => {
  // 计算缺口
  const gap = values.standardCount - values.configuredCount;

  // 创建任务对象
  const taskData = {
    ...values,
    stage: "阶段1", // 默认阶段，不在表单中显示但任务中需要
    gap: gap,
    remark: values.remark || "",
  };

  if (isEdit.value) {
    // 编辑模式 - 添加key
    taskData.key = editId.value;
    // 触发编辑成功事件
    emit("edit-success");
  } else {
    // 新增模式
    // 触发添加成功事件
    emit("add-success");
  }

  // 重置表单
  resetForm();

  // 关闭模态框
  visible.value = false;

  console.log(isEdit.value ? "编辑任务成功:" : "添加任务成功:", taskData);
};

// 提交表单
const handleSubmit = () => {
  submitLoading.value = true;

  // 使用表单验证
  formRef.value
    .validate()
    .then(() => {
      onFinish(addForm);
      submitLoading.value = false;
    })
    .catch((error) => {
      console.error("表单验证失败:", error);
      submitLoading.value = false;
    });
};

// 重置表单
const resetForm = () => {
  formRef.value?.resetFields();
};

// 取消按钮事件处理
const handleCancel = () => {
  resetForm();
  visible.value = false;
};

// 暴露方法供父组件调用
defineExpose({
  open,
  openEdit,
  close,
});
</script>

<style scoped>
/* Add task form styles */
.form-row {
  display: flex;
  gap: 16px;
  margin-bottom: 0;
}

.form-item {
  flex: 1;
  margin-bottom: 16px;
}

:deep(.ant-form-item-label) {
  padding-bottom: 4px;
}

:deep(.ant-form-item-label > label) {
  font-size: 13px;
  color: #333;
}

:deep(.ant-select),
:deep(.ant-input),
:deep(.ant-input-number) {
  border-radius: 4px;
}

:deep(.ant-modal-body) {
  padding: 20px 24px;
}

:deep(.ant-form-item-explain-error) {
  font-size: 12px;
}
</style>
