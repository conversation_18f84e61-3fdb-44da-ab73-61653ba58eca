<template>
  <a-modal
    :open="isOpen"
    title="新增专用工具"
    @ok="handleOk"
    @cancel="handleCancel"
    width="800px"
    okText="确定"
    cancelText="取消"
  >
    <a-form ref="formRef" :model="formState">
      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="工具名称：" name="toolName">
            <a-input
              v-model:value="formState.toolName"
              placeholder="请输入工具名称"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="功能/用途：" name="function">
            <a-input
              v-model:value="formState.function"
              placeholder="请输入功能/用途"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="是否需要保养/校验：" name="needMaintenance">
            <a-select
              v-model:value="formState.needMaintenance"
              placeholder="请选择"
            >
              <a-select-option value="是">是</a-select-option>
              <a-select-option value="否">否</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="是否已保养/校验：" name="hasMaintenance">
            <a-select
              v-model:value="formState.hasMaintenance"
              placeholder="请选择"
            >
              <a-select-option value="是">是</a-select-option>
              <a-select-option value="否">否</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="预计保养/校验时间：" name="maintenanceTime">
            <a-date-picker
              v-model:value="formState.maintenanceTime"
              style="width: 100%"
              format="YYYY-MM-DD"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="是否有备用工具：" name="hasBackup">
            <a-select v-model:value="formState.hasBackup" placeholder="请选择">
              <a-select-option value="是">是</a-select-option>
              <a-select-option value="否">否</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="24">
          <a-form-item label="影响评价及措施：" name="impactEvaluation">
            <a-textarea
              v-model:value="formState.impactEvaluation"
              :rows="4"
              placeholder="请输入不可用的影响评价及措施"
              :resize="false"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="准备情况判断：" name="prepJudgment">
            <a-input
              v-model:value="formState.prepJudgment"
              placeholder="请输入准备情况判断"
            />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </a-modal>
</template>

<script setup>
import { ref, reactive } from "vue";

const isOpen = ref(false);
const formRef = ref(null);

const formState = reactive({
  toolName: "",
  function: "",
  needMaintenance: undefined,
  hasMaintenance: undefined,
  maintenanceTime: null,
  hasBackup: undefined,
  impactEvaluation: "",
  prepJudgment: "",
});

const handleOk = () => {
  console.log("表单数据：", formState);
  isOpen.value = false;
};

const handleCancel = () => {
  isOpen.value = false;
};

// 暴露方法给父组件
defineExpose({
  openModal: () => {
    isOpen.value = true;
  },
});
</script>
