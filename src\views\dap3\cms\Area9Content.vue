<template>
  <div class="area9-container">
    <div class="area9-main-title">
      <span class="title-text">专项准备</span>
      <span class="completion-rate">总数：111</span>
    </div>
    <div class="content-area">
      <div class="chart-container">
        <div id="barLineChartArea9" class="pie-chart"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from "vue";
import * as echarts from "echarts/core";

const chartInstance = ref(null);

// 数据与原型图保持一致
const categories = [
  "机械维修",
  "电气维修",
  "仪控维修",
  "技术维修",
  "燃料维修",
  "运行",
  "其他",
];
const barDataFactory = [22, 25, 10, 10, 8, 12, 4]; // 厂级 - Blue
const barDataDept = [14, 13, 10, 8, 6, 10, 5]; // 处室级 - Green
const barDataSection = [8, 8, 5, 5, 3, 8, 3]; // 科室级 - Yellow
const lineDataCompletionRate = [66, 60, 38, 42, 28, 55, 30]; // 完成准备率 - Orange/Brown

const initChart = () => {
  const chartDom = document.getElementById("barLineChartArea9");
  if (!chartDom) return;
  chartInstance.value = echarts.init(chartDom);

  const option = {
    backgroundColor: "transparent",
    color: ["#1890FF", "#52C41A", "#FAAD14", "#FF7A45"],
    legend: {
      data: [
        { name: "厂级", icon: "circle" },
        { name: "处室级", icon: "circle" },
        { name: "科室级", icon: "circle" },
        { name: "完成准备率", icon: "line" },
      ],
      textStyle: {
        color: "#ffffff",
      },
      itemWidth: 12,
      itemHeight: 12,
      top: 0,
      itemGap: 15,
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      top: "12%",
      containLabel: true,
    },

    xAxis: [
      {
        type: "category",
        data: categories,
        axisTick: {
          alignWithLabel: true,
          show: false,
        },
        axisLine: {
          lineStyle: {
            color: "#6e7079",
          },
        },
        axisLabel: {
          color: "#ffffff",
          interval: 0,
          fontSize: 12,
        },
      },
    ],
    yAxis: [
      {
        type: "value",
        name: "",
        min: 0,
        max: 40,
        interval: 10,
        axisLabel: {
          formatter: "{value}",
          color: "#ffffff",
          fontSize: 12,
        },
        splitLine: {
          lineStyle: {
            type: "dashed",
            color: "#3a3f5a",
          },
        },
        axisLine: { show: false },
        axisTick: { show: false },
      },
      {
        type: "value",
        name: "",
        min: 0,
        max: 100,
        interval: 25,
        axisLabel: {
          formatter: "{value}%",
          color: "#ffffff",
          fontSize: 12,
        },
        splitLine: { show: false },
        axisLine: { show: false },
        axisTick: { show: false },
      },
    ],
    series: [
      {
        name: "厂级",
        type: "bar",
        stack: "total",
        barWidth: "40%",
        itemStyle: { color: "#1890FF" }, // 蓝色
        emphasis: {
          focus: "series",
        },
        data: barDataFactory,
      },
      {
        name: "处室级",
        type: "bar",
        stack: "total",
        itemStyle: { color: "#52C41A" }, // 绿色
        emphasis: {
          focus: "series",
        },
        data: barDataDept,
      },
      {
        name: "科室级",
        type: "bar",
        stack: "total",
        itemStyle: { color: "#FAAD14" }, // 黄色
        emphasis: {
          focus: "series",
        },
        data: barDataSection,
      },
      {
        name: "完成准备率",
        type: "line",
        yAxisIndex: 1,
        itemStyle: { color: "#E6A23C" }, // 橙色
        lineStyle: { width: 2 },
        symbol: "circle",
        symbolSize: 8,
        smooth: true,
        emphasis: {
          focus: "series",
        },
        markPoint: {
          symbol: "pin",
          symbolSize: 40,
          data: [
            {
              name: "完成率标记",
              coord: [5, 55], // 运行对应的准备率位置
              itemStyle: {
                color: "#E6A23C",
              },
              label: {
                formatter: "{c}%",
                color: "#fff",
                fontSize: 12,
              },
            },
          ],
        },
        data: lineDataCompletionRate,
      },
    ],
  };

  chartInstance.value.setOption(option);
};

const handleResize = () => {
  if (chartInstance.value) {
    chartInstance.value.resize();
  }
};

onMounted(() => {
  initChart();
  window.addEventListener("resize", handleResize);
});

onUnmounted(() => {
  if (chartInstance.value) {
    chartInstance.value.dispose();
  }
  window.removeEventListener("resize", handleResize);
});
</script>

<style scoped>
.area9-container {
  background-color: #192347;
  color: #fff;
  height: 100%;
  width: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  border-radius: 8px;
  overflow: hidden;
}

.area9-main-title {
  height: 30px;
  background-color: #61aef9;
  color: #ffffff;
  font-size: 16px;
  font-weight: 500;
  display: flex;
  align-items: center;
  padding-left: 15px;
  padding-right: 15px;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  flex-shrink: 0;
  position: relative;
  justify-content: space-between;
}

.area9-main-title::before {
  content: "";
  position: absolute;
  left: 0px;
  top: 5px;
  bottom: 6px;
  width: 4px;
  border-radius: 3px;
  background: linear-gradient(
    to bottom,
    rgba(255, 255, 255, 0.9),
    rgba(255, 255, 255, 0.3)
  );
}

.completion-rate {
  font-size: 14px;
  font-weight: normal;
}

.content-area {
  flex: 1;
  box-sizing: border-box;
  display: flex;
  align-items: stretch;
}

.chart-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pie-chart {
  width: 100%;
  height: 90%;
  position: relative;
}
</style>
