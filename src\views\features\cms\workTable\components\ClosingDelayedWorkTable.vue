<template>
  <div class="closing-delayed-section">
    <div class="section-title">
      <span class="title-icon"></span>
      完工5天未关闭共计7项,详见附件（处理要求：受制约无法按期关闭请在留言备注）
    </div>
    <div class="table-wrapper">
      <el-table
        :data="closingDelayedData"
        border
        stripe
        style="width: 100%"
        :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
      >
        <el-table-column prop="workOrderNo" label="工单号" width="120" />
        <el-table-column prop="taskNo" label="任务号" width="120" />
        <el-table-column prop="title" label="工单任务标题" min-width="200" />
        <el-table-column prop="actualStartDate" label="实际开工" width="120" />
        <el-table-column prop="actualEndDate" label="实际完工" width="120" />
        <el-table-column
          prop="currentApprover"
          label="当前审批人"
          width="120"
        />
        <el-table-column prop="orderStatus" label="工单状态" width="120">
        </el-table-column>
        <el-table-column prop="taskStatus" label="工单任务状态" width="120" />
        <el-table-column prop="teamCode" label="责任班组代码" width="120" />
        <el-table-column prop="responsibleTeam" label="责任班组" width="120" />
        <el-table-column prop="workType" label="作业类型" width="120" />
        <el-table-column prop="unit" label="机组" width="100" />
        <el-table-column prop="system" label="系统" width="120" />
        <el-table-column prop="overhaulCode" label="大修代号" width="120" />
      </el-table>
    </div>
  </div>
</template>

<script>


export default {
  name: "ClosingDelayedWorkTable",
  data() {
    return {
      closingDelayedData: [
        {
          workOrderNo: "WO-2023-0401",
          taskNo: "T-0401",
          title: "2号机组高压加热器疏水阀更换",
          actualStartDate: "2023-10-10",
          actualEndDate: "2023-10-15",
          currentApprover: "张经理",
          orderStatus: "待关闭",
          taskStatus: "已完工",
          teamCode: "JX-02",
          responsibleTeam: "机械二班",
          workType: "计划性维修",
          unit: "2号机组",
          system: "给水系统",
          overhaulCode: "DX-2023-02",
        },
        {
          workOrderNo: "WO-2023-0402",
          taskNo: "T-0402",
          title: "1号锅炉安全阀校验",
          actualStartDate: "2023-10-12",
          actualEndDate: "2023-10-16",
          currentApprover: "李经理",
          orderStatus: "待关闭",
          taskStatus: "已完工",
          teamCode: "JX-01",
          responsibleTeam: "机械一班",
          workType: "定检维修",
          unit: "1号机组",
          system: "锅炉系统",
          overhaulCode: "DX-2023-01",
        },
        {
          workOrderNo: "WO-2023-0403",
          taskNo: "T-0403",
          title: "4号机组凝汽器管道修复",
          actualStartDate: "2023-10-08",
          actualEndDate: "2023-10-12",
          currentApprover: "王经理",
          orderStatus: "待关闭",
          taskStatus: "已完工",
          teamCode: "JX-03",
          responsibleTeam: "机械三班",
          workType: "故障维修",
          unit: "4号机组",
          system: "凝汽系统",
          overhaulCode: "DX-2023-04",
        },
        {
          workOrderNo: "WO-2023-0404",
          taskNo: "T-0404",
          title: "3号机组汽轮机油系统滤网清洗",
          actualStartDate: "2023-10-11",
          actualEndDate: "2023-10-14",
          currentApprover: "赵经理",
          orderStatus: "待关闭",
          taskStatus: "已完工",
          teamCode: "JX-02",
          responsibleTeam: "机械二班",
          workType: "计划性维修",
          unit: "3号机组",
          system: "润滑系统",
          overhaulCode: "DX-2023-03",
        },
        {
          workOrderNo: "WO-2023-0405",
          taskNo: "T-0405",
          title: "6号锅炉引风机叶片修复",
          actualStartDate: "2023-10-09",
          actualEndDate: "2023-10-13",
          currentApprover: "钱经理",
          orderStatus: "待关闭",
          taskStatus: "已完工",
          teamCode: "JX-01",
          responsibleTeam: "机械一班",
          workType: "故障维修",
          unit: "6号机组",
          system: "引风系统",
          overhaulCode: "DX-2023-06",
        },
        {
          workOrderNo: "WO-2023-0406",
          taskNo: "T-0406",
          title: "5号机组给水泵轴承更换",
          actualStartDate: "2023-10-13",
          actualEndDate: "2023-10-17",
          currentApprover: "孙经理",
          orderStatus: "待关闭",
          taskStatus: "已完工",
          teamCode: "JX-03",
          responsibleTeam: "机械三班",
          workType: "计划性维修",
          unit: "5号机组",
          system: "给水系统",
          overhaulCode: "DX-2023-05",
        },
        {
          workOrderNo: "WO-2023-0407",
          taskNo: "T-0407",
          title: "8号锅炉脱硫系统管道更换",
          actualStartDate: "2023-10-10",
          actualEndDate: "2023-10-16",
          currentApprover: "周经理",
          orderStatus: "待关闭",
          taskStatus: "已完工",
          teamCode: "JX-02",
          responsibleTeam: "机械二班",
          workType: "大修项目",
          unit: "8号机组",
          system: "脱硫系统",
          overhaulCode: "DX-2023-08",
        },
      ],
    };
  },
  methods: {
    
  }
};
</script>

<style scoped>
/* 完工5天未关闭区域样式 */
.closing-delayed-section {
  margin-bottom: 30px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  width: 100%;
}

.table-wrapper {
  padding: 15px;
  overflow-x: auto;
}

/* 标题样式 */
.section-title {
  padding: 15px 20px;
  font-size: 15px;
  color: #666;
  font-weight: 500;
  display: flex;
  align-items: center;
  background: linear-gradient(to right, #f8f9fc, #ffffff);
  border-left: 3px solid #1890ff;
  letter-spacing: 0.5px;
  margin-bottom: 0;
  box-shadow: 0 1px 0 rgba(0, 0, 0, 0.05);
}

.title-icon {
  display: inline-block;
  width: 4px;
  height: 16px;
  margin-right: 8px;
  background-color: #1890ff;
  border-radius: 2px;
}

/* 标签样式 */
.el-tag {
  font-weight: bold;
  padding: 2px 8px;
}
</style>
