<template>
  <div class="work-request-section">
    <div class="section-title">
      <span class="title-icon"></span>
      机械现存WR共计16项，详见附件，高优先级及重要WR如下，请尽快核实并处理
    </div>
    <div class="table-wrapper">
      <el-table
        :data="workRequestData"
        border
        stripe
        style="width: 100%"
        :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
      >
        <el-table-column prop="description" label="描述" min-width="250" />
        <el-table-column prop="overhaul" label="大修" width="80" />
        <el-table-column prop="initiator" label="发起人" width="100" />
        <el-table-column prop="initiationTime" label="发起时间" width="150" />
        <el-table-column prop="priority" label="优先级" width="100">
        </el-table-column>
        <el-table-column prop="unit" label="机组" width="100" />
        <el-table-column prop="equipmentLevel" label="设备分级" width="100" />
        <el-table-column prop="taskType" label="作业类型" width="120" />
        <el-table-column prop="specialty" label="建设专业" width="100" />
      </el-table>
    </div>
  </div>
</template>

<script>
export default {
  name: "WorkRequestTable",
  data() {
    return {
      workRequestData: [
        {
          description: "3号机组汽轮机调速系统故障，出力波动，需紧急处理",
          overhaul: "否",
          initiator: "张工",
          initiationTime: "2023-10-15 08:30",
          priority: "紧急",
          unit: "3号机组",
          equipmentLevel: "A级",
          taskType: "故障处理",
          specialty: "机械",
        },
        {
          description: "1号锅炉给水泵轴承温度异常，需检查更换",
          overhaul: "否",
          initiator: "李工",
          initiationTime: "2023-10-16 10:15",
          priority: "高",
          unit: "1号机组",
          equipmentLevel: "B级",
          taskType: "预防性维修",
          specialty: "机械",
        },
        {
          description: "4号机组凝汽器真空度下降，需检查抽气系统",
          overhaul: "是",
          initiator: "王工",
          initiationTime: "2023-10-17 14:45",
          priority: "高",
          unit: "4号机组",
          equipmentLevel: "A级",
          taskType: "大修项目",
          specialty: "机械",
        },
        {
          description: "2号机组高压加热器疏水阀泄漏，需更换密封",
          overhaul: "否",
          initiator: "刘工",
          initiationTime: "2023-10-18 09:20",
          priority: "紧急",
          unit: "2号机组",
          equipmentLevel: "B级",
          taskType: "故障处理",
          specialty: "机械",
        },
        {
          description: "6号机组除氧器水位控制阀故障，需维修更换",
          overhaul: "否",
          initiator: "赵工",
          initiationTime: "2023-10-19 11:30",
          priority: "中",
          unit: "6号机组",
          equipmentLevel: "C级",
          taskType: "故障处理",
          specialty: "机械",
        },
        {
          description: "5号锅炉引风机叶轮磨损，需检查修复",
          overhaul: "是",
          initiator: "孙工",
          initiationTime: "2023-10-20 13:15",
          priority: "高",
          unit: "5号机组",
          equipmentLevel: "A级",
          taskType: "大修项目",
          specialty: "机械",
        },
      ],
    };
  },
  methods: {},
};
</script>

<style scoped>
/* 工作请求区域样式 */
.work-request-section {
  margin-bottom: 30px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  width: 100%;
}

.table-wrapper {
  padding: 15px;
  overflow-x: auto;
}

/* 标题样式 */
.section-title {
  padding: 15px 20px;
  font-size: 15px;
  color: #666;
  font-weight: 500;
  display: flex;
  align-items: center;
  background: linear-gradient(to right, #f8f9fc, #ffffff);
  border-left: 3px solid #1890ff;
  letter-spacing: 0.5px;
  margin-bottom: 0;
  box-shadow: 0 1px 0 rgba(0, 0, 0, 0.05);
}

.title-icon {
  display: inline-block;
  width: 4px;
  height: 16px;
  margin-right: 8px;
  background-color: #1890ff;
  border-radius: 2px;
}

/* 优先级标签样式 */
.el-tag {
  font-weight: bold;
  padding: 2px 8px;
}
</style>
