<template>
  <a-modal
    v-model:visible="visible"
    title="参考时间算法管理"
    width="1400px"
    :footer="null"
    @cancel="handleCancel"
    :bodyStyle="{ height: '700px', overflow: 'auto', padding: '0' }"
  >
    <div class="algorithm-manage-container">
      <!-- 标题和操作区域 -->
      <div class="algorithm-header">
        <a-button type="primary" size="small" @click="handleAddAlgorithm"
          >新增</a-button
        >
      </div>

      <!-- 表格区域 -->
      <a-table
        :columns="columns"
        :data-source="algorithmList"
        :pagination="{ pageSize: 10 }"
        bordered
        size="middle"
        :scroll="{ x: 1200 }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'action'">
            <a-space size="small">
              <a @click="handleEdit(record)">修改</a>
              <a-divider type="vertical" />
              <a-popconfirm
                title="确定要删除这条算法吗?"
                @confirm="handleDelete(record)"
              >
                <a>删除</a>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </div>
  </a-modal>

  <!-- 使用独立的新增/编辑算法组件 -->
  <add-algorithm-modal
    ref="addAlgorithmModalRef"
    @add-success="handleAddSuccess"
    @edit-success="handleEditSuccess"
  />
</template>

<script setup lang="ts">
import { ref, defineExpose } from "vue";
import AddAlgorithmModal from "./AddAlgorithmModal.vue";

// 模态框可见性状态
const visible = ref(false);
const addAlgorithmModalRef = ref();

// 表格列定义
const columns = [
  {
    title: "阶段名称",
    dataIndex: "stageName",
    key: "stageName",
    width: 150,
    align: "center",
  },
  {
    title: "排序",
    dataIndex: "order",
    key: "order",
    width: 80,
    align: "center",
  },
  {
    title: "到场时间",
    dataIndex: "arrivalTime",
    key: "arrivalTime",
    width: 120,
    align: "center",
  },
  {
    title: "到场时间偏移量",
    dataIndex: "arrivalOffset",
    key: "arrivalOffset",
    width: 150,
    align: "center",
  },
  {
    title: "离场时间",
    dataIndex: "departureTime",
    key: "departureTime",
    width: 120,
    align: "center",
  },
  {
    title: "离场时间偏移量",
    dataIndex: "departureOffset",
    key: "departureOffset",
    width: 150,
    align: "center",
  },
  {
    title: "操作",
    key: "action",
    fixed: "right",
    width: 120,
    align: "center",
  },
];

// 示例算法数据
const algorithmList = ref([
  {
    key: "1",
    stageName: "施工准备",
    order: 1,
    arrivalTime: "T01",
    arrivalOffset: "T+0D",
    departureTime: "T10",
    departureOffset: "T+10D",
  },
  {
    key: "2",
    stageName: "主体施工",
    order: 2,
    arrivalTime: "T11",
    arrivalOffset: "T+11D",
    departureTime: "T31",
    departureOffset: "T+31D",
  },
  {
    key: "3",
    stageName: "设备安装",
    order: 3,
    arrivalTime: "T32",
    arrivalOffset: "T+32D",
    departureTime: "T46",
    departureOffset: "T+46D",
  },
  {
    key: "4",
    stageName: "调试验收",
    order: 4,
    arrivalTime: "T47",
    arrivalOffset: "T+47D",
    departureTime: "T59",
    departureOffset: "T+59D",
  },
]);

// 添加算法事件处理
const handleAddAlgorithm = () => {
  // 获取下一个排序值
  const nextOrder =
    algorithmList.value.length > 0
      ? Math.max(...algorithmList.value.map((item) => item.order)) + 1
      : 1;

  // 打开新增模态框
  addAlgorithmModalRef.value.openAdd(nextOrder);
};

// 编辑算法事件处理
const handleEdit = (record) => {
  // 调用子组件的编辑方法
  addAlgorithmModalRef.value.openEdit(record);
};

// 删除算法事件处理
const handleDelete = (record) => {
  console.log("删除算法", record);
  // 从列表中移除该算法
  algorithmList.value = algorithmList.value.filter(
    (item) => item.key !== record.key
  );
};

// 处理添加成功事件
const handleAddSuccess = () => {
  console.log("处理添加成功事件");
};

// 处理编辑成功事件
const handleEditSuccess = () => {
  console.log("处理编辑成功事件");
};

// 打开模态框的方法
const open = () => {
  visible.value = true;
};

// 关闭模态框的方法
const close = () => {
  visible.value = false;
};

// 取消按钮事件处理
const handleCancel = () => {
  close();
};

// 暴露方法供父组件调用
defineExpose({
  open,
  close,
});
</script>

<style scoped>
.algorithm-manage-container {
  padding: 16px;
  overflow: visible;
}

.algorithm-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.algorithm-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
}
</style>
