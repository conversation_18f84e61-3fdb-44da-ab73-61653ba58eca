<template>
  <div class="action-buttons-grid">
    <div class="action-button" @click="showModal('变更物资清单维护')">
      <div class="icon-placeholder icon-document"></div>
      <span class="button-text">变更物资清单维护</span>
    </div>
    <div class="action-button" @click="showModal('变更进展反馈')">
      <div class="icon-placeholder icon-document"></div>
      <span class="button-text">变更进展反馈</span>
    </div>
    <div class="action-button" @click="showModal('变更采购详情')">
      <div class="icon-placeholder icon-document"></div>
      <span class="button-text">变更采购详情</span>
    </div>
    <div class="action-button" @click="showModal('变更信息详情')">
      <div class="icon-placeholder icon-document"></div>
      <span class="button-text">变更信息详情</span>
    </div>
  </div>

  <!-- Ant Design Vue Modal -->
  <a-modal
    v-model:visible="modalVisible"
    :title="modalTitle"
    @ok="handleModalClose"
    @cancel="handleModalClose"
    :width="getModalWidth()"
  >
    <template v-if="modalTitle === '变更进展反馈'">
      <FeedbackTable />
    </template>
    <template v-else-if="modalTitle === '变更物资清单维护'">
      <MaterialListTable />
    </template>
    <template v-else-if="modalTitle === '变更采购详情'">
      <PurchaseDetailTable />
    </template>
    <template v-else-if="modalTitle === '变更信息详情'">
      <ChangeInfoTable />
    </template>
    <template v-else>
      <p>这里是关于"{{ modalTitle }}"的详细内容...</p>
      <p>（后续可以替换为实际组件或数据）</p>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { ref } from "vue";
import FeedbackTable from "./FeedbackTable.vue";
import MaterialListTable from "./MaterialListTable.vue";
import PurchaseDetailTable from "./PurchaseDetailTable.vue";
import ChangeInfoTable from "./ChangeInfoTable.vue";

const modalVisible = ref(false);
const modalTitle = ref("");

// Function to show the modal
const showModal = (buttonName) => {
  modalTitle.value = buttonName;
  modalVisible.value = true;
};

// Function to hide the modal
const handleModalClose = () => {
  modalVisible.value = false;
};

// 根据不同内容设置不同的模态框宽度
const getModalWidth = () => {
  if (modalTitle.value === "变更进展反馈") {
    return 1000;
  } else if (modalTitle.value === "变更物资清单维护") {
    return 1400;
  } else if (modalTitle.value === "变更采购详情") {
    return 1400;
  } else if (modalTitle.value === "变更信息详情") {
    return 1400;
  } else {
    return 520;
  }
};
</script>

<style scoped>
.action-buttons-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: repeat(2, 1fr);
  gap: 15px; /* Gap between buttons */
  padding: 10px; /* Padding inside the grid container */
  height: 100%;
  box-sizing: border-box;
}

.action-button {
  border-radius: 10px; /* Rounded corners */
  padding: 15px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center; /* Center content vertically */
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative; /* Needed for potential pseudo-elements */
  overflow: hidden; /* Hide overflow from pseudo-elements */
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  background-color: #fff; /* Apply white background to all */
  color: #333;
  background-image: radial-gradient(
    circle at top left,
    rgba(230, 247, 255, 0.6) 0%,
    transparent 45%
  ); /* Apply subtle pattern to all */
}

.action-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Styles formerly in .secondary now apply to all .action-button */
.action-button .button-text {
  color: #555; /* Slightly darker text for secondary */
}

.button-text {
  font-size: 14px;
  font-weight: 500;
  margin-top: 10px; /* Space between icon and text */
  line-height: 1.3;
}

/* Updated Icon Placeholder Styles */
.icon-placeholder {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #e6f7ff; /* Light blue background for all icons */
  background-size: 60% 60%; /* Adjust size of the icon */
  background-repeat: no-repeat;
  background-position: center;
}

/* Define the document icon */
.icon-document {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%231677ff'%3E%3Cpath d='M14 2H6c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 1.99 2H18c1.1 0 2-.9 2-2V8l-6-6zm2 16H8v-2h8v2zm0-4H8v-2h8v2zm-3-5V3.5L18.5 9H13z'/%3E%3C/svg%3E");
}

/* Removed emoji styles */
/* .icon-placeholder.wrench::after { ... } */
/* .icon-placeholder.feedback::after { ... } */
/* .icon-placeholder.document::after { ... } */
/* .action-button.primary .icon-placeholder::after { ... } */
</style>
