<template>
    <div class="gantt-container" ref="ganttContainer"></div>
  </template>
  
  <script setup>
  import { ref, onMounted } from "vue";
  import "dhtmlx-gantt";
  import "dhtmlx-gantt/codebase/dhtmlxgantt.css";
  
  const ganttContainer = ref(null);
  
  // 示例数据
  const tasks = {
    data: [
      {
        id: 1,
        text: "项目1",
        start_date: "2024-01-01",
        duration: 150,
        progress: 0.4,
        open: true,
        readonly: true,
      },
      {
        id: 2,
        text: "任务1",
        start_date: "2024-01-01",
        duration: 60,
        progress: 0.6,
        parent: 1,
        readonly: true,
      },
      {
        id: 3,
        text: "任务2",
        start_date: "2024-03-01",
        duration: 90,
        progress: 0.3,
        parent: 1,
        readonly: true,
      },
    ],
    links: [],
  };
  
  // 定义月份下方的数字数组
  const monthNumbers = [3, 5, 2, 7, 1, 8, 4, 6, 9, 0, 2, 5];
  
  onMounted(() => {
    // 基础配置
    gantt.config.readonly = true;
    gantt.config.drag_move = false;
    gantt.config.drag_resize = false;
    gantt.config.drag_progress = false;
    gantt.config.drag_links = false;
  
    // 新版本配置方式
    gantt.config.scales = [
      {
        unit: "month",
        step: 1,
        format: function (date) {
          return (date.getMonth() + 1) + "月";
        },
        height: 30  // 设置第一行高度
      },
      {
        unit: "month",
        step: 1,
        format: function (date) {
          // 使用带样式的HTML
          return `<div class="month-number">${
            monthNumbers[date.getMonth()]
          }</div>`;
        },
        height: 30  // 设置第二行高度
      },
    ];
  
    // 设置时间范围
    gantt.config.start_date = new Date(2024, 0, 1);
    gantt.config.end_date = new Date(2024, 11, 31);
  
    // 设置列宽和高度
    gantt.config.min_column_width = 80;
    gantt.config.scale_height = 25;
  
    // 配置列
    gantt.config.columns = [
      { name: "text", label: "任务名称", tree: true, width: 200, height: 60 },  // 设置列表头高度
      { name: "start_date", label: "开始时间", align: "center", width: 100 },
      { name: "duration", label: "持续时间", align: "center", width: 80 },
    ];
  
    // 初始化
    gantt.init(ganttContainer.value);
    gantt.parse(tasks);
  });
  </script>
  
  <style scoped>
  .gantt-container {
    height: 500px;
    width: 100%;
  }
  
  :deep(.gantt_scale_cell) {
    font-weight: bold;
    text-align: center;
    line-height: 30px;  /* 调整文字垂直居中 */
  }
  
  :deep(.gantt_scale_line) {
    border-top: 1px solid #cecece;
  }
  
  :deep(.gantt_scale_cell) {
    border-right: 1px solid #cecece;
  }
  
  /* 月份数字的样式 */
  :deep(.month-number) {
    font-size: 12px;
    color: #666;
    font-weight: normal;
    line-height: 30px;  /* 调整文字垂直居中 */
  }
  
  /* 调整表头高度 */
  :deep(.gantt_grid_head_cell) {
    height: 60px !important;  /* 设置左侧表头高度 */
    line-height: 60px !important;  /* 文字垂直居中 */
  }
  
  /* 调整甘特图表头整体高度 */
  :deep(.gantt_grid_scale) {
    height: 60px !important;
  }
  
  /* 调整右侧时间轴区域表头高度 */
  :deep(.gantt_scale_line) {
    height: 30px !important;
  }
  </style>
  