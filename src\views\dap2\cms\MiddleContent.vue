<template>
  <div class="middle-content">
    <!-- 顶部统计信息区域 - 占8%高度 -->
    <div class="stats-container">
      <!-- 大修机组统计 -->
      <div class="stat-item">
        <div class="stat-title">
          <div class="stat-icon">
            <i class="el-icon-setting"></i>
          </div>
          大修机组
        </div>
        <div class="stat-value">2<span class="stat-unit">个</span></div>
      </div>

      <!-- 调停/小停机组统计 -->
      <div class="stat-item">
        <div class="stat-title">
          <div class="stat-icon">
            <i class="el-icon-warning"></i>
          </div>
          调停/小停机组
        </div>
        <div class="stat-value">0<span class="stat-unit">个</span></div>
      </div>

      <!-- 功率运行机组统计 -->
      <div class="stat-item">
        <div class="stat-title">
          <div class="stat-icon">
            <i class="el-icon-data-line"></i>
          </div>
          功率运行机组
        </div>
        <div class="stat-value">23<span class="stat-unit">个</span></div>
      </div>
    </div>

    <!-- 中间图片容器区域 - 占87%高度 -->
    <div
      class="image-container"
      @mouseenter="handleMouseEnter"
      @mouseleave="handleMouseLeave"
    >
      <!-- 旧图片 - 执行淡出动画 -->
      <img
        v-if="showOldImage"
        :src="oldImage"
        class="main-image zoom-fade"
        @animationend="onZoomOutEnd"
      />

      <!-- 新图片 - 执行淡入动画 -->
      <img v-if="showNewImage" :src="currentImage" class="main-image zoom-in" />

      <!-- 仓库标记容器 - 当新图片显示时才显示 -->
      <div v-if="showNewImage" class="warehouse-container">
        <!-- 动态生成仓库标记点 -->
        <div
          v-for="(warehouse, index) in currentWarehouses"
          :key="index"
          class="warehouse-marker"
          :class="{ active: warehouse.isActive }"
          :style="{ left: warehouse.x + '%', top: warehouse.y + '%' }"
          @click="selectWarehouse(warehouse.id)"
        >
          <img
            :src="
              warehouse.isActive
                ? '/src/assets/2687.png'
                : '/src/assets/2688.png'
            "
            class="warehouse-icon"
            alt="仓库标记"
          />
          <div class="warehouse-name">{{ warehouse.name }}</div>
          <!-- 告示牌 - 只在仓库高亮时显示 -->
          <div
            v-if="warehouse.isActive"
            class="warehouse-signboard"
            :style="{
              left: warehouse.signboardX + '%',
              top: warehouse.signboardY + '%',
              width: warehouse.signboardWidth + 'px',
              height: warehouse.signboardHeight + 'px',
            }"
          >
            <div class="signboard-header">{{ warehouse.name }}仓库信息</div>
            <div class="signboard-content">
              <div class="signboard-item">
                <span class="item-label">状态：</span>
                <span class="item-value">{{ warehouse.status }}</span>
              </div>
              <div class="signboard-item">
                <span class="item-label">容量：</span>
                <span class="item-value">{{ warehouse.capacity }}</span>
              </div>
              <div class="signboard-item">
                <span class="item-label">使用率：</span>
                <span class="item-value">{{ warehouse.usage }}%</span>
              </div>
              <div class="signboard-item">
                <span class="item-label">更新时间：</span>
                <span class="item-value">{{ warehouse.updateTime }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部按钮容器 - 占5%高度 -->
    <div class="icons-container">
      <!-- 动态生成仓库按钮 -->
      <div
        v-for="(name, index) in warehouseNames"
        :key="index"
        class="icon"
        :class="{
          active: currentImage === imageUrls[index], // 当前图片对应的按钮高亮
          disabled: isAnimating, // 动画过程中禁用按钮
        }"
        @click="!isAnimating && changeImage(imageUrls[index], name)"
      >
        {{ name }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from "vue";

/**
 * 图片URL数组 - 存储所有轮播图片的路径
 */
const imageUrls = [
  "https://img2.baidu.com/it/u=185830230,1153029376&fm=253&fmt=auto&app=120&f=JPEG?w=1422&h=800", // 固定风景照1
  "http://img1.baidu.com/it/u=298645773,137386903&fm=253&app=120&f=JPEG?w=1422&h=800", // 固定风景照2
  "http://img0.baidu.com/it/u=3690408732,616367883&fm=253&app=120&f=JPEG?w=1422&h=800", // 固定风景照3
  "http://img0.baidu.com/it/u=2272943917,4078851494&fm=253&app=120&f=JPEG?w=1422&h=800", // 固定风景照4
  "http://img2.baidu.com/it/u=759291306,2719628734&fm=253&app=120&f=JPEG?w=1422&h=800", // 固定风景照5
  "http://img2.baidu.com/it/u=725076132,1630117999&fm=253&app=120&f=JPEG?w=1422&h=800", // 固定风景照6
  "http://img2.baidu.com/it/u=3164959131,3412703940&fm=253&app=120&f=JPEG?w=1422&h=800", // 固定风景照7
];

/**
 * 仓库名称数组 - 对应底部按钮显示的文字
 */
const warehouseNames = ["重庆", "雪山", "田地", "海滨", "湖南", "三门", "温州"];

/**
 * 状态变量定义
 */
// 当前显示的图片URL
const currentImage = ref(imageUrls[0]);
// 旧图片的URL（淡出动画中的图片）
const oldImage = ref("");
// 是否正在执行动画
const isAnimating = ref(false);
// 是否显示旧图片
const showOldImage = ref(false);
// 是否显示新图片
const showNewImage = ref(true);
// 自动轮播定时器
const autoplayTimer = ref(null);
// 恢复轮播的延迟定时器
const delayTimer = ref(null);
// 仓库高亮轮播定时器
const warehouseHighlightTimer = ref(null);
// 是否启用自动轮播
const autoplayEnabled = ref(true);
// 是否启用仓库高亮轮播
const warehouseHighlightEnabled = ref(true);
// 鼠标是否悬停在图片上
const isHovering = ref(false);
// 旧图片的仓库数据
const oldWarehouses = ref([]);
// 旧图片的索引
const oldImageIndex = ref(0);
// 当前选中的仓库ID
const selectedWarehouseId = ref(null);
// 当前高亮的仓库索引
const currentHighlightIndex = ref(0);

/**
 * 仓库数据 - 每张图片对应的仓库信息
 * 格式: [
 *   [
 *     {
 *       id: 1,
 *       name: '仓库1',
 *       x: 30, y: 40,
 *       isActive: false,
 *       status: '正常',
 *       capacity: '500吨',
 *       usage: 85,
 *       updateTime: '2023-06-20',
 *       signboardX: -150, // 告示牌相对于仓库点的X位置（正值向右，负值向左）
 *       signboardY: -120, // 告示牌相对于仓库点的Y位置（正值向下，负值向上）
 *       signboardWidth: 200, // 告示牌宽度
 *       signboardHeight: 150 // 告示牌高度
 *     },
 *     ...
 *   ],
 *   ...
 * ]
 */
const warehousesByImage = [
  // 第一张图片的仓库 (重庆)
  [
    {
      id: 1,
      name: "重庆A",
      x: 30,
      y: 40,
      isActive: false,
      status: "正常",
      capacity: "1000吨",
      usage: 75,
      updateTime: "2023-06-15",
      signboardX: -150,
      signboardY: -120,
      signboardWidth: 200,
      signboardHeight: 150,
    },
    {
      id: 2,
      name: "重庆B",
      x: 60,
      y: 70,
      isActive: false,
      status: "维护中",
      capacity: "800吨",
      usage: 0,
      updateTime: "2023-06-18",
      signboardX: 20,
      signboardY: -150,
      signboardWidth: 200,
      signboardHeight: 150,
    },
    {
      id: 3,
      name: "重庆C",
      x: 80,
      y: 30,
      isActive: false,
      status: "正常",
      capacity: "1200吨",
      usage: 92,
      updateTime: "2023-06-20",
      signboardX: -200,
      signboardY: 20,
      signboardWidth: 200,
      signboardHeight: 150,
    },
  ],
  // 第二张图片的仓库 (雪山)
  [
    {
      id: 4,
      name: "雪山A",
      x: 20,
      y: 50,
      isActive: false,
      status: "正常",
      capacity: "600吨",
      usage: 45,
      updateTime: "2023-06-19",
      signboardX: 20,
      signboardY: 20,
      signboardWidth: 200,
      signboardHeight: 150,
    },
    {
      id: 5,
      name: "雪山B",
      x: 50,
      y: 30,
      isActive: false,
      status: "正常",
      capacity: "750吨",
      usage: 60,
      updateTime: "2023-06-17",
      signboardX: -200,
      signboardY: -120,
      signboardWidth: 200,
      signboardHeight: 150,
    },
  ],
  // 第三张图片的仓库 (田地)
  [
    {
      id: 6,
      name: "田地A",
      x: 40,
      y: 60,
      isActive: false,
      status: "正常",
      capacity: "900吨",
      usage: 88,
      updateTime: "2023-06-20",
      signboardX: -200,
      signboardY: -150,
      signboardWidth: 200,
      signboardHeight: 150,
    },
    {
      id: 7,
      name: "田地B",
      x: 70,
      y: 40,
      isActive: false,
      status: "正常",
      capacity: "850吨",
      usage: 72,
      updateTime: "2023-06-16",
      signboardX: 20,
      signboardY: -120,
      signboardWidth: 200,
      signboardHeight: 150,
    },
  ],
  // 第四张图片的仓库 (海滨)
  [
    {
      id: 8,
      name: "海滨A",
      x: 35,
      y: 45,
      isActive: false,
      status: "正常",
      capacity: "1100吨",
      usage: 65,
      updateTime: "2023-06-18",
      signboardX: 20,
      signboardY: 20,
      signboardWidth: 200,
      signboardHeight: 150,
    },
    {
      id: 9,
      name: "海滨B",
      x: 65,
      y: 55,
      isActive: false,
      status: "维护中",
      capacity: "950吨",
      usage: 10,
      updateTime: "2023-06-15",
      signboardX: -200,
      signboardY: -120,
      signboardWidth: 200,
      signboardHeight: 150,
    },
  ],
  // 第五张图片的仓库 (湖南)
  [
    {
      id: 10,
      name: "湖南A",
      x: 25,
      y: 35,
      isActive: false,
      status: "正常",
      capacity: "700吨",
      usage: 85,
      updateTime: "2023-06-19",
      signboardX: 20,
      signboardY: -120,
      signboardWidth: 200,
      signboardHeight: 150,
    },
    {
      id: 11,
      name: "湖南B",
      x: 75,
      y: 65,
      isActive: false,
      status: "正常",
      capacity: "850吨",
      usage: 70,
      updateTime: "2023-06-17",
      signboardX: -200,
      signboardY: 20,
      signboardWidth: 200,
      signboardHeight: 150,
    },
  ],
  // 第六张图片的仓库 (三门)
  [
    {
      id: 12,
      name: "三门A",
      x: 45,
      y: 25,
      isActive: false,
      status: "正常",
      capacity: "950吨",
      usage: 82,
      updateTime: "2023-06-20",
      signboardX: -200,
      signboardY: 20,
      signboardWidth: 200,
      signboardHeight: 150,
    },
    {
      id: 13,
      name: "三门B",
      x: 55,
      y: 75,
      isActive: false,
      status: "正常",
      capacity: "800吨",
      usage: 68,
      updateTime: "2023-06-16",
      signboardX: 20,
      signboardY: -150,
      signboardWidth: 200,
      signboardHeight: 150,
    },
  ],
  // 第七张图片的仓库 (温州)
  [
    {
      id: 14,
      name: "温州A",
      x: 30,
      y: 60,
      isActive: false,
      status: "正常",
      capacity: "1050吨",
      usage: 77,
      updateTime: "2023-06-18",
      signboardX: 20,
      signboardY: -120,
      signboardWidth: 200,
      signboardHeight: 150,
    },
    {
      id: 15,
      name: "温州B",
      x: 70,
      y: 30,
      isActive: false,
      status: "正常",
      capacity: "900吨",
      usage: 65,
      updateTime: "2023-06-15",
      signboardX: -200,
      signboardY: 20,
      signboardWidth: 200,
      signboardHeight: 150,
    },
  ],
];

/**
 * 计算当前图片对应的仓库数据
 * 根据当前显示的图片URL，获取对应的仓库数据
 */
const currentWarehouses = computed(() => {
  const index = imageUrls.indexOf(currentImage.value);
  if (index === -1) return [];

  // 获取当前图片对应的仓库数据
  const warehouses = warehousesByImage[index] || [];

  // 更新激活状态
  return warehouses.map((warehouse) => ({
    ...warehouse,
    isActive: warehouse.id === selectedWarehouseId.value,
  }));
});

/**
 * 自动切换仓库高亮状态
 * 每2秒轮流高亮一个仓库
 */
const startWarehouseHighlight = () => {
  // 清除可能存在的旧定时器
  clearTimeout(warehouseHighlightTimer.value);

  // 设置新定时器
  warehouseHighlightTimer.value = setTimeout(() => {
    if (warehouseHighlightEnabled.value && !isHovering.value) {
      // 获取当前图片的仓库数组
      const currentIndex = imageUrls.indexOf(currentImage.value);
      const warehouses = warehousesByImage[currentIndex] || [];

      if (warehouses.length > 0) {
        // 计算下一个要高亮的仓库索引
        currentHighlightIndex.value =
          (currentHighlightIndex.value + 1) % warehouses.length;

        // 更新所有仓库的高亮状态
        warehouses.forEach((warehouse, index) => {
          warehouse.isActive = index === currentHighlightIndex.value;
        });

        // 更新选中的仓库ID
        selectedWarehouseId.value = warehouses[currentHighlightIndex.value].id;
      }
    }

    // 无论是否执行了切换，都继续下一次轮播
    if (warehouseHighlightEnabled.value) {
      startWarehouseHighlight();
    }
  }, 2000); // 2秒切换一次高亮
};

/**
 * 手动选择仓库
 * @param {number} warehouseId - 要选择的仓库ID
 */
const selectWarehouse = (warehouseId) => {
  // 暂停自动高亮切换
  warehouseHighlightEnabled.value = false;
  clearTimeout(warehouseHighlightTimer.value);

  // 获取当前图片的仓库数组
  const currentIndex = imageUrls.indexOf(currentImage.value);
  const warehouses = warehousesByImage[currentIndex] || [];

  // 更新所有仓库的高亮状态
  let foundIndex = -1;
  warehouses.forEach((warehouse, index) => {
    const isActive = warehouse.id === warehouseId;
    warehouse.isActive = isActive;
    if (isActive) {
      foundIndex = index;
    }
  });

  // 更新当前高亮索引和选中的仓库ID
  if (foundIndex !== -1) {
    currentHighlightIndex.value = foundIndex;
    selectedWarehouseId.value = warehouseId;
  }

  // 5秒后恢复自动高亮切换
  setTimeout(() => {
    warehouseHighlightEnabled.value = true;
    startWarehouseHighlight();
  }, 5000);
};

/**
 * 设置默认高亮仓库
 * @param {number} imageIndex - 图片索引
 */
const setDefaultHighlight = (imageIndex) => {
  // 重置当前高亮索引
  currentHighlightIndex.value = 0;

  // 获取当前图片的仓库数组
  const warehouses = warehousesByImage[imageIndex] || [];

  // 更新所有仓库的高亮状态
  warehouses.forEach((warehouse, index) => {
    warehouse.isActive = index === 0; // 默认高亮第一个仓库
  });

  // 更新选中的仓库ID
  if (warehouses.length > 0) {
    selectedWarehouseId.value = warehouses[0].id;
  } else {
    selectedWarehouseId.value = null;
  }
};

/**
 * 手动切换图片
 * @param {string} url - 目标图片的URL
 */
const changeImage = (url, name) => {
  console.log(url, name);
  // 只有当目标图片不是当前图片且没有动画正在进行时才执行切换
  if (currentImage.value !== url && !isAnimating.value) {
    // 1. 停止自动轮播
    autoplayEnabled.value = false;
    clearTimeout(autoplayTimer.value);
    clearTimeout(delayTimer.value);

    // 2. 保存当前图片的仓库数据，用于动画过渡
    const currentIndex = imageUrls.indexOf(currentImage.value);
    oldWarehouses.value = warehousesByImage[currentIndex] || [];
    oldImageIndex.value = currentIndex;

    // 3. 设置动画状态
    isAnimating.value = true; // 标记动画开始
    oldImage.value = currentImage.value; // 保存当前图片为旧图片
    showOldImage.value = true; // 显示旧图片（开始淡出动画）
    showNewImage.value = false; // 暂时隐藏新图片

    // 4. 等旧图片快消失时才显示新图片
    setTimeout(() => {
      currentImage.value = url; // 更新当前图片URL
      showNewImage.value = true; // 显示新图片（开始淡入动画）

      // 5. 设置新图片的仓库高亮状态
      const newIndex = imageUrls.indexOf(url);
      setDefaultHighlight(newIndex);
    }, 700); // 旧图片动画时长为1秒，在700ms时显示新图片

    // 6. 10秒后恢复轮播
    delayTimer.value = setTimeout(() => {
      autoplayEnabled.value = true;
      if (!isHovering.value) {
        startAutoplay();
      }
    }, 10000);
  }
};

/**
 * 自动切换到下一张图片
 * 逻辑与手动切换类似，但目标是下一张图片
 */
const autoChangeImage = () => {
  if (!isAnimating.value) {
    // 1. 保存当前图片的仓库数据
    const currentIndex = imageUrls.indexOf(currentImage.value);
    oldWarehouses.value = warehousesByImage[currentIndex] || [];
    oldImageIndex.value = currentIndex;

    // 2. 设置动画状态
    isAnimating.value = true;
    oldImage.value = currentImage.value;
    showOldImage.value = true;
    showNewImage.value = false;

    // 3. 计算下一张图片的索引（循环轮播）
    const nextIndex = (currentIndex + 1) % imageUrls.length;
    // 打印出对应按钮的名字
    console.log(warehouseNames[nextIndex]);
    // 4. 等旧图片快消失时才显示新图片
    setTimeout(() => {
      currentImage.value = imageUrls[nextIndex];
      showNewImage.value = true;

      // 5. 设置新图片的仓库高亮状态
      setDefaultHighlight(nextIndex);
    }, 700);
  }
};

/**
 * 旧图片动画结束事件处理
 * 当旧图片淡出动画完成时触发
 */
const onZoomOutEnd = () => {
  showOldImage.value = false; // 完全隐藏旧图片
  isAnimating.value = false; // 标记动画结束
};

/**
 * 开始自动轮播
 * 设置定时器，定期切换图片
 */
const startAutoplay = () => {
  // 清除可能存在的旧定时器
  clearTimeout(autoplayTimer.value);

  // 设置新定时器
  autoplayTimer.value = setTimeout(() => {
    // 只有在自动轮播启用、没有动画进行中、鼠标不在图片上时才切换图片
    if (autoplayEnabled.value && !isAnimating.value && !isHovering.value) {
      autoChangeImage();
    }
    // 无论是否执行了切换，都继续下一次轮播
    if (autoplayEnabled.value) {
      startAutoplay();
    }
  }, 10000); // 5秒轮播一次
};

/**
 * 鼠标移入图片事件处理
 * 暂停自动轮播和仓库高亮切换
 */
const handleMouseEnter = () => {
  isHovering.value = true;
  clearTimeout(autoplayTimer.value); // 暂停图片轮播
  clearTimeout(warehouseHighlightTimer.value); // 暂停仓库高亮轮播
};

/**
 * 鼠标移出图片事件处理
 * 恢复自动轮播和仓库高亮切换
 */
const handleMouseLeave = () => {
  isHovering.value = false;
  // 只有在自动轮播启用时才恢复轮播
  if (autoplayEnabled.value) {
    startAutoplay();
  }
  // 只有在仓库高亮轮播启用时才恢复轮播
  if (warehouseHighlightEnabled.value) {
    startWarehouseHighlight();
  }
};

/**
 * 监听当前图片变化
 * 当图片切换时，更新仓库显示
 */
watch(currentImage, (newValue) => {
  const index = imageUrls.indexOf(newValue);
  if (index !== -1) {
    // 确保仓库高亮状态正确
    setDefaultHighlight(index);
  }
});

/**
 * 组件挂载时的生命周期钩子
 * 初始化并启动自动轮播和仓库高亮轮播
 */
onMounted(() => {
  // 设置第一张图片的默认高亮仓库
  setDefaultHighlight(0);

  // 启动自动轮播
  autoplayEnabled.value = true;
  startAutoplay();

  // 启动仓库高亮轮播
  warehouseHighlightEnabled.value = true;
  startWarehouseHighlight();
});

/**
 * 组件卸载时的生命周期钩子
 * 清除所有定时器，防止内存泄漏
 */
onUnmounted(() => {
  clearTimeout(autoplayTimer.value);
  clearTimeout(delayTimer.value);
  clearTimeout(warehouseHighlightTimer.value);
});
</script>

<style scoped>
/* 主容器样式 - 垂直排列三个区域 */
.middle-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  position: relative;
  width: 100%;
  height: 100%;
}

/* 顶部统计信息区域 - 占总高度的8% */
.stats-container {
  width: 100%;
  height: 8%;
  display: flex;
  justify-content: space-around;
  align-items: center;
  background: linear-gradient(
    to right,
    rgba(0, 20, 60, 0.7),
    rgba(0, 40, 80, 0.7),
    rgba(0, 20, 60, 0.7)
  );
  padding: 5px 0;
  border-radius: 10px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
  z-index: 10;
  margin-bottom: 5px;
}

/* 单个统计项样式 */
.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(
    to bottom,
    rgba(0, 60, 120, 0.7),
    rgba(0, 30, 80, 0.7)
  );
  border-radius: 15px;
  padding: 5px 15px;
  width: 25%;
  height: 90%;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(0, 180, 255, 0.3);
}

/* 统计项标题样式 */
.stat-title {
  color: #00b4ff;
  font-size: 12px;
  margin-bottom: 2px;
  display: flex;
  align-items: center;
}

/* 统计项图标样式 */
.stat-icon {
  width: 16px;
  height: 16px;
  margin-right: 4px;
  background-color: rgba(0, 180, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 统计项数值样式 */
.stat-value {
  color: #ffcc00;
  font-size: 18px;
  font-weight: bold;
  text-shadow: 0 0 10px rgba(255, 204, 0, 0.5);
}

/* 统计项单位样式 */
.stat-unit {
  font-size: 12px;
  margin-left: 2px;
}

/* 中间图片容器 - 占总高度的87% */
.image-container {
  position: relative;
  width: 100%;
  height: 87%;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 图片样式 - 居中且不占满容器 */
.main-image {
  position: absolute;
  width: 90%;
  height: 90%;
  object-fit: cover;
  border-radius: 10px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

/* 仓库标记容器 */
.warehouse-container {
  position: absolute;
  width: 90%;
  height: 90%;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 20;
  pointer-events: none;
}

/* 单个仓库标记点 */
.warehouse-marker {
  position: absolute;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  pointer-events: auto;
  transition: all 0.3s ease;
}

/* 仓库图标样式 */
.warehouse-icon {
  width: 32px;
  height: 32px;
  filter: drop-shadow(0 0 5px rgba(0, 0, 0, 0.5));
  transition: all 0.3s ease;
}

/* 仓库名称样式 */
.warehouse-name {
  color: white;
  background-color: rgba(0, 0, 0, 0.7);
  padding: 3px 8px;
  border-radius: 10px;
  font-size: 12px;
  margin-top: 5px;
  opacity: 0;
  transform: translateY(-5px);
  transition: all 0.3s ease;
  white-space: nowrap;
  text-shadow: 0 1px 2px black;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

/* 仓库标记悬停效果 */
.warehouse-marker:hover .warehouse-icon {
  transform: scale(1.2);
  filter: drop-shadow(0 0 8px rgba(255, 204, 0, 0.8));
}

/* 仓库标记悬停时显示名称 */
.warehouse-marker:hover .warehouse-name {
  opacity: 1;
  transform: translateY(0);
}

/* 激活状态的仓库标记 */
.warehouse-marker.active .warehouse-icon {
  filter: drop-shadow(0 0 10px rgba(255, 204, 0, 0.8));
}

/* 激活状态的仓库名称始终显示 */
.warehouse-marker.active .warehouse-name {
  opacity: 1;
  transform: translateY(0);
  background-color: rgba(255, 204, 0, 0.7);
  color: black;
  font-weight: bold;
}

/* 图片淡入动画 - 从中心点放大显示 */
@keyframes zoomIn {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.5);
  }
  100% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

/* 图片淡出动画 - 从中心点放大消失 */
@keyframes zoomFadeOut {
  0% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
  20% {
    opacity: 0.9;
    transform: translate(-50%, -50%) scale(1.1);
  }
  40% {
    opacity: 0.7;
    transform: translate(-50%, -50%) scale(1.2);
  }
  60% {
    opacity: 0.5;
    transform: translate(-50%, -50%) scale(1.3);
  }
  80% {
    opacity: 0.3;
    transform: translate(-50%, -50%) scale(1.4);
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(1.5);
  }
}

/* 应用淡入动画的类 */
.zoom-in {
  animation: zoomIn 0.8s cubic-bezier(0.2, 0.8, 0.2, 1) forwards;
  opacity: 0;
}

/* 应用淡出动画的类 */
.zoom-fade {
  animation: zoomFadeOut 1s cubic-bezier(0.2, 0.8, 0.2, 1) forwards;
}

/* 底部按钮容器 - 占总高度的5% */
.icons-container {
  width: 100%;
  height: 5%;
  display: flex;
  justify-content: center;
  gap: 10px;
  background: linear-gradient(
    to right,
    rgba(0, 20, 60, 0.7),
    rgba(0, 40, 80, 0.7),
    rgba(0, 20, 60, 0.7)
  );
  padding: 5px 15px;
  border-radius: 10px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
  z-index: 10;
  margin-top: 5px;
}

/* 单个按钮样式 */
.icon {
  width: 80px;
  height: 30px;
  border-radius: 5px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  background: rgba(0, 0, 0, 0.2);
  color: #fff;
  font-weight: bold;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  font-size: 14px;
}

/* 按钮悬停效果 */
.icon:hover:not(.disabled) {
  transform: scale(1.05);
  box-shadow: 0 0 15px rgba(255, 255, 255, 0.3);
}

/* 激活状态的按钮样式 */
.icon.active {
  border-color: #ffcc00;
  background: linear-gradient(
    to bottom,
    rgba(255, 204, 0, 0.3),
    rgba(255, 150, 0, 0.2)
  );
  box-shadow: 0 0 10px rgba(255, 204, 0, 0.5);
}

/* 禁用状态的按钮样式 */
.icon.disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

/* 按钮顶部高光效果 */
.icon::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(
    to right,
    transparent,
    rgba(255, 255, 255, 0.5),
    transparent
  );
}

/* 按钮底部阴影效果 */
.icon::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(
    to right,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
}

/* 告示牌样式 */
.warehouse-signboard {
  position: absolute;
  background: linear-gradient(
    to bottom,
    rgba(0, 40, 80, 0.9),
    rgba(0, 20, 60, 0.9)
  );
  border-radius: 8px;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.5);
  border: 2px solid rgba(0, 180, 255, 0.5);
  pointer-events: auto;
  overflow: hidden;
  z-index: 30;
  transform-origin: center center;
  animation: signboardAppear 0.5s ease forwards;
}

/* 告示牌标题样式 */
.signboard-header {
  background: linear-gradient(
    to right,
    rgba(0, 60, 120, 0.9),
    rgba(0, 100, 160, 0.9)
  );
  color: #ffffff;
  font-weight: bold;
  padding: 8px 12px;
  font-size: 14px;
  text-align: center;
  border-bottom: 1px solid rgba(0, 180, 255, 0.3);
}

/* 告示牌内容区域样式 */
.signboard-content {
  padding: 10px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* 告示牌信息项样式 */
.signboard-item {
  display: flex;
  justify-content: space-between;
  font-size: 13px;
}

/* 告示牌标签样式 */
.item-label {
  color: #00b4ff;
}

/* 告示牌数值样式 */
.item-value {
  color: #ffffff;
  font-weight: bold;
}

/* 告示牌显示动画 */
@keyframes signboardAppear {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}
</style>
