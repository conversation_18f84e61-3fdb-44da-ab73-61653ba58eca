<template>
  <div class="box">
    <GridLayout
      :prevent-collision="true"
      v-model:layout="state.layout"
      :col-num="7"
      :row-height="30"
      :is-draggable="false"
      :is-resizable="false"
      :vertical-compact="true"
      :use-css-transforms="true"
    >
      <GridItem
        style="border: 1px solid #000"
        v-for="item in state.layout"
        :key="item.i"
        :x="item.x"
        :y="item.y"
        :w="item.w"
        :h="item.h"
        :i="item.i"
      >
        <component :is="item.componentName" :title="item.componentName" />
      </GridItem>
    </GridLayout>
  </div>
</template>

<script setup lang="ts">
import { reactive, onMounted } from "vue";
import { GridLayout, GridItem } from "vue-grid-layout-v3";

interface LayoutItem {
  i: string | number;
  x: number;
  y: number;
  w: number;
  h: number;
  componentName: string;
}

const state = reactive({
  layout: [] as LayoutItem[],
});

onMounted(() => {
  const savedLayout = localStorage.getItem("layout");
  if (savedLayout) {
    state.layout = JSON.parse(savedLayout) as LayoutItem[];
  }
  console.log(state.layout);
});
</script>

<style lang="scss" scoped>
.box {
  width: 100%;
  height: 100vh;
  text-align: center;
  line-height: 50px;
}
</style>
