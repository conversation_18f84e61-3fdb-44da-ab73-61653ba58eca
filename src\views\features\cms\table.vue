<template>
    <div class="gantt-container">
      <div class="gantt-header-row">
        <h2 class="gantt-title">xx项目甘特图</h2>
        <a-button 
          type="primary" 
          @click="exportGanttChart" 
          :loading="exporting"
          icon="download"
        >
          导出甘特图
        </a-button>
      </div>
      <div class="table-container">
        <a-table
          ref="ganttTableRef"
          :columns="columns"
          :data-source="data"
          :pagination="false"
          bordered
          size="middle"
          :scroll="{ y: 500 }"
          class="gantt-table"
          :custom-row="customRowClass"
        >
          <!-- 自定义列渲染 -->
          <template #bodyCell="{ column, record }">
            <!-- 渲染甘特图区域 -->
            <template v-if="column.dataIndex === 'gantt'">
              <div class="gantt-row">
                <div class="gantt-timeline">
                  <!-- 添加滚动区域的日期线 -->
                  <div
                    v-if="showTodayLine"
                    class="scroll-area-today-line"
                    :style="todayLineStyle"
                  ></div>
  
                  <!-- 甘特图条 -->
                  <div
                    v-if="shouldRenderBar(record)"
                    class="gantt-bar"
                    :style="getBarStyleNew(record)"
                  >
                    <span v-if="record.duration" class="bar-text">
                      {{ record.duration }}
                    </span>
                  </div>
  
                  <!-- 超期/提前指示器，只对已完成项目（status=1）显示 -->
                  <div
                    v-if="record.status === 1 && record.num !== undefined"
                    class="delay-indicator"
                    :style="getDelayStyle(record)"
                  >
                    <!-- 指示器内不显示文本 -->
                  </div>
                  <!-- 将超期/提前文本作为单独元素展示在指示器外部 -->
                  <div
                    v-if="record.status === 1 && record.num !== undefined"
                    class="delay-text"
                    :style="getDelayTextStyle(record)"
                  >
                    {{
                      record.num > 0
                        ? "+" + record.num + "天"
                        : "-" + Math.abs(record.num) + "天"
                    }}
                  </div>
                </div>
              </div>
            </template>
          </template>
  
          <!-- 自定义表头 -->
          <template #headerCell="{ column }">
            <template v-if="column.dataIndex === 'gantt'">
              <div class="gantt-header" id="gantt-header">
                <div class="month-row">
                  <div
                    v-for="(month, index) in months"
                    :key="index"
                    class="month-cell"
                  >
                    {{ month }}
                  </div>
                </div>
                <div class="repair-row">
                  <div
                    v-for="(count, index) in repairCounts"
                    :key="index"
                    class="repair-cell"
                  >
                    {{ count }}
                  </div>
                </div>
                <!-- 表头区域的日期线 -->
                <div
                  class="continuous-today-line"
                  :style="todayLineStyle"
                  v-if="showTodayLine"
                ></div>
              </div>
            </template>
            <template v-else>
              {{ column.title }}
            </template>
          </template>
        </a-table>
      </div>
      <!-- 隐藏的截图容器 -->
      <div id="screenshotContainer" style="display: none;"></div>
    </div>
  </template>
  
  <script setup>
  import { ref, computed } from "vue";
  import { message } from "ant-design-vue"; // 导入消息提示组件
  import html2canvas from "html2canvas"; // 导入html2canvas库
  
  // 定义导出状态变量
  const exporting = ref(false);
  const ganttTableRef = ref(null); // 表格引用
  
  // 定义月份
  const months = [
    "1月",
    "2月",
    "3月",
    "4月",
    "5月",
    "6月",
    "7月",
    "8月",
    "9月",
    "10月",
    "11月",
    "12月",
  ];
  
  // 生成xx次数数据（示例数据）
  const repairCounts = [
    "3次",
    "2次",
    "4次",
    "1次",
    "2次",
    "5次",
    "2次",
    "3次",
    "4次",
    "2次",
    "1次",
    "3次",
  ];
  
  // 自定义行属性，禁用行的hover状态
  const customRowClass = () => {
    return {
      class: "no-hover-effect",
    };
  };
  
  // 基本列定义
  const baseColumns = [
    {
      title: "xx编号",
      dataIndex: "id",
      key: "id",
      width: 120,
      align: "center",
    },
    {
      title: "类型",
      dataIndex: "type",
      key: "type",
      width: 100,
      align: "center",
    },
    {
      title: "计划开始时间",
      dataIndex: "startDate",
      key: "startDate",
      width: 140,
      align: "center",
    },
    {
      title: "计划结束时间",
      dataIndex: "endDate",
      key: "endDate",
      width: 140,
      align: "center",
    },
    {
      title: "计划工期",
      dataIndex: "duration",
      key: "duration",
      width: 100,
      align: "center",
    },
  ];
  
  // 甘特图列 - 一个大列包含所有月份
  const ganttColumn = {
    title: "", // 使用自定义表头
    dataIndex: "gantt",
    key: "gantt",
    width: 800,
  };
  
  // 合并所有列
  const columns = [...baseColumns, ganttColumn];
  
  // 模拟数据
  const data = [
    {
      key: "1",
      id: "JH2023-001",
      title: "",
      type: "A",
      startDate: "2023-03-10",
      endDate: "2023-05-20",
      duration: "70天",
      color: "#4285F4",
      status: 1, // 完整色块
      num: 10, // 超期10%
    },
    {
      key: "2",
      id: "JH2023-002",
      title: "",
      type: "A",
      startDate: "2023-05-15",
      endDate: "2023-07-05",
      duration: "50天",
      color: "#EA4335",
      status: 1, // 完整色块
      num: -5, // 提前5%
    },
    {
      key: "3",
      id: "JH2023-003",
      title: "",
      type: "A",
      startDate: "2023-06-08",
      endDate: "2023-07-28",
      duration: "50天",
      color: "#FBBC05",
      status: 0.5, // 50%进度
    },
    {
      key: "4",
      id: "JH2023-004",
      title: "",
      type: "A",
      startDate: "2023-08-01",
      endDate: "2023-10-10",
      duration: "70天",
      color: "#34A853",
      status: 0.3, // 30%进度
    },
    {
      key: "5",
      id: "JH2023-005",
      title: "",
      type: "A",
      startDate: "2023-09-10",
      endDate: "2023-11-20",
      duration: "70天",
      color: "#673AB7",
      status: 1, // 完整色块
      num: 15, // 超期15%
    },{
      key: "5",
      id: "JH2023-005",
      title: "",
      type: "A",
      startDate: "2023-09-10",
      endDate: "2023-11-20",
      duration: "70天",
      color: "#673AB7",
      status: 1, // 完整色块
      num: 15, // 超期15%
    },{
      key: "5",
      id: "JH2023-005",
      title: "",
      type: "A",
      startDate: "2023-09-10",
      endDate: "2023-11-20",
      duration: "70天",
      color: "#673AB7",
      status: 1, // 完整色块
      num: 15, // 超期15%
    },{
      key: "5",
      id: "JH2023-005",
      title: "",
      type: "A",
      startDate: "2023-09-10",
      endDate: "2023-11-20",
      duration: "70天",
      color: "#673AB7",
      status: 1, // 完整色块
      num: 15, // 超期15%
    },{
      key: "5",
      id: "JH2023-005",
      title: "",
      type: "A",
      startDate: "2023-09-10",
      endDate: "2023-11-20",
      duration: "70天",
      color: "#673AB7",
      status: 1, // 完整色块
      num: 15, // 超期15%
    },{
      key: "5",
      id: "JH2023-005",
      title: "",
      type: "A",
      startDate: "2023-09-10",
      endDate: "2023-11-20",
      duration: "70天",
      color: "#673AB7",
      status: 1, // 完整色块
      num: 15, // 超期15%
    },{
      key: "5",
      id: "JH2023-005",
      title: "",
      type: "A",
      startDate: "2023-09-10",
      endDate: "2023-11-20",
      duration: "70天",
      color: "#673AB7",
      status: 1, // 完整色块
      num: 15, // 超期15%
    },{
      key: "5",
      id: "JH2023-005",
      title: "",
      type: "A",
      startDate: "2023-09-10",
      endDate: "2023-11-20",
      duration: "70天",
      color: "#673AB7",
      status: 1, // 完整色块
      num: 15, // 超期15%
    },{
      key: "5",
      id: "JH2023-005",
      title: "",
      type: "A",
      startDate: "2023-09-10",
      endDate: "2023-11-20",
      duration: "70天",
      color: "#673AB7",
      status: 1, // 完整色块
      num: 15, // 超期15%
    },{
      key: "5",
      id: "JH2023-005",
      title: "",
      type: "A",
      startDate: "2023-09-10",
      endDate: "2023-11-20",
      duration: "70天",
      color: "#673AB7",
      status: 1, // 完整色块
      num: 15, // 超期15%
    },
  ];
  
  // 新的获取条形样式方法，将甘特图整体计算
  const getBarStyleNew = (record) => {
    // 如果没有预计算的月份数据，根据日期计算
    if (!record.startMonth && record.startDate) {
      // 解析日期
      const startDate = new Date(record.startDate);
      const endDate = new Date(record.endDate);
  
      // 计算月份
      record.startMonth = startDate.getMonth() + 1; // 0-11 转为 1-12
      record.endMonth = endDate.getMonth() + 1;
  
      // 计算日偏移量
      record.startOffset = startDate.getDate();
      record.endOffset = endDate.getDate();
  
      // 计算工期（天数）
      const diffTime = Math.abs(endDate - startDate);
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      record.duration = diffDays + "天";
    }
  
    // 根据类型设置颜色
    let color;
    switch (record.type) {
      case "A":
        color = "#1e88e5";
        break;
      case "B":
        color = "#4caf50";
        break;
      case "C":
        color = "#ffc107";
        break;
      default:
        color = "#673AB7"; // 默认颜色
    }
  
    // 计算在整个时间线中的位置百分比
    // 一年总共12个月，每个月按30天计算
    const totalDays = 12 * 30;
  
    // 计算起始位置（距离左边界的百分比）
    const startDayOfYear = (record.startMonth - 1) * 30 + record.startOffset;
    const leftPercent = (startDayOfYear / totalDays) * 100;
  
    // 计算结束位置
    const endDayOfYear = (record.endMonth - 1) * 30 + record.endOffset;
  
    // 计算宽度百分比
    const durationDays = endDayOfYear - startDayOfYear;
    const widthPercent = (durationDays / totalDays) * 100;
  
    let style = {
      left: `${leftPercent}%`,
      width: `${widthPercent}%`,
    };
  
    // 根据status设置背景样式
    if (record.status === 1) {
      // 完整显示色块
      style.backgroundColor = color;
    } else {
      // 按照比例显示色块，使用线性渐变
      const percentage = record.status * 100 || 0;
      style.background = `linear-gradient(to right, ${color} ${percentage}%, rgba(255,255,255,0.3) ${percentage}%)`;
      // 为status不为1的添加虚线边框
      style.border = `1px dashed ${color}`;
    }
  
    return style;
  };
  
  // 判断是否应该渲染甘特图条
  const shouldRenderBar = (record) => {
    return (
      (record.startMonth && record.endMonth) ||
      (record.startDate && record.endDate)
    );
  };
  
  // 计算今日日期线的位置
  const showTodayLine = computed(() => {
    const today = new Date();
    const currentYear = today.getFullYear();
    const currentMonth = today.getMonth() + 1; // JavaScript 月份是0-11
  
    // 检查当前月份是否在甘特图范围内
    return currentMonth >= 1 && currentMonth <= 12;
  });
  
  const todayLineStyle = computed(() => {
    const today = new Date();
    const currentMonth = today.getMonth() + 1;
    const currentDate = today.getDate();
    const daysInMonth = new Date(today.getFullYear(), currentMonth, 0).getDate();
    const monthIndex = currentMonth - 1;
    const cellWidth = 100 / 12;
    const dayRatio = (currentDate - 1) / daysInMonth;
    const leftPosition = cellWidth * monthIndex + cellWidth * dayRatio;
  
    return {
      left: `${leftPosition}%`,
    };
  });
  
  // 获取延期/提前指示器的样式
  const getDelayStyle = (record) => {
    if (record.status !== 1 || record.num === undefined) return {};
  
    // 计算在整个时间线中的甘特图条位置
    const totalDays = 12 * 30;
    const startDayOfYear = (record.startMonth - 1) * 30 + record.startOffset;
    const endDayOfYear = (record.endMonth - 1) * 30 + record.endOffset;
    const durationDays = endDayOfYear - startDayOfYear;
  
    // 甘特图条的宽度和位置
    const widthPercent = (durationDays / totalDays) * 100;
    const leftPercent = (startDayOfYear / totalDays) * 100;
  
    // 将天数转换为百分比 (相对于365天)
    const daysInYear = 365;
    const dayPercentage = (Math.abs(record.num) / daysInYear) * 100;
  
    // 指示器的位置，无论是超期还是提前，都显示在右侧
    const delayLeft = leftPercent + widthPercent;
  
    // 设置渐变色
    const isDelay = record.num > 0; // 正数表示超期，负数表示提前
    const gradientColor = isDelay
      ? "linear-gradient(to right, rgba(255,0,0,0.3), rgba(255,0,0,0.7))" // 超期：红色
      : "linear-gradient(to right, rgba(0,150,0,0.3), rgba(0,150,0,0.7))"; // 提前：绿色
  
    return {
      left: `${delayLeft}%`,
      width: `${dayPercentage}%`,
      background: gradientColor,
    };
  };
  
  // 获取延期/提前文本样式
  const getDelayTextStyle = (record) => {
    if (record.status !== 1 || record.num === undefined) return {};
  
    // 计算在整个时间线中的甘特图条位置
    const totalDays = 12 * 30;
    const startDayOfYear = (record.startMonth - 1) * 30 + record.startOffset;
    const endDayOfYear = (record.endMonth - 1) * 30 + record.endOffset;
    const durationDays = endDayOfYear - startDayOfYear;
  
    // 甘特图条的宽度和位置
    const widthPercent = (durationDays / totalDays) * 100;
    const leftPercent = (startDayOfYear / totalDays) * 100;
  
    // 将天数转换为百分比 (相对于365天)
    const daysInYear = 365;
    const dayPercentage = (Math.abs(record.num) / daysInYear) * 100;
  
    // 指示器的位置，无论是超期还是提前，都显示在右侧
    const delayLeft = leftPercent + widthPercent;
  
    // 文本的位置：显示在指示器和色块的右侧
    const textLeft = delayLeft + dayPercentage + 0.5; // 添加小偏移，确保不重叠
  
    // 根据超期还是提前设置不同文本颜色
    const isDelay = record.num > 0; // 正数表示超期，负数表示提前
    const textColor = isDelay ? "#ff4d4f" : "#52c41a";
  
    return {
      left: `${textLeft}%`,
      color: textColor,
    };
  };
  
  // 导出甘特图为图片
  const exportGanttChart = async () => {
    exporting.value = true;
    message.loading({
      content: "正在准备截图...",
      key: "ganttExport",
      duration: 0,
    });
  
    try {
      const tableElement = ganttTableRef.value && ganttTableRef.value.$el;
      if (!tableElement) throw new Error("无法获取表格元素");
  
      // 1. 克隆 DOM
      const tableCopy = tableElement.cloneNode(true);
      
      // 删除甘特图标题行
      const titleRow = tableCopy.querySelector('.gantt-header-row');
      if (titleRow && titleRow.parentNode) {
        titleRow.parentNode.removeChild(titleRow);
      }
      
      // 获取原始列宽度配置
      const columnsConfig = [
        { width: 120 }, // xx编号
        { width: 100 }, // 类型
        { width: 140 }, // 计划开始时间
        { width: 140 }, // 计划结束时间
        { width: 100 }, // 计划工期
        { width: 800 }, // 甘特图区域
      ];
      
      // 计算总宽度
      const totalWidth = columnsConfig.reduce((sum, col) => sum + col.width, 0);
      
      // 2. 处理样式 - 强化表格布局控制
      const tableInstance = tableCopy.querySelector(".ant-table");
      if (tableInstance) {
        tableInstance.style.tableLayout = "fixed";
        tableInstance.style.borderCollapse = "collapse";
        tableInstance.style.borderSpacing = "0";
        tableInstance.style.width = `${totalWidth}px`;
        tableInstance.style.maxWidth = `${totalWidth}px`;
      }
  
      // 设置每个表格容器宽度
      const tableContainer = tableCopy.querySelector(".ant-table-container");
      if (tableContainer) {
        tableContainer.style.width = `${totalWidth}px`;
        tableContainer.style.maxWidth = `${totalWidth}px`;
      }
      
      // 限制表格内容区域宽度
      const tableContent = tableCopy.querySelector(".ant-table-content");
      if (tableContent) {
        tableContent.style.width = `${totalWidth}px`;
        tableContent.style.maxWidth = `${totalWidth}px`;
      }
  
      // 移除固定列
      const fixedCells = tableCopy.querySelectorAll(".ant-table-cell-fix-left, .ant-table-cell-fix-right");
      fixedCells.forEach(cell => { cell.style.position = "static"; });
  
      // 移除滚动限制
      const scrollContainers = tableCopy.querySelectorAll("[style*='overflow']");
      scrollContainers.forEach(el => {
        el.style.overflow = "visible";
        el.style.height = "auto";
        el.style.maxHeight = "none";
      });
      
      // 先找到表格本身，设置colgroup/col
      const tableEl = tableCopy.querySelector('table');
      if (tableEl) {
        tableEl.style.width = `${totalWidth}px`;
        tableEl.style.tableLayout = "fixed";
        
        // 创建或获取colgroup
        let colgroup = tableEl.querySelector('colgroup');
        if (!colgroup) {
          colgroup = document.createElement('colgroup');
          tableEl.insertBefore(colgroup, tableEl.firstChild);
        } else {
          colgroup.innerHTML = '';
        }
        
        // 创建col元素并设置宽度
        columnsConfig.forEach(col => {
          const colElement = document.createElement('col');
          colElement.style.width = `${col.width}px`;
          colElement.style.minWidth = `${col.width}px`;
          colElement.style.maxWidth = `${col.width}px`;
          colgroup.appendChild(colElement);
        });
      }
  
      // 增强甘特图条的文字显示
      const ganttBars = tableCopy.querySelectorAll('.gantt-bar');
      ganttBars.forEach(bar => {
        const barWidth = parseFloat(bar.style.width);
        // 确保文字可见，不管条形图多窄都显示文字
        const barText = bar.querySelector('.bar-text');
        if (barText) {
          barText.style.width = "auto";
          barText.style.whiteSpace = "nowrap";
          barText.style.overflow = "visible";
          barText.style.position = "relative";
          barText.style.zIndex = "10";
          barText.style.color = "white";
          barText.style.textShadow = "0 0 2px rgba(0,0,0,0.8)";
          barText.style.fontWeight = "bold";
          
          // 如果条形图太窄，将文字放在条形图外部
          if (barWidth < 15) {
            bar.style.minWidth = "10px";
            barText.style.position = "absolute";
            barText.style.right = "-45px"; // 放在右侧
          }
        }
      });
      
      // 强制表头宽度
      const tableHeader = tableCopy.querySelector('.ant-table-thead');
      if (tableHeader) {
        const headerCells = tableHeader.querySelectorAll('th');
        headerCells.forEach((cell, index) => {
          if (index < columnsConfig.length) {
            const width = columnsConfig[index].width;
            cell.style.width = `${width}px`;
            cell.style.minWidth = `${width}px`;
            cell.style.maxWidth = `${width}px`;
            cell.width = width;
            cell.style.boxSizing = "border-box";
            cell.style.padding = "0";
            cell.style.margin = "0";
          }
        });
  
        // 确保计划工期与甘特图表头正确对齐
        const lastInfoCell = headerCells[4]; // 计划工期
        const ganttCell = headerCells[5]; // 甘特图列
        
        if (lastInfoCell && ganttCell) {
          lastInfoCell.style.borderRight = "1px solid #4882c0";
          ganttCell.style.borderLeft = "none";
          ganttCell.style.paddingLeft = "0";
          ganttCell.style.marginLeft = "0";
        }
      }
      
      // 处理甘特图表头特殊组件的宽度 - 精确对齐
      const ganttHeader = tableCopy.querySelector('.gantt-header');
      if (ganttHeader) {
        const ganttWidth = columnsConfig[5].width;
        ganttHeader.style.width = `${ganttWidth}px`;
        ganttHeader.style.minWidth = `${ganttWidth}px`;
        ganttHeader.style.maxWidth = `${ganttWidth}px`;
        ganttHeader.style.margin = "0";
        ganttHeader.style.padding = "0";
        ganttHeader.style.boxSizing = "border-box";
        ganttHeader.style.overflow = "hidden";
        
        // 设置月份行和修复行的宽度
        const monthRow = ganttHeader.querySelector('.month-row');
        const repairRow = ganttHeader.querySelector('.repair-row');
        
        if (monthRow) {
          monthRow.style.width = `${ganttWidth}px`;
          monthRow.style.margin = "0";
          monthRow.style.padding = "0";
          monthRow.style.boxSizing = "border-box";
        }
        
        if (repairRow) {
          repairRow.style.width = `${ganttWidth}px`;
          repairRow.style.margin = "0";
          repairRow.style.padding = "0";
          repairRow.style.boxSizing = "border-box";
        }
        
        // 确保每个月份单元格宽度相等
        const monthCells = ganttHeader.querySelectorAll('.month-cell');
        const repairCells = ganttHeader.querySelectorAll('.repair-cell');
        
        if (monthCells.length) {
          const cellWidth = Math.floor(ganttWidth / monthCells.length);
          monthCells.forEach(cell => {
            cell.style.width = `${cellWidth}px`;
            cell.style.minWidth = `${cellWidth}px`;
            cell.style.maxWidth = `${cellWidth}px`;
            cell.style.flex = 'none';
            cell.style.boxSizing = "border-box";
          });
        }
        
        if (repairCells.length) {
          const cellWidth = Math.floor(ganttWidth / repairCells.length);
          repairCells.forEach(cell => {
            cell.style.width = `${cellWidth}px`;
            cell.style.minWidth = `${cellWidth}px`;
            cell.style.maxWidth = `${cellWidth}px`;
            cell.style.flex = 'none';
            cell.style.boxSizing = "border-box";
          });
        }
      }
      
      // 处理表体单元格
      const tableBody = tableCopy.querySelector('.ant-table-tbody');
      if (tableBody) {
        const rows = tableBody.querySelectorAll('tr');
        rows.forEach(row => {
          const cells = row.querySelectorAll('td');
          cells.forEach((cell, index) => {
            if (index < columnsConfig.length) {
              const width = columnsConfig[index].width;
              cell.style.width = `${width}px`;
              cell.style.minWidth = `${width}px`;
              cell.style.maxWidth = `${width}px`;
              cell.width = width;
              cell.style.boxSizing = "border-box";
              cell.style.padding = "0";
              cell.style.margin = "0";
              
              // 确保计划工期列与甘特图列之间边框正确
              if (index === 4) { // 计划工期列
                cell.style.borderRight = "1px solid #38618d";
              }
              if (index === 5) { // 甘特图列
                cell.style.borderLeft = "none";
                cell.style.paddingLeft = "0";
                cell.style.marginLeft = "0";
              }
            }
          });
        });
      }
  
      // 准备截图容器
      const screenshotContainer = document.createElement("div");
      screenshotContainer.style.position = "absolute";
      screenshotContainer.style.left = "-9999px";
      screenshotContainer.style.width = `${totalWidth}px`;
      screenshotContainer.style.background = "#162e5a";
      document.body.appendChild(screenshotContainer);
      screenshotContainer.appendChild(tableCopy);
  
      // 等待DOM更新
      await new Promise(resolve => setTimeout(resolve, 1000));
  
      // 使用html2canvas生成图片
      const canvas = await html2canvas(tableCopy, {
        width: totalWidth,
        height: tableCopy.scrollHeight,
        windowWidth: totalWidth,
        windowHeight: tableCopy.scrollHeight,
        backgroundColor: "#162e5a",
        scale: 2, // 提高清晰度
        useCORS: true,
        logging: false, 
        allowTaint: true,
        onclone: (clonedDoc, clonedElement) => {
          // 处理克隆后的DOM
          const table = clonedElement.querySelector('table');
          if (table) {
            table.style.width = `${totalWidth}px`;
            table.style.maxWidth = `${totalWidth}px`;
          }
          
          // 增强甘特图条的文字显示
          const bars = clonedElement.querySelectorAll('.gantt-bar');
          bars.forEach(bar => {
            const barWidth = parseFloat(bar.style.width);
            // 确保文字可见，不管条形图多窄都显示文字
            const barText = bar.querySelector('.bar-text');
            if (barText) {
              barText.style.width = "auto";
              barText.style.whiteSpace = "nowrap";
              barText.style.overflow = "visible";
              barText.style.position = "relative";
              barText.style.zIndex = "10";
              barText.style.color = "white";
              barText.style.textShadow = "0 0 2px rgba(0,0,0,0.8)";
              barText.style.fontWeight = "bold";
              
              // 如果条形图太窄，将文字放在条形图外部
              if (barWidth < 15) {
                bar.style.minWidth = "10px";
                barText.style.position = "absolute";
                barText.style.right = "-45px"; // 放在右侧
              }
            }
          });
          
          // 处理表头
          const headerCells = clonedElement.querySelectorAll('thead th');
          headerCells.forEach((cell, index) => {
            if (index < columnsConfig.length) {
              const width = columnsConfig[index].width;
              cell.style.width = `${width}px`;
              cell.style.minWidth = `${width}px`;
              cell.style.maxWidth = `${width}px`;
              cell.width = width;
              cell.style.boxSizing = "border-box";
              cell.style.padding = "0";
              cell.style.margin = "0";
            }
            
            // 确保计划工期与甘特图表头正确对齐
            if (index === 4) { // 计划工期列
              cell.style.borderRight = "1px solid #4882c0";
            }
            if (index === 5) { // 甘特图列
              cell.style.borderLeft = "none";
              cell.style.paddingLeft = "0";
              cell.style.marginLeft = "0";
            }
          });
          
          // 处理表体
          const bodyCells = clonedElement.querySelectorAll('tbody td');
          bodyCells.forEach((cell, index) => {
            const cellIndex = index % columnsConfig.length;
            const width = columnsConfig[cellIndex].width;
            cell.style.width = `${width}px`;
            cell.style.minWidth = `${width}px`;
            cell.style.maxWidth = `${width}px`;
            cell.width = width;
            cell.style.boxSizing = "border-box";
            cell.style.padding = "0";
            cell.style.margin = "0";
            
            // 确保计划工期列与甘特图列之间边框正确
            if (cellIndex === 4) { // 计划工期列
              cell.style.borderRight = "1px solid #38618d";
            }
            if (cellIndex === 5) { // 甘特图列
              cell.style.borderLeft = "none";
              cell.style.paddingLeft = "0";
              cell.style.marginLeft = "0";
            }
          });
          
          // 强制甘特图列宽度
          const ganttCells = clonedElement.querySelectorAll('td:nth-child(6), th:nth-child(6)');
          ganttCells.forEach(cell => {
            const ganttWidth = columnsConfig[5].width;
            cell.style.width = `${ganttWidth}px`;
            cell.style.minWidth = `${ganttWidth}px`;
            cell.style.maxWidth = `${ganttWidth}px`;
            cell.style.margin = "0";
            cell.style.padding = "0";
            cell.style.boxSizing = "border-box";
            cell.style.position = "relative";
          });
          
          // 强制设置甘特图容器宽度
          const ganttContainers = clonedElement.querySelectorAll('.gantt-timeline');
          ganttContainers.forEach(container => {
            const ganttWidth = columnsConfig[5].width;
            container.style.width = `${ganttWidth}px`;
            container.style.minWidth = `${ganttWidth}px`;
            container.style.maxWidth = `${ganttWidth}px`;
            container.style.margin = "0";
            container.style.padding = "0";
            container.style.boxSizing = "border-box";
            container.style.position = "relative";
          });
          
          // 确保甘特图月份行容器正确对齐
          const ganttHeader = clonedElement.querySelector('.gantt-header');
          if (ganttHeader) {
            const ganttWidth = columnsConfig[5].width;
            ganttHeader.style.width = `${ganttWidth}px`;
            ganttHeader.style.minWidth = `${ganttWidth}px`;
            ganttHeader.style.maxWidth = `${ganttWidth}px`;
            ganttHeader.style.margin = "0";
            ganttHeader.style.padding = "0";
            ganttHeader.style.boxSizing = "border-box";
            ganttHeader.style.overflow = "hidden";
            
            const monthRow = ganttHeader.querySelector('.month-row');
            const repairRow = ganttHeader.querySelector('.repair-row');
            
            if (monthRow) {
              monthRow.style.width = `${ganttWidth}px`;
              monthRow.style.margin = "0";
              monthRow.style.padding = "0";
              monthRow.style.boxSizing = "border-box";
            }
            
            if (repairRow) {
              repairRow.style.width = `${ganttWidth}px`;
              repairRow.style.margin = "0";
              repairRow.style.padding = "0";
              repairRow.style.boxSizing = "border-box";
            }
          }
          
          // 其他通用样式
          const allCells = clonedElement.querySelectorAll("td, th");
          allCells.forEach(cell => {
            cell.style.boxSizing = "border-box";
            cell.style.height = "40px";
            cell.style.lineHeight = "40px";
            cell.style.verticalAlign = "middle";
            cell.style.padding = "0";
          });
  
          // 强制斑马纹背景色
          const bodyRows = clonedElement.querySelectorAll(".ant-table-tbody tr");
          bodyRows.forEach((row, index) => {
            const bgColor = index % 2 === 0 ? "#1d274b" : "#162e5a";
            row.style.backgroundColor = bgColor;
          });
        }
      });
  
      // 下载图片
      const imageUrl = canvas.toDataURL("image/png");
      const link = document.createElement("a");
      link.download = `甘特图_${new Date().toLocaleDateString().replace(/\//g, "-")}.png`;
      link.href = imageUrl;
      link.click();
  
      message.success({
        content: "图片导出成功！",
        key: "ganttExport",
        duration: 2,
      });
    } catch (error) {
      console.error("导出图片失败:", error);
      message.error({
        content: `导出图片失败: ${error.message || '未知错误'}`,
        key: "ganttExport",
        duration: 3,
      });
    } finally {
      // 清理
      const container = document.querySelector("div[style*='-9999px']");
      if (container && container.parentNode === document.body) {
        document.body.removeChild(container);
      }
      exporting.value = false;
    }
  };
  </script>
  
  <style scoped>
  .gantt-container {
    padding: 0;
    background-color: #f7f9fc;
    border-radius: 0;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  }
  
  .gantt-title {
    font-size: 22px;
    color: #333;
    margin: 20px;
    font-weight: 500;
  }
  
  .table-container {
    position: relative;
    height: 100%;
  }
  
  .gantt-table {
    background: white;
    overflow: hidden;
    margin: 0;
    padding: 0;
    border-spacing: 0;
  }
  
  /* 甘特图自定义表头 */
  .gantt-header {
    width: 100%;
    height: 40px; /* 设置固定高度 */
    position: relative;
    display: flex;
    flex-direction: column;
  }
  
  /* 月份行样式 */
  .month-row {
    display: flex;
    width: 100%;
    background-color: #0f5391;
    height: 24px;
    position: relative;
  }
  
  /* xx次数行样式 */
  .repair-row {
    display: flex;
    width: 100%;
    background-color: #1a63a0;
    height: 16px;
    position: absolute;
    bottom: 0; /* 贴近底部 */
    left: 0;
    right: 0;
  }
  
  /* 调整月份单元格样式 */
  .month-cell {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #0ad0fd;
    font-weight: 500;
    font-size: 13px;
    padding: 0; /* 移除内边距 */
  }
  
  /* 调整xx次数单元格样式 */
  .repair-cell {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 11px;
    padding: 0; /* 移除内边距 */
  }
  
  /* 连贯的今日指示线 */
  .continuous-today-line {
    position: absolute;
    top: 40px;
    height: calc(100% - 40px);
    width: 0;
    border-left: 2px dashed #ff4d4f; /* 改用 border-left 实现虚线 */
    border-style: dashed;
    border-width: 0 0 0 2px;
    border-image: repeating-linear-gradient(
      to bottom,
      #ff4d4f 0,
      #ff4d4f 4px,
      transparent 4px,
      transparent 8px
    ) 1; /* 使用 border-image 实现更短更密的虚线 */
    opacity: 0.8;
    z-index: 1;
    pointer-events: none;
  }
  
  /* 表格相关样式 */
  :deep(.ant-table) {
    position: relative;
  }
  
  :deep(.ant-table-container) {
    position: relative;
  }
  
  :deep(.ant-table-body) {
    position: relative;
    overflow-x: hidden;
    overflow-y: auto;
    max-height: 500px;
    margin: 0; /* 移除外边距 */
  }
  
  /* 确保内容区域的定位正确 */
  :deep(.ant-table-tbody) {
    position: relative;
  }
  
  /* 添加一个新的类来处理滚动区域内的日期线 */
  .scroll-area-today-line {
    position: absolute;
    top: 0;
    bottom: 0;
    width: 0;
    border-left: 2px dashed #ff4d4f;
    border-style: dashed;
    border-width: 0 0 0 2px;
    border-image: repeating-linear-gradient(
      to bottom,
      #ff4d4f 0,
      #ff4d4f 4px,
      transparent 4px,
      transparent 8px
    ) 1;
    opacity: 0.8;
    z-index: 999;
    pointer-events: none;
  }
  
  /* 调整表格滚动区域的样式 */
  :deep(.ant-table-body) {
    position: relative;
    height: 500px; /* 固定高度 */
    overflow-y: auto;
  }
  
  :deep(.ant-table-header) {
    position: relative;
    z-index: 1000; /* 确保表头在日期线上方 */
    background-color: #0f5391; /* 保持表头背景色 */
  }
  
  /* 确保甘特图区域可以显示日期线 */
  .gantt-timeline {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center; /* 垂直居中 */
  }
  
  /* 甘特图行样式 */
  .gantt-row {
    display: flex;
    width: 100%;
    height: 40px;
    position: relative;
    padding: 4px 0; /* 增加上下内边距 */
    margin: 0;
    box-sizing: border-box;
  }
  
  .gantt-cell {
    flex: 1;
    height: 100%;
    position: relative;
    border: none !important;
  }
  
  .gantt-cell:last-child {
    border-right: none;
  }
  
  /* 甘特图条形样式 */
  .gantt-bar {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    height: 23px; /* 调整高度为23px */
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 12px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    box-shadow: none;
    z-index: 1;
  }
  
  /* 调整首尾样式 */
  .bar-start {
    border-radius: 0;
    padding-left: 8px;
    justify-content: center;
  }
  
  .bar-middle {
    border-radius: 0;
    justify-content: center;
  }
  
  .bar-end {
    border-radius: 0;
    padding-right: 8px;
    justify-content: center;
  }
  
  .bar-full {
    border-radius: 0;
    padding: 0 8px;
    justify-content: center;
  }
  
  .bar-text {
    white-space: nowrap;
    overflow: visible;
    text-overflow: clip;
    font-weight: 500;
    text-align: center;
    width: auto;
    padding: 0 4px;
  }
  
  /* 禁用表格行悬停效果的自定义类 */
  :deep(.no-hover-effect) {
    background-color: inherit !important;
  }
  
  :deep(.no-hover-effect:hover) {
    background-color: inherit !important;
  }
  
  /* 禁用表格悬停引起的闪烁问题 */
  :deep(.ant-table-tbody > tr > td) {
    padding: 0;
    margin: 0;
    border: none !important;
    height: 40px;
    line-height: 40px;
  }
  
  /* 完全禁用所有hover效果 */
  :deep(.ant-table-tbody > tr:hover > td),
  :deep(.ant-table-tbody > tr:hover),
  :deep(.ant-table-tbody > tr.ant-table-row:hover > td),
  :deep(.ant-table-tbody > tr > td.ant-table-cell-hover),
  :deep(.ant-table-tbody > tr.ant-table-row-hover),
  :deep(.ant-table-row:hover),
  :deep(.ant-table-row-hover),
  :deep(.ant-table-cell-hover),
  :deep(.ant-table-tbody > tr.ant-table-row:hover),
  :deep(.ant-table-tbody > tr:hover td),
  :deep(.ant-table-tbody > tr.ant-table-row:focus),
  :deep(.ant-table-tbody > tr.ant-table-row:active),
  :deep(.ant-table-cell:focus),
  :deep(.ant-table-cell:active) {
    background-color: initial !important;
    background: none !important;
    transition: none !important;
    animation: none !important;
  }
  
  /* 强制斑马线样式 */
  :deep(.ant-table-tbody > tr:nth-child(odd)),
  :deep(.ant-table-tbody > tr:nth-child(odd)):hover,
  :deep(.ant-table-tbody > tr:nth-child(odd) td):hover,
  :deep(.ant-table-tbody > tr:nth-child(odd).ant-table-row):hover,
  :deep(.ant-table-tbody > tr:nth-child(odd) > td),
  :deep(.ant-table-tbody > tr:nth-child(odd) > td:hover) {
    background-color: #1d274b !important;
  }
  
  :deep(.ant-table-tbody > tr:nth-child(even)),
  :deep(.ant-table-tbody > tr:nth-child(even)):hover,
  :deep(.ant-table-tbody > tr:nth-child(even) td):hover,
  :deep(.ant-table-tbody > tr:nth-child(even).ant-table-row):hover,
  :deep(.ant-table-tbody > tr:nth-child(even) > td),
  :deep(.ant-table-tbody > tr:nth-child(even) > td:hover) {
    background-color: #162e5a !important;
  }
  
  /* 覆盖所有Ant Design CSS变量 */
  :deep(.ant-table) {
    --ant-table-row-hover-bg: unset !important;
    --ant-table-cell-hover-bg: unset !important;
  }
  
  :root {
    --ant-table-row-hover-bg: unset !important;
    --ant-table-cell-hover-bg: unset !important;
  }
  
  /* 表格样式调整 */
  :deep(.ant-table-tbody > tr > td) {
    padding: 0;
    margin: 0;
    border: none !important;
    height: 40px;
    line-height: 40px;
    color: white !important; /* 设置字体颜色为白色 */
  }
  
  /* 设置表格行高 */
  :deep(.ant-table-tbody > tr) {
    height: 40px;
    line-height: 40px;
    padding: 0;
    margin: 0;
  }
  
  /* 设置表格斑马条纹 */
  :deep(.ant-table-tbody > tr:nth-child(odd)),
  :deep(.ant-table-tbody > tr:nth-child(odd)):hover,
  :deep(.ant-table-tbody > tr:nth-child(odd) > td:hover),
  :deep(.ant-table-tbody > tr:nth-child(odd).ant-table-row:hover),
  :deep(.ant-table-tbody > tr:nth-child(odd).ant-table-row-hover) {
    background-color: #1d274b !important;
  }
  
  :deep(.ant-table-tbody > tr:nth-child(even)),
  :deep(.ant-table-tbody > tr:nth-child(even):hover),
  :deep(.ant-table-tbody > tr:nth-child(even) > td:hover),
  :deep(.ant-table-tbody > tr:nth-child(even).ant-table-row:hover),
  :deep(.ant-table-tbody > tr:nth-child(even).ant-table-row-hover) {
    background-color: #162e5a !important;
  }
  
  /* 表头与表体之间的分隔线也可以移除 */
  :deep(.ant-table-thead > tr:last-child > th) {
    border-bottom: none !important; /* 移除底部边框 */
  }
  
  /* 移除表格所有边框 */
  :deep(.ant-table-container) {
    border: none !important;
  }
  
  :deep(.ant-table) {
    border: none !important;
  }
  
  :deep(.ant-table-container table) {
    border: none !important;
    border-collapse: collapse;
  }
  
  /* 基本信息列样式 */
  :deep(.ant-table-thead > tr > th) {
    background-color: #0f5391;
    color: #0ad0fd;
    font-weight: 500;
    padding: 0; /* 移除内边距 */
    height: 40px;
    line-height: 40px;
    border: none !important;
  }
  
  /* 隐藏表头分割线 */
  :deep(.ant-table-thead > tr > th::before) {
    display: none !important; /* 完全隐藏伪元素分隔线 */
  }
  
  /* 表头单元格之间的间隔处理 */
  :deep(.ant-table-container table > thead > tr:first-child th) {
    border-top: none;
  }
  
  :deep(.ant-table-container table > thead > tr th) {
    border-right: none;
    border-bottom: none;
  }
  
  /* 处理甘特图表头特殊样式 */
  :deep(.ant-table-thead > tr > th:last-child) {
    padding: 0 !important; /* 移除甘特图表头内边距 */
    overflow: hidden; /* 确保内容不溢出 */
    border: none !important;
  }
  
  /* 调整甘特图表头高度以匹配基本列表头 */
  :deep(.ant-table-thead > tr > th.ant-table-cell) {
    height: auto;
    padding: 4px;
  }
  
  /* 确保表格行之间没有间隙 */
  :deep(.ant-table) {
    border-collapse: collapse;
    padding: 0;
    margin: 0;
  }
  
  :deep(.ant-table-container) {
    padding: 0;
    margin: 0;
  }
  
  /* 设置最后一列甘特图区域的相对定位 */
  :deep(.ant-table-thead > tr > th:last-child),
  :deep(.ant-table-tbody > tr > td:last-child) {
    position: relative;
    overflow: visible;
    border: none !important; /* 确保甘特图区域没有边框 */
  }
  
  /* 移除表格边框和悬停效果 */
  :deep(.ant-table-wrapper) {
    border: none !important;
  }
  
  :deep(.ant-table),
  :deep(.ant-table-container) {
    border-radius: 0 !important;
  }
  
  /* 阻止鼠标事件处理 */
  :deep(.ant-table) {
    pointer-events: auto !important;
  }
  
  /* 为基本信息列添加竖向边框 */
  :deep(.ant-table-tbody > tr > td:nth-child(-n + 5)) {
    border-right: 1px solid #38618d !important;
  }
  
  /* 确保表头单元格没有边框 */
  :deep(.ant-table-thead > tr > th) {
    border: none !important;
  }
  
  /* 为计划工期表头添加右侧边框 */
  :deep(.ant-table-thead > tr > th:nth-child(5)) {
    border-right: 1px solid #4882c0 !important;
  }
  
  /* 时间线整体容器 */
  .gantt-timeline {
    width: 100%;
    height: 100%;
    position: relative;
  }
  
  /* 月份分隔线 */
  .month-divider {
    position: absolute;
    top: 0;
    height: 100%;
    width: 1px;
    /* background-color: rgba(255, 255, 255, 0.1); */
    z-index: 1;
    pointer-events: none;
  }
  
  /* 延期/提前指示器样式 */
  .delay-indicator {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    height: 23px; /* 调整高度为23px */
    display: flex;
    align-items: center;
    justify-content: center;
    white-space: nowrap;
    text-overflow: ellipsis;
    z-index: 3;
  }
  
  /* 延期/提前文本样式 */
  .delay-text {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    height: 23px; /* 调整高度为23px */
    display: flex;
    align-items: center;
    justify-content: flex-start;
    color: white;
    font-size: 12px;
    white-space: nowrap;
    overflow: visible;
    text-overflow: clip;
    font-weight: 500;
    z-index: 4;
    text-shadow: 0 0 2px rgba(0, 0, 0, 0.5);
    padding: 0 4px;
  }
  
  /* 移除表头圆角 */
  :deep(.ant-table) {
    border-radius: 0 !important;
  }
  
  :deep(.ant-table-container) {
    border-radius: 0 !important;
  }
  
  :deep(.ant-table-thead > tr:first-child > th:first-child) {
    border-top-left-radius: 0 !important;
  }
  
  :deep(.ant-table-thead > tr:first-child > th:last-child) {
    border-top-right-radius: 0 !important;
  }
  
  /* 调整表格单元格样式 */
  :deep(.ant-table-cell) {
    padding: 0 !important;
    margin: 0 !important;
    height: 40px !important;
    height: 25px !important;
    line-height: 25px !important;
  }
  
  /* 确保基本信息列内容垂直居中和字体颜色 */
  :deep(.ant-table-tbody > tr > td:not(:last-child)) {
    text-align: center;
    vertical-align: middle;
    padding: 0 8px;
    color: white !important; /* 确保字体颜色为白色 */
  }
  
  /* 甘特图标题行样式 */
  .gantt-header-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 20px;
  }
  
  /* 移除表格边框和悬停效果 */
  :deep(.ant-table-wrapper) {
    border: none !important;
  }
  </style>
  
  