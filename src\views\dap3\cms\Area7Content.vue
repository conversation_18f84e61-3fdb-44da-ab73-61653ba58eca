<template>
  <div class="area7-container">
    <div class="area7-main-title">
      <span class="title-text">物资准备</span>
      <span class="completion-rate">完成准备率: 92%</span>
    </div>
    <div class="content-area">
      <!-- 左侧饼图 -->
      <div class="chart-container">
        <div id="pieChart7_1" class="pie-chart"></div>
      </div>
      <!-- 右侧柱状图和标签 -->
      <div class="chart-container chart-with-labels">
        <div id="pieChart7_2" class="column-chart"></div>
        <!-- 自定义标签区域 -->
        <div class="label-container">
          <div class="data-label" v-for="(item, index) in pieData2Reversed" :key="index" :style="{ color: getColor(item.name) }">
            <div class="label-line" :style="{ backgroundColor: getColor(item.name) }"></div>
            <div class="label-content">
              <div class="label-name">{{ item.name }}</div>
              <div class="label-value">{{ item.value }} / {{ item.percentage }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from "vue";
import * as echarts from "echarts/core";
import { PieChart, BarChart } from "echarts/charts";
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
} from "echarts/components";
import { LabelLayout } from "echarts/features";
import { CanvasRenderer } from "echarts/renderers";

// 注册必要的ECharts组件
echarts.use([
  PieChart,
  BarChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  LabelLayout,
  CanvasRenderer,
]);

// 第一个饼图数据: 完成准备、未批准、其他三项
const pieData1 = [
  { name: "完成准备", value: 3000, percentage: "50%", color: "#00E676" }, // 绿色
  { name: "未批准", value: 1800, percentage: "30%", color: "#FFD54F" }, // 黄色
  { name: "其他", value: 720, percentage: "12%", color: "#29B6F6" }, // 蓝色
];

// 第二个饼图数据: 物资不足、供应商延期、其他三项
const pieData2 = [
  { name: "物资不足", value: 900, percentage: "15%", color: "#FF7043" }, // 橙红色
  { name: "供应商延期", value: 600, percentage: "10%", color: "#9C27B0" }, // 紫色
  { name: "其他", value: 300, percentage: "5%", color: "#29B6F6" }, // 蓝色
];

// 获取反转的数据用于标签显示
const pieData2Reversed = pieData2.slice().reverse();

// 获取标签颜色函数
const getColor = (name) => {
  const item = pieData2.find(item => item.name === name);
  return item ? item.color : '#ffffff';
};

// 计算总数
const total1 = pieData1.reduce((sum, item) => sum + item.value, 0);
const total2 = pieData2.reduce((sum, item) => sum + item.value, 0);

// 饼图实例引用
const chartInstance1 = ref(null);
const chartInstance2 = ref(null);

// 初始化第一个饼图 - 完成准备相关
const initChart1 = () => {
  const chartDom = document.getElementById("pieChart7_1");
  if (!chartDom) return;
  
  // 初始化ECharts实例
  chartInstance1.value = echarts.init(chartDom);
  
  // 配置项
  const option = {
    backgroundColor: "transparent",
    color: pieData1.map((item) => item.color),
    title: {
      text: total1.toString(),
      left: "center",
      top: "center",
      textStyle: {
        color: "#fff",
        fontSize: 18,
        fontWeight: "bold",
      },
    },
    tooltip: {
      trigger: "item",
      formatter: function(params) {
        const item = pieData1.find((pItem) => pItem.name === params.name);
        if (item) {
          return `<div style="padding: 8px;">
                    <div style="color:${item.color};font-weight:bold;margin-bottom:5px;">
                      ${item.name}
                    </div>
                    <div>数量: ${item.value}</div>
                    <div>占比: ${item.percentage}</div>
                  </div>`;
        }
        return params.name;
      },
      extraCssText: 'background-color: rgba(25, 35, 71, 0.9); border: 1px solid rgba(97, 174, 249, 0.6); border-radius: 4px; box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3); color: #fff;',
      confine: true, // 确保提示框不会超出容器
    },
    series: [
      {
        name: "物资准备",
        type: "pie",
        radius: ["40%", "88%"], // 进一步增大饼图半径
        center: ["50%", "55%"], // 饼图位置居中
        avoidLabelOverlap: true,
        itemStyle: {
          borderRadius: 2,
          borderColor: "#192347",
          borderWidth: 1.5,
        },
        label: {
          show: true,
          position: 'outside', // 外部显示标签
          formatter: function (params) {
            return [
              `{name|${params.name}}`,
              `{value|${params.value} / ${params.percent}%}`
            ].join('\n');
          },
          rich: {
            name: {
              fontSize: 12,
              fontWeight: 'bold',
              padding: [0, 0, 3, 0]
            },
            value: {
              fontSize: 11
            }
          }
        },
        labelLine: {
          show: true, // 显示标签线
          length: 10,
          length2: 10,
          smooth: true
        },
        emphasis: {
          scale: false, 
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: "rgba(0, 0, 0, 0.5)",
          },
        },
        data: pieData1.map((item) => ({
          value: item.value,
          name: item.name,
          itemStyle: {
            color: item.color,
          },
          label: {
            color: item.color
          },
          labelLine: {
            lineStyle: {
              color: item.color
            }
          }
        })),
      },
    ],
  };
  
  chartInstance1.value.setOption(option);
};

// 初始化第二个图表 - 简化版柱状图
const initChart2 = () => {
  const chartDom = document.getElementById("pieChart7_2");
  if (!chartDom) return;
  
  // 初始化ECharts实例
  chartInstance2.value = echarts.init(chartDom);
  
  // 检查数据是否全为0
  const allZero = pieData2.every(item => item.value === 0);
  // 如果全为0，使用小的固定高度值
  const fixedHeight = 5;
  
  // 配置项
  const option = {
    backgroundColor: "transparent",
    grid: {
      left: '15%',
      right: '55%',
      top: '10%',
      bottom: '5%',
      containLabel: false
    },
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow"
      }
    },
    xAxis: {
      show: false,
      type: 'category',
      data: ['']
    },
    yAxis: {
      show: false,
      type: 'value',
      max: allZero ? (fixedHeight * 3) : 'dataMax' // 限制最大高度
    },
    series: [
      // 调整顺序，让"物资不足"（橙红色）位于底部
      {
        name: pieData2[0].name, // "物资不足"
        type: 'bar',
        stack: '总量',
        barWidth: '80%',
        data: [allZero ? fixedHeight : pieData2[0].value],
        itemStyle: {
          color: pieData2[0].color
        },
        label: {
          show: false
        }
      },
      // "供应商延期"（紫色）位于中间
      {
        name: pieData2[1].name, // "供应商延期"
        type: 'bar',
        stack: '总量',
        barWidth: '80%',
        data: [allZero ? fixedHeight : pieData2[1].value],
        itemStyle: {
          color: pieData2[1].color
        },
        label: {
          show: false
        }
      },
      // "其他"（蓝色）位于顶部
      {
        name: pieData2[2].name, // "其他"
        type: 'bar',
        stack: '总量',
        barWidth: '80%',
        data: [allZero ? fixedHeight : pieData2[2].value],
        itemStyle: {
          color: pieData2[2].color
        },
        label: {
          show: false
        }
      }
    ]
  };
  
  chartInstance2.value.setOption(option);
};

const handleResize = () => {
  if (chartInstance1.value) {
    chartInstance1.value.resize();
  }
  if (chartInstance2.value) {
    chartInstance2.value.resize();
  }
};

onMounted(() => {
  initChart1();
  initChart2();
  window.addEventListener("resize", handleResize);
});

onUnmounted(() => {
  if (chartInstance1.value) {
    chartInstance1.value.dispose();
  }
  if (chartInstance2.value) {
    chartInstance2.value.dispose();
  }
  window.removeEventListener("resize", handleResize);
});
</script>

<style scoped>
.area7-container {
  background-color: #192347; 
  color: #fff;
  height: 100%;
  width: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  border-radius: 8px;
  overflow: hidden;
}

.area7-main-title {
  height: 30px;
  background-color: #61aef9;
  color: #ffffff;
  font-size: 16px;
  font-weight: 500;
  display: flex;
  align-items: center;
  padding-left: 15px;
  text-align: left;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  flex-shrink: 0;
  position: relative;
  justify-content: space-between;
  padding-right: 15px;
}

.area7-main-title::before {
  content: "";
  position: absolute;
  left: 0px;
  top: 5px;
  bottom: 6px;
  width: 4px;
  border-radius: 3px;
  background: linear-gradient(
    to bottom,
    rgba(255, 255, 255, 0.9),
    rgba(255, 255, 255, 0.3)
  );
}

.completion-rate {
  font-size: 14px;
}

.content-area {
  flex: 1;
  box-sizing: border-box;
  display: flex;
  flex-direction: row; /* 横向排列 */
  align-items: stretch; /* 让子元素撑满高度 */
  height: calc(100% - 30px); /* 减去标题高度 */
}

.chart-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
}

.chart-container:first-child {
  width: 60%; /* 左侧饼图容器占60%宽度 */
}

.chart-container:last-child {
  width: 40%; /* 右侧柱状图容器占40%宽度 */
}

.pie-chart {
  width: 100%;
  height: 100%; /* 增加图表高度利用率 */
  position: relative;
}

.column-chart {
  position: relative;
  width: 100%;
  height: 100%;
}

/* 新增样式 - 标签容器 */
.chart-with-labels {
  display: flex;
  position: relative;
}

.label-container {
  position: absolute;
  right: -8px;
  top: 10%;
  height: 80%;
  width: 55%;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
}

.data-label {
  display: flex;
  align-items: center;
  position: relative;
  margin-bottom: 8px;
  padding: 4px 0;
}

.label-line {
  height: 2px;
  width: 25px; /* 调整长度 */
  margin-right: 8px;
  position: relative;
  border-radius: 4px; /* 添加圆角 */
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2); /* 添加阴影 */
}


.label-content {
  display: flex;
  flex-direction: column;
}

.label-name {
  font-weight: bold;
  font-size: 13px;
  line-height: 18px;
  text-shadow: 0 0 2px rgba(0, 0, 0, 0.5);
}

.label-value {
  font-size: 12px;
  line-height: 16px;
}

/* 悬停效果 */
.data-label:hover .label-line::before {
  background: linear-gradient(to left, currentColor, currentColor); /* 悬停时线条变实 */
  transition: all 0.3s ease;
}

.data-label:hover .label-line::after {
  transform: scale(1.2);
  opacity: 1;
  transition: all 0.3s ease;
}
</style>
