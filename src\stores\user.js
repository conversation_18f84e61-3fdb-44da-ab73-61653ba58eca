import {
  defineStore
} from "pinia";

export const useUserStore = defineStore("user", {
  // 定义状态（state）
  state: () => {
    return {
      Arr: [{
          x: 2,
          y: 0,
          w: 2,
          h: 4,
          i: "1",
          componentName: "aboutIndex",
          name: '我是about'
        },
        {
          x: 4,
          y: 0,
          w: 2,
          h: 5,
          i: "2",
          componentName: "addindex",
          name: '我是addindex'
        },
        {
          x: 6,
          y: 0,
          w: 2,
          h: 3,
          i: "3",
          componentName: "delindex"
        },

        {
          x: 8,
          y: 0,
          w: 2,
          h: 3,
          i: "4",
          componentName: "oneIndex",
          name: '我是oneIndex'
        },
      ],
      age: 18,
      // 其他需要全局共享的数据
    };
  },

  // 定义方法（actions）
  actions: {},
});