<template>
  <div class="unfinished-work-section">
    <div class="section-title">
      <span class="title-icon"></span>
      机械现存未完工工作
    </div>
    <div class="table-wrapper">
      <el-table
        :data="unfinishedWorkData"
        border
        stripe
        style="width: 100%"
        :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
      >
        <el-table-column prop="workOrderNo" label="工单号" width="120" />
        <el-table-column prop="taskNo" label="任务号" width="120" />
        <el-table-column prop="title" label="工单任务标题" min-width="200" />
        <el-table-column prop="status" label="工单任务状态" width="120" />
        <el-table-column prop="maintenanceLevel" label="维修分级" width="100" />
        <el-table-column prop="plannedStartDate" label="计划开工" width="120" />
        <el-table-column prop="plannedEndDate" label="计划完工" width="120" />
        <el-table-column
          prop="responsiblePerson"
          label="工作负责人"
          width="120"
        />
        <el-table-column
          prop="preparationPerson"
          label="工作准备人"
          width="120"
        />
        <el-table-column prop="delayDays" label="延期时间" width="100" />
        <el-table-column
          prop="completionStatus"
          label="工单任务完成状态"
          width="140"
        />
      </el-table>
    </div>
  </div>
</template>

<script>
export default {
  name: "UnfinishedWorkTable",
  props: {
    queryParams: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      unfinishedWorkData: [
        {
          workOrderNo: "WO-2023-0001",
          taskNo: "T-0023",
          title: "3号机组给水泵检修",
          status: "进行中",
          maintenanceLevel: "B级",
          plannedStartDate: "2023-10-10",
          plannedEndDate: "2023-10-20",
          responsiblePerson: "张工",
          preparationPerson: "李工",
          delayDays: 0,
          completionStatus: 65,
        },
        {
          workOrderNo: "WO-2023-0002",
          taskNo: "T-0025",
          title: "4号机组汽轮机调速系统检修",
          status: "待处理",
          maintenanceLevel: "A级",
          plannedStartDate: "2023-10-12",
          plannedEndDate: "2023-10-25",
          responsiblePerson: "王工",
          preparationPerson: "赵工",
          delayDays: 2,
          completionStatus: 30,
        },
        {
          workOrderNo: "WO-2023-0003",
          taskNo: "T-0026",
          title: "8号机组凝汽器管道更换",
          status: "暂停",
          maintenanceLevel: "A级",
          plannedStartDate: "2023-09-15",
          plannedEndDate: "2023-10-05",
          responsiblePerson: "刘工",
          preparationPerson: "孙工",
          delayDays: 15,
          completionStatus: 40,
        },
        {
          workOrderNo: "WO-2023-0004",
          taskNo: "T-0030",
          title: "锅炉给水泵检修",
          status: "进行中",
          maintenanceLevel: "C级",
          plannedStartDate: "2023-10-05",
          plannedEndDate: "2023-10-15",
          responsiblePerson: "钱工",
          preparationPerson: "周工",
          delayDays: 0,
          completionStatus: 85,
        },
        {
          workOrderNo: "WO-2023-0005",
          taskNo: "T-0031",
          title: "除氧器水位控制阀更换",
          status: "进行中",
          maintenanceLevel: "B级",
          plannedStartDate: "2023-10-08",
          plannedEndDate: "2023-10-18",
          responsiblePerson: "陈工",
          preparationPerson: "吴工",
          delayDays: 0,
          completionStatus: 50,
        },
      ],
    };
  },
  mounted() {},
  methods: {},
};
</script>

<style scoped>
/* 机械现存未完工工作区域样式 */
.unfinished-work-section {
  margin-bottom: 30px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  width: 100%;
}

.table-wrapper {
  padding: 15px;
  overflow-x: auto;
}

/* 标题样式 */
.section-title {
  padding: 15px 20px;
  font-size: 15px;
  color: #666;
  font-weight: 500;
  display: flex;
  align-items: center;
  background: linear-gradient(to right, #f8f9fc, #ffffff);
  border-left: 3px solid #1890ff;
  letter-spacing: 0.5px;
  margin-bottom: 0;
  box-shadow: 0 1px 0 rgba(0, 0, 0, 0.05);
}

.title-icon {
  display: inline-block;
  width: 4px;
  height: 16px;
  margin-right: 8px;
  background-color: #1890ff;
  border-radius: 2px;
}

/* 表格内的标签样式 */
.unfinished-work-section .el-tag {
  font-size: 13px;
  padding: 4px 8px;
}
</style>
