<template>
  <a-modal
    :open="isOpen"
    title="新增风险管控"
    @ok="handleOk"
    @cancel="handleCancel"
    width="800px"
    okText="确定"
    cancelText="取消"
  >
    <a-form ref="formRef" :model="formState">
      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="风险类型：" name="riskType">
            <a-input
              v-model:value="formState.riskType"
              placeholder="请输入风险类型"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="风险点：" name="riskPoint">
            <a-input
              v-model:value="formState.riskPoint"
              placeholder="请输入风险点"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="24">
          <a-form-item label="风险后果：" name="riskConsequence">
            <a-textarea
              v-model:value="formState.riskConsequence"
              :rows="4"
              placeholder="请输入风险后果"
              :resize="false"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="24">
          <a-form-item label="管控要求：" name="controlRequirement">
            <a-textarea
              v-model:value="formState.controlRequirement"
              :rows="4"
              placeholder="请输入管控要求"
              :resize="false"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="24">
          <a-form-item label="管控措施：" name="controlMeasures">
            <a-textarea
              v-model:value="formState.controlMeasures"
              :rows="4"
              placeholder="请输入管控措施"
              :resize="false"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="措施落实情况：" name="implementationStatus">
            <a-select
              v-model:value="formState.implementationStatus"
              placeholder="请选择"
            >
              <a-select-option value="已落实">已落实</a-select-option>
              <a-select-option value="未落实">未落实</a-select-option>
              <a-select-option value="部分落实">部分落实</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </a-modal>
</template>

<script setup>
import { ref, reactive } from "vue";

const isOpen = ref(false);
const formRef = ref(null);

const formState = reactive({
  riskType: "",
  riskPoint: "",
  riskConsequence: "",
  controlRequirement: "",
  controlMeasures: "",
  implementationStatus: undefined,
});

const handleOk = () => {
  console.log("表单数据：", formState);
  isOpen.value = false;
};

const handleCancel = () => {
  isOpen.value = false;
};

// 暴露方法给父组件
defineExpose({
  openModal: () => {
    isOpen.value = true;
  },
});
</script>
