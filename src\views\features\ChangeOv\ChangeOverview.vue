<template>
  <a-card
    class="overview-card"
    :bordered="false"
    style="height: 100%; display: flex; flex-direction: column"
  >
    <template #title>
      <div class="card-title">
        <div class="title-icon"></div>
        <span>变更总览</span>
      </div>
    </template>

    <div class="divider"></div>

    <div class="filter-legend-row">
      <div class="card-filter">
        <div class="filter-item">
          <span>责任部门:</span>
          <a-select
            placeholder="请选择部门"
            size="small"
            style="width: 120px"
            allow-clear
          >
            <a-select-option value="dept1">运维部门</a-select-option>
            <a-select-option value="dept2">技术部门</a-select-option>
          </a-select>
        </div>
        <div class="filter-item">
          <span>变更类型:</span>
          <a-select
            placeholder="请选择类型"
            size="small"
            style="width: 120px"
            allow-clear
          >
            <a-select-option value="type1">普通变更</a-select-option>
            <a-select-option value="type2">紧急变更</a-select-option>
          </a-select>
        </div>
        <a-button type="primary" size="small">查询</a-button>
      </div>
    </div>

    <div
      class="chart-container"
      ref="barChartRef"
      style="flex: 1; min-height: 0"
    ></div>
  </a-card>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from "vue";
import * as echarts from "echarts/core";

// 图表引用
const barChartRef = ref(null);

// 图表实例
let barChart = null;

// 初始化图表
const initChart = () => {
  if (barChartRef.value && !barChart) {
    // Ensure chart is initialized only once
    barChart = echarts.init(barChartRef.value);
    const barOption = {
      tooltip: {
        trigger: "axis",
        axisPointer: { type: "shadow" },
      },
      legend: {
        data: ["已完成", "未完成"],
        top: "top",
        left: "center",
        itemWidth: 10,
        itemHeight: 10,
        textStyle: { fontSize: 12 },
      },
      grid: {
        left: "3%",
        right: "4%",
        bottom: "3%",
        top: "40px",
        containLabel: true,
      },
      xAxis: {
        type: "category",
        data: [
          "初步设计",
          "详细设计",
          "施工方案",
          "技术交底",
          "开工确认",
          "已开工",
          "已完工",
          "投用检查",
        ],
        axisLabel: {
          interval: 0,
          fontSize: 12,
          color: "#333",
          margin: 12,
        },
        axisTick: { alignWithLabel: true },
        axisLine: { lineStyle: { color: "#E0E0E0" } },
      },
      yAxis: {
        type: "value",
        splitLine: {
          lineStyle: {
            type: "dashed",
            color: "#E0E0E0",
          },
        },
        axisLabel: { color: "#666" },
      },
      series: [
        {
          name: "已完成",
          type: "bar",
          stack: "total",
          barWidth: "25%",
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: "#36a3ff" },
              { offset: 1, color: "#1677ff" },
            ]),
          },
          label: {
            show: true,
            position: "inside",
            color: "#fff",
            fontSize: 12,
          },
          data: [120, 132, 101, 134, 90, 230, 210, 180], // 示例数据
        },
        {
          name: "未完成",
          type: "bar",
          stack: "total",
          barWidth: "25%",
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: "#73de8a" },
              { offset: 1, color: "#3fbf70" },
            ]),
          },
          label: {
            show: true,
            position: "inside",
            color: "#fff",
            fontSize: 12,
          },
          data: [60, 72, 71, 74, 190, 130, 110, 50], // 示例数据
        },
      ],
    };
    barChart.setOption(barOption);
  }
};

// 窗口大小变化时重绘图表
const resizeChart = () => {
  barChart?.resize();
};

// 生命周期钩子
onMounted(() => {
  // Use nextTick to ensure the DOM element is ready
  nextTick(() => {
    initChart();
  });
  window.addEventListener("resize", resizeChart);
});

onUnmounted(() => {
  window.removeEventListener("resize", resizeChart);
  barChart?.dispose();
  barChart = null; // Prevent memory leaks
});
</script>

<style scoped>
.overview-card {
  background-color: #fff;
  border-radius: 4px;
  padding: 10px;
  box-sizing: border-box;
}

.card-title {
  display: flex;
  align-items: center;
  font-size: 17px;
  font-weight: 600;
  color: #333;
  line-height: 1.2;
}

.title-icon {
  width: 3px;
  height: 14px;
  background-color: #1677ff;
  border-radius: 2px;
  margin-right: 6px;
  flex-shrink: 0;
}

.divider {
  height: 1px;
  background-color: #f0f0f0;
  margin-top: 8px;
  margin-bottom: 8px;
}

.filter-legend-row {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 8px;
  flex-wrap: wrap;
  gap: 10px;
}

.card-filter {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  align-items: center;
  margin-bottom: 0;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #666;
}

.chart-container {
  width: 100%;
  height: 100%;
}

:deep(.ant-card-head) {
  min-height: auto;
  padding: 0;
  border-bottom: none;
}

:deep(.ant-card-head-title) {
  padding: 0;
}

:deep(.ant-card-body) {
  padding: 0;
  display: flex;
  flex-direction: column;
  height: 100%;
  flex: 1;
  min-height: 0;
}

:deep(.ant-select-selector) {
  font-size: 12px;
}
</style>
