<template>
  <a-modal
    v-model:open="visible"
    title="迎峰度冬,度夏设置"
    @ok="handleOk"
    @cancel="handleCancel"
    okText="确认"
    cancelText="取消"
    :width="500"
  >
    <a-form :model="formState" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
      <a-row>
        <a-col :span="12">
          <a-form-item label="迎峰度夏">
            <a-date-picker v-model:value="formState.summerStart" :locale="locale" :format="'MM月DD日'" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="至">
            <a-date-picker v-model:value="formState.summerEnd" :locale="locale" :format="'MM月DD日'" />
          </a-form-item>
        </a-col>
      </a-row>
      <a-row>
        <a-col :span="12">
          <a-form-item label="迎峰度冬">
            <a-date-picker v-model:value="formState.winterStart" :locale="locale" :format="'MM月DD日'" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="至春节前第">
            <a-input-number v-model:value="formState.daysBeforeSpring" :min="1" :max="99" style="width: 100%" />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import type { Dayjs } from 'dayjs';
import locale from 'ant-design-vue/es/date-picker/locale/zh_CN';

interface FormState {
  summerStart: Dayjs | null;
  summerEnd: Dayjs | null;
  winterStart: Dayjs | null;
  daysBeforeSpring: number;
}

const visible = ref(false);
const formState = ref<FormState>({
  summerStart: null,
  summerEnd: null,
  winterStart: null,
  daysBeforeSpring: 1
});

// 打开模态框的方法
const openModal = (data?: any) => {
  if (data) {
    formState.value = {
      ...formState.value,
      ...data
    };
  }
  visible.value = true;
};

// 处理确认
const handleOk = () => {
  visible.value = false;
};

// 处理取消
const handleCancel = () => {
  visible.value = false;
};

// 暴露方法给父组件
defineExpose({
  openModal
});
</script> 