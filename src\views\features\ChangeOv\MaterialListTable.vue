<template>
  <div class="material-list-container">
    <div class="search-section">
      <div class="top-search-row">
        <div class="search-item">
          <span>实施窗口:</span>
          <a-input placeholder="请输入" style="width: 180px" />
        </div>
        <div class="search-item">
          <span>变更负责人:</span>
          <a-input placeholder="请输入" style="width: 180px" />
        </div>
        <div class="search-item">
          <span>变更单号:</span>
          <a-input placeholder="请输入" style="width: 180px" />
        </div>
        <a-button type="primary">导入模板</a-button>
        <a-button>导入</a-button>
      </div>

      <div class="divider"></div>

      <div class="filter-row">
        <div class="filter-item">
          <span>物资编码:</span>
          <a-input placeholder="请输入" style="width: 150px" />
        </div>
        <div class="filter-item">
          <span>工单号码:</span>
          <a-input placeholder="请输入" style="width: 150px" />
        </div>
        <div class="filter-item">
          <span>工单类型:</span>
          <a-select placeholder="请选择" style="width: 150px">
            <a-select-option value="type1">全部</a-select-option>
          </a-select>
        </div>
        <div class="filter-item">
          <span>物件分类:</span>
          <a-select placeholder="请选择" style="width: 150px">
            <a-select-option value="type1">全部</a-select-option>
          </a-select>
        </div>
        <div class="filter-item">
          <span>物资名称:</span>
          <a-input placeholder="请输入" style="width: 150px" />
        </div>
        <div class="filter-item">
          <span>预留成功状况:</span>
          <a-select placeholder="请选择" style="width: 150px">
            <a-select-option value="type1">全部</a-select-option>
          </a-select>
        </div>
        <div class="filter-item">
          <span>设备工程师:</span>
          <a-input placeholder="请输入" style="width: 150px" />
        </div>
        <div class="filter-item">
          <span>责任班组代码:</span>
          <a-input placeholder="请输入" style="width: 150px" />
        </div>
        <a-button type="primary" icon="search">查询</a-button>
      </div>
    </div>

    <a-table
      :columns="columns"
      :data-source="tableData"
      :rowSelection="rowSelection"
      :scroll="{ x: 2000, y: 400 }"
      bordered
      size="middle"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'operation'">
          <a-button type="link" @click="showEditModal(record)">修改</a-button>
        </template>
      </template>
    </a-table>

    <!-- 编辑模态框 -->
    <a-modal
      v-model:visible="modalVisible"
      title="修改信息"
      @ok="handleOk"
      @cancel="handleCancel"
      :maskClosable="false"
    >
      <a-form
        :model="editForm"
        layout="horizontal"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-item label="物资编码">
          <a-input v-model:value="editForm.materialCode" />
        </a-form-item>
        <a-form-item label="工单任务号">
          <a-input v-model:value="editForm.workOrderNo" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive } from "vue";

// 表格列定义
const columns = [
  {
    title: "物料申请号(MRR)",
    dataIndex: "materialRequestNo",
    width: 150,
    fixed: "left",
  },
  {
    title: "物资编码",
    dataIndex: "materialCode",
    width: 120,
  },
  {
    title: "物资名称",
    dataIndex: "materialName",
    width: 250,
  },
  {
    title: "物资分类",
    dataIndex: "materialType",
    width: 100,
  },
  {
    title: "责任分类",
    dataIndex: "responsibilityType",
    width: 100,
  },
  {
    title: "预留单号",
    dataIndex: "reservationNo",
    width: 120,
  },
  {
    title: "预留行号",
    dataIndex: "reservationLineNo",
    width: 100,
  },
  {
    title: "预留成功状况",
    dataIndex: "reservationStatus",
    width: 120,
  },
  {
    title: "预留行状态",
    dataIndex: "reservationLineStatus",
    width: 100,
  },
  {
    title: "已删除项目",
    dataIndex: "deletedItem",
    width: 100,
  },
  {
    title: "采购申请号",
    dataIndex: "purchaseRequestNo",
    width: 120,
  },
  {
    title: "采购申请行",
    dataIndex: "purchaseRequestLine",
    width: 100,
  },
  {
    title: "采购订单及交货单号",
    dataIndex: "purchaseOrderNo",
    width: 180,
  },
  {
    title: "工单号码",
    dataIndex: "workOrderNo",
    width: 120,
  },
  {
    title: "操作",
    dataIndex: "operation",
    width: 100,
    fixed: "right",
  },
];

// 行选择配置
const rowSelection = {
  columnWidth: 40,
};

// 表格数据
const tableData = [
  {
    key: "1",
    materialRequestNo: "00237388",
    materialCode: "2140200119",
    materialName: "非标外壳端编织插头(Φ338×314×123.5)",
    materialType: "易损耗物资",
    responsibilityType: "C",
    reservationNo: "0000625702",
    reservationLineNo: "0003",
    reservationStatus: "全部预留成功",
    reservationLineStatus: "全部领用",
    deletedItem: "",
    purchaseRequestNo: "0085269-01",
    purchaseRequestLine: "",
    purchaseOrderNo: "",
    workOrderNo: "",
  },
  {
    key: "2",
    materialRequestNo: "00237388",
    materialCode: "2140200690",
    materialName: "非标基本型端编织插头(Φ200×172×103.5)",
    materialType: "易损耗物资",
    responsibilityType: "C",
    reservationNo: "0000625702",
    reservationLineNo: "0006",
    reservationStatus: "全部预留成功",
    reservationLineStatus: "全部领用",
    deletedItem: "",
    purchaseRequestNo: "0085269-01",
    purchaseRequestLine: "",
    purchaseOrderNo: "",
    workOrderNo: "",
  },
  {
    key: "3",
    materialRequestNo: "00237388",
    materialCode: "9000069987",
    materialName: "垫片\\PARBRHD250 RPITFE 815-ID290-OD388×3.0",
    materialType: "易损耗物资",
    responsibilityType: "C",
    reservationNo: "0000625702",
    reservationLineNo: "0005",
    reservationStatus: "全部预留成功",
    reservationLineStatus: "全部领用",
    deletedItem: "",
    purchaseRequestNo: "0085269-01",
    purchaseRequestLine: "",
    purchaseOrderNo: "",
    workOrderNo: "",
  },
  {
    key: "4",
    materialRequestNo: "00237388",
    materialCode: "9000069988",
    materialName: "垫科\\PARBRHD250 RPITFE 815-ID168-OD223×2.0",
    materialType: "易损耗物资",
    responsibilityType: "C",
    reservationNo: "0000625702",
    reservationLineNo: "0004",
    reservationStatus: "全部预留成功",
    reservationLineStatus: "全部领用",
    deletedItem: "",
    purchaseRequestNo: "0085269-01",
    purchaseRequestLine: "",
    purchaseOrderNo: "",
    workOrderNo: "",
  },
  {
    key: "5",
    materialRequestNo: "00237389",
    materialCode: "2140200119",
    materialName: "非标外壳端编织插头(Φ338×314×123.5)",
    materialType: "易损耗物资",
    responsibilityType: "C",
    reservationNo: "0000625694",
    reservationLineNo: "0003",
    reservationStatus: "全部预留成功",
    reservationLineStatus: "未领用",
    deletedItem: "",
    purchaseRequestNo: "0085270-01",
    purchaseRequestLine: "",
    purchaseOrderNo: "",
    workOrderNo: "",
  },
  {
    key: "6",
    materialRequestNo: "00237389",
    materialCode: "2140200690",
    materialName: "非标基本型端编织插头(Φ200×172×103.5)",
    materialType: "易损耗物资",
    responsibilityType: "C",
    reservationNo: "0000625694",
    reservationLineNo: "0004",
    reservationStatus: "全部预留成功",
    reservationLineStatus: "全部领用",
    deletedItem: "",
    purchaseRequestNo: "0085270-01",
    purchaseRequestLine: "",
    purchaseOrderNo: "",
    workOrderNo: "",
  },
  {
    key: "7",
    materialRequestNo: "00237389",
    materialCode: "9000069987",
    materialName: "垫片\\PARBRHD250 RPITFE 815-ID290-OD388×3.0",
    materialType: "易损耗物资",
    responsibilityType: "C",
    reservationNo: "0000625694",
    reservationLineNo: "0005",
    reservationStatus: "全部预留成功",
    reservationLineStatus: "全部领用",
    deletedItem: "",
    purchaseRequestNo: "0085270-01",
    purchaseRequestLine: "",
    purchaseOrderNo: "",
    workOrderNo: "",
  },
  {
    key: "8",
    materialRequestNo: "00237389",
    materialCode: "9000069988",
    materialName: "垫科\\PARBRHD250 RPITFE 815-ID168-OD223×2.0",
    materialType: "易损耗物资",
    responsibilityType: "C",
    reservationNo: "0000625694",
    reservationLineNo: "0006",
    reservationStatus: "全部预留成功",
    reservationLineStatus: "全部领用",
    deletedItem: "",
    purchaseRequestNo: "0085270-01",
    purchaseRequestLine: "",
    purchaseOrderNo: "",
    workOrderNo: "",
  },
];

// 模态框相关
const modalVisible = ref(false);
const currentRecord = ref(null);
const editForm = reactive({
  materialCode: "",
  workOrderNo: "",
});

// 显示编辑模态框
const showEditModal = (record) => {
  currentRecord.value = record;
  editForm.materialCode = record.materialCode;
  editForm.workOrderNo = record.workOrderNo;
  modalVisible.value = true;
};

// 处理确认
const handleOk = () => {
  if (currentRecord.value) {
    // 更新数据
    currentRecord.value.materialCode = editForm.materialCode;
    currentRecord.value.workOrderNo = editForm.workOrderNo;
    
    // 关闭模态框
    modalVisible.value = false;
  }
};

// 处理取消
const handleCancel = () => {
  modalVisible.value = false;
};
</script>

<style scoped>
.material-list-container {
  width: 100%;
}

.search-section {
  margin-bottom: 16px;
  background-color: #f0f2f5;
  padding: 16px;
  border-radius: 4px;
}

.top-search-row {
  display: flex;
  margin-bottom: 16px;
  gap: 16px;
  align-items: center;
  padding-bottom: 12px;
}

.divider {
  height: 1px;
  background-color: #d9d9d9;
  margin-bottom: 16px;
}

.filter-row {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;
  padding-top: 4px;
}

.search-item,
.filter-item {
  display: flex;
  align-items: center;
}

.search-item span,
.filter-item span {
  margin-right: 8px;
  white-space: nowrap;
}

.form-item {
  margin-bottom: 16px;
}

.form-label {
  display: inline-block;
  width: 100px;
  text-align: right;
  margin-right: 8px;
}

:deep(.ant-table-thead > tr > th) {
  background-color: #1e3a8a;
  color: white;
  text-align: center;
  padding: 8px;
}

:deep(.ant-table-tbody > tr > td) {
  padding: 4px 8px;
  font-size: 14px;
  text-align: center;
}

:deep(.ant-table-tbody > tr:hover > td) {
  background-color: #e6f7ff;
}

:deep(.ant-table-row-selected > td) {
  background-color: #e6f7ff;
}
</style>
