<template>
  <div
    ref="floatButtonRef"
    class="global-float-button"
    :style="buttonStyle"
    @mousedown="startDrag"
    @touchstart.prevent="startDrag"
    @click.stop="openModal"
    title="显示帮助"
  >
    <QuestionCircleOutlined />
  </div>

  <a-modal
    v-model:open="isModalVisible"
    title="智能帮助助手"
    :width="800"
    :footer="null"
    :maskClosable="true"
    :closable="true"
    wrapClassName="help-modal-wrapper"
    @cancel="handleCancel"
    destroyOnClose
  >
    <HelpChatInterface v-if="isModalVisible" />
  </a-modal>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onBeforeUnmount } from "vue";
import { Modal as AModal } from "ant-design-vue";
import { QuestionCircleOutlined } from "@ant-design/icons-vue";
import HelpChatInterface from "./HelpChatInterface.vue";

const floatButtonRef = ref(null);
const isModalVisible = ref(false);
const isDragging = ref(false);
let movedDistance = 0; // 用于区分点击和拖拽

const buttonPosition = reactive({
  x: window.innerWidth - 70,
  y: window.innerHeight - 70,
});
const dragOffset = reactive({ x: 0, y: 0 });

const buttonStyle = computed(() => ({
  top: `${buttonPosition.y}px`,
  left: `${buttonPosition.x}px`,
}));

const startDrag = (event) => {
  movedDistance = 0; // 重置移动距离
  isDragging.value = true;
  const clientX =
    event.type === "touchstart" ? event.touches[0].clientX : event.clientX;
  const clientY =
    event.type === "touchstart" ? event.touches[0].clientY : event.clientY;

  dragOffset.x = clientX - buttonPosition.x;
  dragOffset.y = clientY - buttonPosition.y;

  document.addEventListener("mousemove", handleDrag);
  document.addEventListener("mouseup", stopDrag);
  document.addEventListener("touchmove", handleDrag, { passive: false });
  document.addEventListener("touchend", stopDrag);

  if (floatButtonRef.value) {
    floatButtonRef.value.style.cursor = "grabbing";
    floatButtonRef.value.style.transition = "none"; // 拖动时不应用过渡
  }
};

const handleDrag = (event) => {
  if (!isDragging.value) return;

  event.preventDefault();

  const clientX =
    event.type === "touchmove" ? event.touches[0].clientX : event.clientX;
  const clientY =
    event.type === "touchmove" ? event.touches[0].clientY : event.clientY;

  let newX = clientX - dragOffset.x;
  let newY = clientY - dragOffset.y;

  // 记录移动距离
  movedDistance +=
    Math.abs(newX - buttonPosition.x) + Math.abs(newY - buttonPosition.y);

  // 边界检查
  const buttonWidth = floatButtonRef.value?.offsetWidth || 50;
  const buttonHeight = floatButtonRef.value?.offsetHeight || 50;
  const maxX = window.innerWidth - buttonWidth;
  const maxY = window.innerHeight - buttonHeight;

  // 更新坐标，限制在边界内
  buttonPosition.x = Math.max(0, Math.min(newX, maxX));
  buttonPosition.y = Math.max(0, Math.min(newY, maxY));
};

const stopDrag = (event) => {
  if (!isDragging.value) return;
  isDragging.value = false;

  document.removeEventListener("mousemove", handleDrag);
  document.removeEventListener("mouseup", stopDrag);
  document.removeEventListener("touchmove", handleDrag);
  document.removeEventListener("touchend", stopDrag);

  if (floatButtonRef.value) {
    floatButtonRef.value.style.cursor = "grab";
    floatButtonRef.value.style.transition = "background-color 0.3s ease"; // 恢复过渡
  }

  // 可选：保存位置
  localStorage.setItem("globalButtonPos", JSON.stringify(buttonPosition));
};

const openModal = () => {
  // 只有移动距离很小（小于阈值，例如5像素）才视为点击
  if (movedDistance < 5) {
    isModalVisible.value = true;
  }
  // 重置拖拽状态，以防万一 stopDrag 未正确触发
  isDragging.value = false;
  movedDistance = 0; // 重置移动距离
};

const handleCancel = () => {
  isModalVisible.value = false;
};

onMounted(() => {
  const savedPos = localStorage.getItem("globalButtonPos");
  if (savedPos) {
    try {
      const pos = JSON.parse(savedPos);
      // 边界检查，防止保存的位置在新窗口尺寸下超出屏幕
      const buttonWidth = floatButtonRef.value?.offsetWidth || 50;
      const buttonHeight = floatButtonRef.value?.offsetHeight || 50;
      const maxX = window.innerWidth - buttonWidth;
      const maxY = window.innerHeight - buttonHeight;

      buttonPosition.x = Math.max(0, Math.min(pos.x, maxX));
      buttonPosition.y = Math.max(0, Math.min(pos.y, maxY));
    } catch (e) {
      console.error("加载按钮位置失败", e);
      // 使用默认值
      buttonPosition.x = window.innerWidth - 70;
      buttonPosition.y = window.innerHeight - 70;
    }
  } else {
    // 默认值
    buttonPosition.x = window.innerWidth - 70;
    buttonPosition.y = window.innerHeight - 70;
  }

  if (floatButtonRef.value) {
    floatButtonRef.value.style.cursor = "grab";
  }
});

onBeforeUnmount(() => {
  document.removeEventListener("mousemove", handleDrag);
  document.removeEventListener("mouseup", stopDrag);
  document.removeEventListener("touchmove", handleDrag);
  document.removeEventListener("touchend", stopDrag);
});
</script>

<style scoped>
.global-float-button {
  position: fixed;
  z-index: 9999;
  width: 50px;
  height: 50px;
  background-color: #1890ff;
  color: white;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 24px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  user-select: none;
  transition: background-color 0.3s ease; /* 只对背景色应用过渡 */
  cursor: grab;
}

.global-float-button:hover {
  background-color: #40a9ff;
}
</style>

<style>
.help-modal-wrapper .ant-modal-body {
  padding: 0 !important; /* Force remove padding */
  height: 70vh; /* Taller - 70% of viewport height */
  /* Or use a fixed height like 650px or 700px if preferred */
  /* height: 650px; */
  display: flex; /* Allow chat interface to fill the height */
}

/* Optional: Style the modal header/title */
.help-modal-wrapper .ant-modal-header {
  background-color: #f0f2f5; /* Light background for header */
  border-bottom: 1px solid #e8e8e8;
}
.help-modal-wrapper .ant-modal-title {
    font-weight: 600;
}
</style>
