import {
  createRouter,
  createWebHistory
} from "vue-router";




const router = createRouter({
  history: createWebHistory(
    import.meta.env.BASE_URL),
  routes: [
    // {
    //   path: "/",
    //   name: "login",
    //   component: () => import("@/views/Login/LoginIndex.vue"),
    // },
    // {
    //   path: "/",
    //   name: "dap",
    //   component: () => import("@/views/dap/index.vue"),
    //   // component: () => import("@/views/home/<USER>/plan1.vue"),
    // },
    {
      path: "/menu",
      name: "dap",
      // component: () => import("@/views/dap2/index.vue"),
      component: () => import("@/views/dap/index.vue"),
      // component: () => import("@/views/home/<USER>/plan1.vue"),
    },
    {
      path: "/home",
      name: "home",
      component: () => import("@/views/home/<USER>"),
      // component: () => import("@/views/home/<USER>/plan1.vue"),
    },
    {
      path: "/menu",
      name: "menu",
      redirect: '/features',
      component: () => import("@/views/Menu/Boxindex.vue"),
      children: [{
          path: "/features",
          name: "feature",
          // component: () => import("@/views/features/featuresIndex.vue"),
          component: () => import("@/views/features/add.vue"),
          // component: () => import("@/views/home/<USER>/index.vue"),
        },
        {
          path: "/dap4",
          name: "dap4",
          component: () => import("@/views/dap4/index.vue"),
        },
        {
          path: "/dap5",
          name: "dap5",
          component: () => import("@/views/dap5/index.vue"),
        },
        {
          path: "/dap",
          name: "dap",
          component: () => import("@/views/dap3/index.vue"),
        },
        {
          path: "/a",
          name: "a",
          component: () => import("@/views/features/cms/a.vue"),
        },

        {
          path: "/a",
          name: "a",
          component: () => import("@/views/features/cms/a.vue"),
        },
        {
          path: "/b",
          name: "b",
          component: () => import("@/views/features/cms/b.vue"),
        },
        {
          path: "/c",
          name: "c",
          component: () => import("@/views/features/cms/c.vue"),
        },
        {
          path: "/d",
          name: "d",
          component: () => import("@/views/features/cms/d.vue"),
        },
        {
          path: "/f",
          name: "f",
          component: () => import("@/views/features/cms/f.vue"),
        },
        {
          path: "/g",
          name: "g",
          component: () => import("@/views/features/cms/g.vue"),
        },
        {
          path: "/timeline",
          name: "timeline",
          component: () => import("@/views/ProjectTimeline.vue"),
        },
        {
          path: "/visualization",
          name: "addTable",
          component: () => import("@/views/about/AddTable.vue"),
        },
        {
          path: "/profile",
          name: "Features",
          component: () => import("@/views/about/index.vue"),
        },
        {
          path: "/settings",
          name: "settings",
          children: [{
              path: "user-settings",
              name: "user-settings",
              component: () => import("@/views/settings/user-settings.vue"),
            },
            {
              path: "app-settings",
              name: "app-settings",
              component: () => import("@/views/settings/app-settings.vue"),
            },
          ],
        },
      ],
    },
  ],
});

export default router;