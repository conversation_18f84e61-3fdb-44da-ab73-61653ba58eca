<template>
  <div class="name-select">
    <a-input-search
      v-model:value="selectedName"
      placeholder="请选择人员"
      :readonly="true"
      @search="showModal"
      style="width: 100%"
    />

    <a-modal
      v-model:visible="visible"
      title="选择人员"
      @ok="handleOk"
      @cancel="handleCancel"
      width="800px"
    >
      <!-- 搜索区域 -->
      <div class="search-area">
        <a-row :gutter="16">
          <a-col :span="8">
            <div class="search-item">
              <span class="label">姓名：</span>
              <a-input
                v-model:value="searchForm.name"
                placeholder="请输入姓名"
                allowClear
                @pressEnter="handleSearch"
              />
            </div>
          </a-col>
          <a-col :span="8">
            <div class="search-item">
              <span class="label">职工号：</span>
              <a-input
                v-model:value="searchForm.staffAccount"
                placeholder="请输入职工号"
                allowClear
                @pressEnter="handleSearch"
              />
            </div>
          </a-col>
          <a-col :span="8">
            <a-button type="primary" @click="handleSearch"> 查询 </a-button>
          </a-col>
        </a-row>
      </div>

      <!-- 表格区域 -->
      <div class="table-container">
        <a-table
          :dataSource="tableData"
          :columns="columns"
          :pagination="false"
          :scroll="{ y: tableScrollHeight }"
          row-key="staffAccount"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'action'">
              <a-radio
                :checked="selectedRowKey === record.staffAccount"
                @change="() => handleSelect(record)"
              />
            </template>
          </template>
        </a-table>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from "vue";

const props = defineProps({
  value: {
    type: String,
    default: "",
  },
});

const emit = defineEmits(["update:value", "select"]);

// 响应式状态
const visible = ref(false);
const selectedName = ref(props.value);
const selectedRowKey = ref("");
const selectedRow = ref(null);
const tableScrollHeight = ref(400);

// 搜索表单
const searchForm = ref({
  name: "",
  staffAccount: "",
});

// 表格数据
const tableData = ref([
  {
    name: "张三",
    staffAccount: "001",
    section: "技术部",
    unit: "总公司",
  },
  // ... 其他数据
]);

// 表格列定义
const columns = [
  {
    title: "选择",
    key: "action",
    width: 60,
    align: "center",
    customRender: ({ record }) => ({
      props: {
        checked: selectedRowKey.value === record.staffAccount,
      },
      on: {
        change: () => handleSelect(record),
      },
    }),
  },
  {
    title: "姓名",
    dataIndex: "name",
    width: 100,
  },
  {
    title: "职工号",
    dataIndex: "staffAccount",
    width: 120,
  },
  {
    title: "处室",
    dataIndex: "section",
    width: 150,
  },
  {
    title: "单位",
    dataIndex: "unit",
    width: 200,
  },
];

// 显示弹窗
const showModal = () => {
  visible.value = true;
  handleSearch();
};

// 处理搜索
const handleSearch = () => {
  // 这里添加实际的搜索逻辑
  console.log("搜索条件:", searchForm.value);
};

// 处理选择
const handleSelect = (record) => {
  selectedRowKey.value = record.staffAccount;
  selectedRow.value = record;
};

// 确定选择
const handleOk = () => {
  if (selectedRow.value) {
    selectedName.value = selectedRow.value.name;
    emit("update:value", selectedRow.value.name);
    emit("select", selectedRow.value);
  }
  handleCancel();
};

// 取消选择
const handleCancel = () => {
  visible.value = false;
  selectedRowKey.value = "";
  selectedRow.value = null;
};

// 监听 value 变化
watch(
  () => props.value,
  (newVal) => {
    selectedName.value = newVal;
  }
);

// 组件挂载时设置表格高度
onMounted(() => {
  // 可以根据实际情况调整表格高度
  tableScrollHeight.value = window.innerHeight * 0.4;
});
</script>

<style scoped>
.name-select {
  width: 100%;
}

.search-area {
  margin-bottom: 16px;
}

.search-item {
  display: flex;
  align-items: center;
}

.search-item .label {
  min-width: 60px;
  margin-right: 8px;
}

.table-container {
  margin-top: 16px;
}

:deep(.ant-table-cell) {
  padding: 8px !important;
}

/* 确保输入框占满剩余空间 */
:deep(.ant-input-wrapper) {
  width: 100%;
}
</style>
