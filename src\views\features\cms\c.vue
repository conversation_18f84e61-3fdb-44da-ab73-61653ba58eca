<template>
  <div class="table-view-container">
    <!-- 查询区域 -->
    <div class="query-section">
      <el-form :inline="true" class="query-form">
        <div class="query-title">生产报告查询:</div>

        <el-form-item>
          <el-select
            v-model="queryParams.department"
            placeholder="选择部门"
            class="query-select"
          >
            <el-option value="维修二处" label="维修二处"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-select
            v-model="queryParams.section"
            placeholder="选择科室"
            class="query-select"
          >
            <el-option value="机械科" label="机械科"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-date-picker
            v-model="queryParams.date"
            type="date"
            placeholder="选择日期"
            format="yyyy年MM月dd日"
            value-format="yyyy-MM-dd"
            class="query-date"
            :picker-options="pickerOptions"
          ></el-date-picker>
        </el-form-item>

        <div class="query-buttons">
          <el-button type="primary">配置字段显示</el-button>
          <el-button type="primary">设置邮件</el-button>
          <el-button type="primary">编辑附件</el-button>
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button type="primary" @click="handleExport">导出</el-button>
        </div>
      </el-form>
    </div>

    <!-- 日常维修日报公告区域 -->
    <div class="announcement-section">
      <div class="section-title announcement-title">
        维修二处机械科日常维修日报
      </div>
      <div class="announcement-content">
        <div class="announcement-header">
          <p>各位领导，同事：</p>
          <p>
            请阅读机械专业生产计划通报，3/4/8 号机组日常计划CM+DM 项目 74 个，OM
            项目 436 个。
          </p>
        </div>
        <div class="announcement-body">
          <p class="announcement-subtitle">1. 管理要求责任:</p>
          <p class="indent">
            (1) "两个零容忍"：对违法作假零容忍;
            对违规操作零容忍，"不安全不生产，没把握停下来"。
          </p>
          <p class="indent">
            (2) 防人因 "35 铁律"：3 必须:
            必须严格遵守程序、必须填九位码自检、必须及时汇报异常；5 不动:
            工作对象不核实不动、操作风险不清楚不动、安全措施未确认不动、执行标准不明确不动、安装方向不确定不动。
          </p>
        </div>
      </div>
    </div>

    <!-- 机组数据对比表格 -->
    <unit-comparison-table :query-params="queryParams"></unit-comparison-table>

    <!-- 机械现存未完工工作表格 -->
    <unfinished-work-table :query-params="queryParams"></unfinished-work-table>

    <!-- 机械明日自主实施工作表格 -->
    <tomorrow-work-table :query-params="queryParams"></tomorrow-work-table>

    <!-- 机械明日计划开工工作表格 -->
    <planned-work-table :query-params="queryParams"></planned-work-table>

    <!-- 机械明日配合工作表格 -->
    <coordination-work-table
      :query-params="queryParams"
    ></coordination-work-table>

    <!-- 明日机械配合运行试验工作表格 -->
    <operation-test-work-table
      :query-params="queryParams"
    ></operation-test-work-table>

    <!-- 机械现存WR工作请求表格 -->
    <work-request-table :query-params="queryParams"></work-request-table>

    <!-- T-8周未完成准备表格 -->
    <t8-week-preparation-table
      :query-params="queryParams"
    ></t8-week-preparation-table>

    <!-- T-23周未批准表格 -->
    <t23-week-pending-table
      :query-params="queryParams"
    ></t23-week-pending-table>

    <!-- 无制约未完成准备工单表格 -->
    <unconstrained-work-order-table
      :query-params="queryParams"
    ></unconstrained-work-order-table>

    <!-- 完工5天未关闭表格 -->
    <closing-delayed-work-table
      :query-params="queryParams"
    ></closing-delayed-work-table>

    <!-- QSR完工未关闭表格 -->
    <qsr-closing-work-table
      :query-params="queryParams"
    ></qsr-closing-work-table>
  </div>
</template>

<script>
import UnitComparisonTable from "./components/UnitComparisonTable.vue";
import UnfinishedWorkTable from "./components/UnfinishedWorkTable.vue";
import TomorrowWorkTable from "./components/TomorrowWorkTable.vue";
import PlannedWorkTable from "./components/PlannedWorkTable.vue";
import CoordinationWorkTable from "./components/CoordinationWorkTable.vue";
import OperationTestWorkTable from "./components/OperationTestWorkTable.vue";
import WorkRequestTable from "./components/WorkRequestTable.vue";
import T8WeekPreparationTable from "./components/T8WeekPreparationTable.vue";
import T23WeekPendingTable from "./components/T23WeekPendingTable.vue";
import UnconstrainedWorkOrderTable from "./components/UnconstrainedWorkOrderTable.vue";
import ClosingDelayedWorkTable from "./components/ClosingDelayedWorkTable.vue";
import QsrClosingWorkTable from "./components/QsrClosingWorkTable.vue";

export default {
  components: {
    UnitComparisonTable,
    UnfinishedWorkTable,
    TomorrowWorkTable,
    PlannedWorkTable,
    CoordinationWorkTable,
    OperationTestWorkTable,
    WorkRequestTable,
    T8WeekPreparationTable,
    T23WeekPendingTable,
    UnconstrainedWorkOrderTable,
    ClosingDelayedWorkTable,
    QsrClosingWorkTable,
  },
  data() {
    return {
      queryParams: {
        department: "维修二处",
        section: "机械科",
        date: new Date(), // 使用日期对象直接赋值当前日期
      },
      // 日期选择器中文配置
      pickerOptions: {
        firstDayOfWeek: 1, // 周一为一周的第一天
        shortcuts: [
          {
            text: "今天",
            onClick(picker) {
              picker.$emit("pick", new Date());
            },
          },
          {
            text: "昨天",
            onClick(picker) {
              const date = new Date();
              date.setTime(date.getTime() - 3600 * 1000 * 24);
              picker.$emit("pick", date);
            },
          },
          {
            text: "一周前",
            onClick(picker) {
              const date = new Date();
              date.setTime(date.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", date);
            },
          },
        ],
      },
    };
  },
  methods: {
    handleQuery() {
      // 查询处理逻辑
      console.log("主组件查询参数:", this.queryParams);

      this.queryParams = {
        ...this.queryParams,
        _timestamp: new Date().getTime(), // 添加时间戳确保每次查询都会更新引用
      };
    },
    handleExport() {
      // 导出处理逻辑
      console.log("导出数据");
    },
  },
};
</script>

<style scoped>
.table-view-container {
  padding: 20px;
  background-color: #fff;
  height: 800px;
  overflow: auto; /* 防止容器自身显示滚动条 */
}

/* 查询区域样式 */
.query-section {
  padding: 20px 25px;
  margin-bottom: 30px;
  background-color: #f5f7fa;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.query-form {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 15px;
}

.query-title {
  font-weight: bold;
  color: #303133;
  margin-right: 20px;
  font-size: 16px;
}

.query-select {
  width: 150px;
}

.query-date {
  width: 220px;
}

.query-buttons {
  margin-left: auto;
  display: flex;
  gap: 10px;
}

/* 按钮样式 */
.query-buttons .el-button {
  padding: 10px 20px;
  font-size: 14px;
}

.el-button--primary {
  background-color: #1e5da0;
  border-color: #1e5da0;
}

.el-button--primary:hover,
.el-button--primary:focus {
  background-color: #3d7ab8;
  border-color: #3d7ab8;
}

/* 公告区域样式 */
.announcement-section {
  margin-bottom: 30px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  padding: 0;
  width: 100%;
  overflow: hidden;
}

.announcement-content {
  padding: 15px 20px 20px;
}

.announcement-header {
  margin-bottom: 15px;
}

.announcement-header p {
  margin: 5px 0;
  color: #333;
  line-height: 1.5;
}

.announcement-body {
  color: #333;
}

.announcement-subtitle {
  font-weight: 500;
  margin: 10px 0;
}

.indent {
  padding-left: 20px;
  margin: 8px 0;
  line-height: 1.5;
}

/* 公告标题居中加粗 */
.announcement-title {
  text-align: center;
  display: block;
  font-weight: bold;
  padding: 15px 0;
  border-left: none;
  color: #333;
}

/* Element UI 样式覆盖 */
.el-form-item {
  margin-bottom: 0;
}

.el-form-item__content {
  line-height: 40px;
}

.el-input__inner,
.el-select .el-input__inner,
.el-date-editor.el-input {
  height: 40px;
  line-height: 40px;
}

.el-date-editor .el-range-input {
  font-size: 14px;
}
</style>
