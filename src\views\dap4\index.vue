<template>
  <div class="parent">
    <div class="div1">
      <div class="header">人员配置</div>
      <div class="content">
        <LeftBlock class="block" />
        <RightBlock class="block" />
      </div>
    </div>
    <div class="div2">
      <div class="header">物资配置3</div>
      <div class="content">
        <MaterialBlock1 class="block" />
        <MaterialBlock2 class="block" />
        <MaterialBlock3 class="block" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import LeftBlock from "./cms/LeftBlock.vue";
import RightBlock from "./cms/RightBlock.vue";
import MaterialBlock1 from "./cms/MaterialBlock1.vue";
import MaterialBlock2 from "./cms/MaterialBlock2.vue";
import MaterialBlock3 from "./cms/MaterialBlock3.vue";
</script>

<style scoped>
.parent {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  grid-template-rows: repeat(6, 1fr);
  grid-column-gap: 0px;
  grid-row-gap: 10px;
  height: 100%;
  padding: 10px;
  box-sizing: border-box;
  background-color: #10152b;
}

.div1 {
  grid-area: 1 / 1 / 4 / 6;
  background-color: #192347;
  display: flex;
  flex-direction: column;
  color: white;
}

.div2 {
  grid-area: 4 / 1 / 7 / 6;
  background-color: #192347;
  display: flex;
  flex-direction: column;
  color: white;
}

.header {
  height: 30px;
  background-color: #37b1fe;
  border-radius: 8px 8px 0 0;
  display: flex;
  align-items: center;
  padding-left: 15px;
  font-weight: bold;
}

.content {
  flex: 1;
  display: flex;
  flex-direction: row;
  padding: 10px;
  padding-top: 0px;
  gap: 10px;
}

.block {
  flex: 1;
}
</style>
