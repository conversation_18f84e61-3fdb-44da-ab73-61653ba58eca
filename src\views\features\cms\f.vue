<template>
  <div class="risk-container">
    <div class="operation-section">
      <a-space>
        <a-button type="primary" @click="showAddModal">
          <plus-outlined />
          新增
        </a-button>
        <a-button @click="handleImport">
          <upload-outlined />
          导入
        </a-button>
        <a-button type="primary" danger @click="handleDelete">
          <delete-outlined />
          删除
        </a-button>
        <a-button type="primary" @click="handleSave">
          <save-outlined />
          保存
        </a-button>
        <a-button type="primary" @click="handleEdit">
          <edit-outlined />
          编辑
        </a-button>
      </a-space>
    </div>

    <a-table
      :columns="columns"
      :data-source="tableData"
      :row-selection="rowSelection"
      :row-key="(record) => record.id"
      :pagination="false"
      :expandable="false"
      class="custom-table"
    >
      <template #bodyCell="{ column, record, index }">
        <!-- 经验反馈主题列 -->
        <template v-if="column.dataIndex === 'subject'">
          <span>{{ record.subject }}</span>
        </template>

        <!-- 概述列 -->
        <template v-if="column.dataIndex === 'summary'">
          <a-popover
            placement="topLeft"
            trigger="hover"
            :content="record.summary"
            :mouseEnterDelay="0.5"
          >
            <span class="summary-text">{{ record.summary }}</span>
          </a-popover>
        </template>

        <!-- 管控措施列 -->
        <template v-if="column.dataIndex === 'controlMeasures'">
          <div class="measure-list">
            <div
              v-for="(measure, index) in record.controlMeasures"
              :key="index"
              class="measure-item"
            >
              <span class="measure-text">{{ measure }}</span>
            </div>
          </div>
        </template>

        <!-- 措施落实情况列 -->
        <template v-if="column.dataIndex === 'implementationStatus'">
          <div class="measure-list">
            <div
              v-for="(status, index) in record.implementationStatus"
              :key="index"
              class="measure-item"
            >
              <span class="measure-text">{{ status }}</span>
            </div>
          </div>
        </template>
      </template>
    </a-table>

  
  </div>
</template>

<script setup>
import { ref } from "vue";
import {
  PlusOutlined,
  DeleteOutlined,
  SaveOutlined,
  EditOutlined,
  UploadOutlined,
  PlusCircleOutlined,
  MinusCircleOutlined,
} from "@ant-design/icons-vue";

import { message } from "ant-design-vue";

const mockData = [
  {
    id: "1",
    subject: "安全风险",
    summary: "高空作业安全管控",
    createTime: "2024-01-01",
    specialId: "SP001",
    updateTime: "2024-01-02",
    updateUser: "admin",
    childList: [
      {
        id: "1-1",
        subject: "严格执行安全操作规程",
        summary: "已纳入日常检查",
      },
      {
        id: "1-2",
        subject: "配备完整的安全防护设备",
        summary: "设备已配备到位",
      },
    ],
  },
  {
    id: "2",
    subject: "安全风险",
    summary: "高空作业安全管控",
    createTime: "2024-01-01",
    specialId: "SP001",
    updateTime: "2024-01-02",
    updateUser: "admin",
    childList: [
      {
        id: "2-1",
        subject: "加强过程质量控制",
        summary: "已建立质量检查制度",
      },
      {
        id: "2-2",
        subject: "完善质量检验标准",
        summary: "标准已更新并执行",
      },
    ],
  },
];

// 移除原有的 tableData 定义，直接使用这个
const tableData = ref(
  mockData.map((item) => ({
    id: item.id,
    subject: item.subject,
    summary: item.summary,
    controlMeasures: item.childList.map((child) => child.subject),
    implementationStatus: item.childList.map((child) => child.summary),
    measureChecked: Array(item.childList.length).fill(false),
    ...item,
  }))
);

const selectedRowKeys = ref([]);

// 选择变化处理方法
const onSelectChange = (keys) => {
  selectedRowKeys.value = keys;
};

// 表格选择配置
const rowSelection = {
  type: "checkbox",
  selectedRowKeys,
  onChange: onSelectChange,
  columnWidth: 60,
};

const columns = [
  {
    title: "序号",
    dataIndex: "index",
    width: 80,
    align: "center",
    customRender: ({ index }) => index + 1,
  },
  {
    title: "经验反馈主题",
    dataIndex: "subject",
    width: 200,
  },
  {
    title: "概述",
    dataIndex: "summary",
    width: 200,
  },
  {
    title: "管控措施",
    dataIndex: "controlMeasures",
    width: 300,
  },
  {
    title: "措施落实情况",
    dataIndex: "implementationStatus",
    width: 300,
  },
];

// 修改删除方法，使用id删除
const handleDelete = () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning("请先选择要删除的数据");
    return;
  }

  // 使用id删除数据
  tableData.value = tableData.value.filter(
    (item) => !selectedRowKeys.value.includes(item.id)
  );
  selectedRowKeys.value = [];
  message.success("删除成功");
};

// 修改保存方法，保留id信息
const handleSave = () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning("请先选择要保存的数据");
    return;
  }

  // 获取选中的数据，包含id信息
  const selectedData = tableData.value
    .filter((item) => selectedRowKeys.value.includes(item.id))
    .map((item) => ({
      id: item.id,
      subject: item.subject,
      summary: item.summary,
      children: item.controlMeasures.map((measure, index) => ({
        id: item.childrenIds[index],
        subject: measure,
        summary: item.implementationStatus[index],
      })),
    }));

  console.log("保存的数据：", selectedData);
  message.success("保存成功");
};

// 新增弹框ref
const addModalRef = ref(null);
// 方法定义
const showAddModal = () => {
  addModalRef.value.show();
  // 清除选中状态
  selectedRowKeys.value = [];
};
const handleImport = () => {
  console.log("导入");
};
// 编辑
const handleEdit = () => {
  if (selectedRowKeys.value.length !== 1) {
    message.warning("请选择一条数据进行编辑");
    return;
  }

  // 获取选中的数据
  const selectedData = tableData.value.find(
    (item) => item.id === selectedRowKeys.value[0]
  );
  if (selectedData) {
    // 直接传递当前行的完整数据
    addModalRef.value.showEdit(selectedData);
  }
};

// 处理编辑保存
const handleEditSave = (data) => {
  // 获取原始数据中的其他字段
  const originalData = tableData.value.find((item) => item.id === data.id);

  // 构造编辑后的数据结构
  const editedData = {
    id: data.id,
    subject: data.subject,
    summary: data.summary,
    createTime: originalData?.createTime,
    specialId: originalData?.specialId,
    updateTime: originalData?.updateTime,
    updateUser: originalData?.updateUser,
    childList: data.controlMeasures.map((measure, index) => ({
      subject: measure,
      summary: data.implementationStatus[index],
    })),
  };

  console.log("编辑后的数据：", editedData);
};

// 处理新增数据
const handleAddData = (data) => {
  // 构造新增数据结构
  const newData = {
    subject: data.subject,
    summary: data.summary,
    childList: data.controlMeasures.map((measure, index) => ({
      subject: measure,
      summary: data.implementationStatus[index],
    })),
  };

  console.log("新增的数据：", newData);
};

// 监听子组件的关闭事件
const handleModalClose = () => {
  // 清除选中状态
  selectedRowKeys.value = [];
};
</script>

<style scoped lang="scss">
.risk-container {
  padding: 16px;
}

.operation-section {
  margin-bottom: 16px;
  display: flex;
  align-items: center;
}

/* 自定义表格样式 */
:deep(.custom-table) {
  /* 表头样式 */
  .ant-table-thead > tr > th {
    background-color: #5b9bd5 !important;
    color: white !important;
    text-align: center !important;
    height: 50px !important;
    padding: 0 !important;
    line-height: 50px !important;
    border-radius: 0 !important; /* 移除圆角 */
  }

  /* 移除表头上方的线 */
  .ant-table-thead > tr > th::before {
    display: none !important;
  }

  /* 表格内容背景色 */
  .ant-table-tbody > tr > td {
    background-color: #bdd7ee !important;
    padding: 0 !important;
  }

  /* 表格边框颜色 */
  .ant-table-cell {
    border-right: 1px solid white !important;
    border-bottom: 1px solid white !important;
  }

  /* 去除表格hover效果 */
  .ant-table-tbody > tr:hover > td {
    background: #bdd7ee !important;
  }

  /* 移除序号列的交互样式 */
  .ant-table-tbody > tr > td:first-child {
    cursor: default !important;
  }
}

.icon-button {
  margin-left: 8px;
  font-size: 16px;
  cursor: pointer;
}

.icon-button:hover {
  color: #1890ff;
}

.measure-item {
  display: flex;
  align-items: center;
  border-bottom: 1px solid white;
  min-height: 32px;
}

/* 移除第一个和最后一个 measure-item 的边框 */
.measure-item:first-child {
  border-top: none;
}

.measure-item:last-child {
  border-bottom: none;
}

.checkbox-container {
  width: 40px;
  min-width: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-right: 1px solid white;
  height: 32px;
}

.measure-input {
  flex: 1;
}

/* 普通单元格的输入框样式 - 居中对齐 */
:deep(.ant-table-cell) .ant-input {
  text-align: center;
  background-color: transparent !important;
  height: 32px;
}

/* 管控措施和措施落实情况的输入框样式 - 左对齐 */
:deep(.ant-table-cell) .measure-item .ant-input {
  text-align: left;
  padding-left: 8px; /* 添加左侧内边距，避免文字贴边 */
}

/* 移除之前的全局输入框样式 */
:deep(.ant-input) {
  background-color: transparent !important;
  height: 32px;
}

:deep(.ant-table-cell) {
  padding: 0 !important;
}

:deep(.ant-checkbox-wrapper) {
  margin: 0;
}

/* 添加概述文本样式 */
.summary-text {
  display: block;
  padding: 4px 11px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 修改 popover 样式 */
:deep(.ant-popover-inner-content) {
  max-width: 300px;
  word-wrap: break-word;
}

/* 设置复选框为圆形 */
:deep(.ant-checkbox) {
  border-radius: 50% !important;

  /* 未选中状态 */
  .ant-checkbox-inner {
    border-radius: 50% !important;
    width: 16px !important;
    height: 16px !important;
  }

  /* 选中状态 */
  &.ant-checkbox-checked .ant-checkbox-inner {
    background-color: #1890ff !important;
    border-color: #1890ff !important;
    border-radius: 50% !important;

    /* 调整对勾的位置和大小 */
    &::after {
      top: 45% !important;
      left: 22% !important;
      width: 5px !important;
      height: 9px !important;
    }
  }
}

.measure-text {
  display: block;
  padding: 4px 11px;
  line-height: 1.5;
  color: rgba(0, 0, 0, 0.88);
}

.measure-list {
  .measure-item {
    padding: 4px 8px;

    .measure-text {
      display: block;
      line-height: 1.5;
      color: rgba(0, 0, 0, 0.88);
    }
  }
}

:deep(.ant-table-selection-column) {
  background-color: #f5f5f5;
  border-right: 1px solid #f0f0f0;
}
</style>
