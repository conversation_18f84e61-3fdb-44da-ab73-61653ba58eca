<template>
  <!-- 机组数据对比表格 -->
  <div class="unit-comparison-section">
    <div class="section-title">
      <span class="title-icon"></span>
      机组数据对比
    </div>
    <div class="table-container">
      <table class="machine-table">
        <!-- 表头 -->
        <tr>
          <th
            v-for="(unit, index) in units"
            :key="`header-${index}`"
            colspan="8"
            class="unit-header"
          >
            {{ unit.name }}
          </th>
        </tr>
        <!-- 二级表头 - 项目行 -->
        <tr>
          <template
            v-for="(unit, unitIndex) in units"
            :key="`heading-${unitIndex}`"
          >
            <td rowspan="5" class="category">CM</td>
            <th class="item-header">项目</th>
            <th
              v-for="(col, colIndex) in unit.columns"
              :key="`col-${unitIndex}-${colIndex}`"
            >
              {{ col }}
            </th>
          </template>
        </tr>
        <!-- CM -->
        <tr>
          <template
            v-for="(unit, unitIndex) in units"
            :key="`cm-s-${unitIndex}`"
          >
            <td class="subcategory">S</td>
            <td
              v-for="(val, colIndex) in unit.data.CM.S"
              :key="`s-${unitIndex}-${colIndex}`"
            >
              {{ val }}
            </td>
          </template>
        </tr>
        <tr>
          <template
            v-for="(unit, unitIndex) in units"
            :key="`cm-a-${unitIndex}`"
          >
            <td class="subcategory">A</td>
            <td
              v-for="(val, colIndex) in unit.data.CM.A"
              :key="`a-${unitIndex}-${colIndex}`"
            >
              {{ val }}
            </td>
          </template>
        </tr>
        <tr>
          <template
            v-for="(unit, unitIndex) in units"
            :key="`cm-b-${unitIndex}`"
          >
            <td class="subcategory">B</td>
            <td
              v-for="(val, colIndex) in unit.data.CM.B"
              :key="`b-${unitIndex}-${colIndex}`"
            >
              {{ val }}
            </td>
          </template>
        </tr>
        <tr>
          <template
            v-for="(unit, unitIndex) in units"
            :key="`cm-c-${unitIndex}`"
          >
            <td class="subcategory">C</td>
            <td
              v-for="(val, colIndex) in unit.data.CM.C"
              :key="`c-${unitIndex}-${colIndex}`"
            >
              {{ val }}
            </td>
          </template>
        </tr>
        <!-- DM -->
        <tr>
          <template
            v-for="(unit, unitIndex) in units"
            :key="`dm-s-${unitIndex}`"
          >
            <td rowspan="4" class="category">DM</td>
            <td class="subcategory">S</td>
            <td
              v-for="(val, colIndex) in unit.data.DM.S"
              :key="`dm-s-${unitIndex}-${colIndex}`"
            >
              {{ val }}
            </td>
          </template>
        </tr>
        <tr>
          <template
            v-for="(unit, unitIndex) in units"
            :key="`dm-a-${unitIndex}`"
          >
            <td class="subcategory">A</td>
            <td
              v-for="(val, colIndex) in unit.data.DM.A"
              :key="`dm-a-${unitIndex}-${colIndex}`"
            >
              {{ val }}
            </td>
          </template>
        </tr>
        <tr>
          <template
            v-for="(unit, unitIndex) in units"
            :key="`dm-b-${unitIndex}`"
          >
            <td class="subcategory">B</td>
            <td
              v-for="(val, colIndex) in unit.data.DM.B"
              :key="`dm-b-${unitIndex}-${colIndex}`"
            >
              {{ val }}
            </td>
          </template>
        </tr>
        <tr>
          <template
            v-for="(unit, unitIndex) in units"
            :key="`dm-c-${unitIndex}`"
          >
            <td class="subcategory">C</td>
            <td
              v-for="(val, colIndex) in unit.data.DM.C"
              :key="`dm-c-${unitIndex}-${colIndex}`"
            >
              {{ val }}
            </td>
          </template>
        </tr>
        <!-- OM行 -->
        <tr>
          <template v-for="(unit, unitIndex) in units" :key="`om-${unitIndex}`">
            <td colspan="2" class="category">OM</td>
            <td
              v-for="(val, colIndex) in unit.data.OM"
              :key="`om-${unitIndex}-${colIndex}`"
            >
              {{ val }}
            </td>
          </template>
        </tr>
        <!-- 工作中行 -->
        <tr>
          <template
            v-for="(unit, unitIndex) in units"
            :key="`work-${unitIndex}`"
          >
            <td colspan="2" class="work-in">工作中</td>
            <td
              v-for="(val, colIndex) in unit.data.WORK_IN"
              :key="`work-${unitIndex}-${colIndex}`"
            >
              {{ val }}
            </td>
          </template>
        </tr>
        <!-- 大/小修行 -->
        <tr>
          <template
            v-for="(unit, unitIndex) in units"
            :key="`repair-${unitIndex}`"
          >
            <td colspan="2" class="repair">大/小修</td>
            <td
              v-for="(val, colIndex) in unit.data.REPAIR"
              :key="`repair-${unitIndex}-${colIndex}`"
            >
              {{ val }}
            </td>
          </template>
        </tr>
        <!-- 等备件行 -->
        <tr>
          <template
            v-for="(unit, unitIndex) in units"
            :key="`parts-${unitIndex}`"
          >
            <td colspan="2" class="part">等备件</td>
            <td
              v-for="(val, colIndex) in unit.data.PARTS"
              :key="`parts-${unitIndex}-${colIndex}`"
            >
              {{ val }}
            </td>
          </template>
        </tr>
        <!-- 等窗口行 -->
        <tr>
          <template
            v-for="(unit, unitIndex) in units"
            :key="`window-${unitIndex}`"
          >
            <td colspan="2" class="window">等窗口</td>
            <td
              v-for="(val, colIndex) in unit.data.WINDOW"
              :key="`window-${unitIndex}-${colIndex}`"
            >
              {{ val }}
            </td>
          </template>
        </tr>
        <!-- 等材料行 -->
        <tr>
          <template
            v-for="(unit, unitIndex) in units"
            :key="`material-${unitIndex}`"
          >
            <td colspan="2" class="material">等材料</td>
            <td
              v-for="(val, colIndex) in unit.data.MATERIAL"
              :key="`material-${unitIndex}-${colIndex}`"
            >
              {{ val }}
            </td>
          </template>
        </tr>
        <!-- 共计行 -->
        <tr>
          <template
            v-for="(unit, unitIndex) in units"
            :key="`total-${unitIndex}`"
          >
            <td colspan="2" class="total">共计</td>
            <td
              v-for="(val, colIndex) in unit.data.TOTAL"
              :key="`total-${unitIndex}-${colIndex}`"
            >
              {{ val }}
            </td>
          </template>
        </tr>
      </table>
    </div>
  </div>
</template>

<script>
export default {
  name: "UnitComparisonTable",
  props: {
    queryParams: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      units: [
        {
          name: "3/8 号机组",
          columns: ["M211", "M212", "M213", "M214", "M215", "M216"],
          data: {
            CM: {
              S: [0, 0, 0, 0, 0, 0],
              A: [0, 0, 0, 0, 0, 0],
              B: [0, 0, 1, 0, 0, 0],
              C: [0, 0, 0, 0, 0, 0],
            },
            DM: {
              S: [0, 0, 0, 0, 0, 0],
              A: [0, 0, 0, 0, 0, 0],
              B: [4, 11, 7, 3, 2, 1],
              C: [7, 7, 4, 3, 0, 1],
            },
            OM: [28, 76, 43, 59, 26, 35],
            WORK_IN: [1, 2, 2, 1, 0, 1],
            REPAIR: [2, 22, 18, 29, 10, 5],
            PARTS: [7, 34, 9, 19, 9, 7],
            WINDOW: [12, 6, 6, 3, 0, 1],
            MATERIAL: [0, 0, 1, 3, 1, 1],
            TOTAL: [39, 94, 54, 66, 28, 37],
          },
        },
        {
          name: "4 号机组",
          columns: ["M211", "M212", "M213", "M214", "M215", "M216"],
          data: {
            CM: {
              S: [0, 0, 0, 0, 0, 0],
              A: [0, 0, 0, 0, 0, 0],
              B: [0, 0, 0, 0, 0, 0],
              C: [0, 0, 0, 0, 0, 0],
            },
            DM: {
              S: [0, 0, 0, 0, 0, 0],
              A: [0, 0, 0, 0, 0, 0],
              B: [1, 7, 0, 2, 1, 0],
              C: [0, 6, 2, 3, 0, 1],
            },
            OM: [9, 59, 52, 41, 27, 29],
            WORK_IN: [1, 1, 1, 0, 0, 1],
            REPAIR: [2, 19, 25, 20, 10, 5],
            PARTS: [2, 28, 6, 11, 12, 1],
            WINDOW: [1, 5, 3, 1, 0, 2],
            MATERIAL: [0, 0, 3, 3, 2, 0],
            TOTAL: [10, 72, 54, 46, 28, 30],
          },
        },
        {
          name: "5号机组",
          columns: ["M211", "M212", "M213", "M214", "M215", "M216"],
          data: {
            CM: {
              S: [0, 0, 0, 0, 0, 0],
              A: [0, 0, 0, 0, 0, 0],
              B: [0, 0, 0, 0, 0, 0],
              C: [0, 0, 0, 0, 0, 0],
            },
            DM: {
              S: [0, 0, 0, 0, 0, 0],
              A: [0, 0, 0, 0, 0, 0],
              B: [1, 7, 0, 2, 1, 0],
              C: [0, 6, 2, 3, 0, 1],
            },
            OM: [9, 59, 52, 41, 27, 29],
            WORK_IN: [1, 1, 1, 0, 0, 1],
            REPAIR: [2, 19, 25, 20, 10, 5],
            PARTS: [2, 28, 6, 11, 12, 1],
            WINDOW: [1, 5, 3, 1, 0, 2],
            MATERIAL: [0, 0, 3, 3, 2, 0],
            TOTAL: [10, 72, 54, 46, 28, 30],
          },
        },
      ],
    };
  },
  mounted() {},
  watch: {
    queryParams: {
      handler(newVal) {
        this.updateDataByQueryParams(newVal);
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    // 根据查询参数更新数据
    updateDataByQueryParams(params) {
      console.log(`${this.$options.name}组件处理查询参数:`, params);
    },
  },
};
</script>

<style scoped>
/* 机组对比表格样式 */
.unit-comparison-section {
  margin-bottom: 30px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  padding: 0;
  width: 100%;
  overflow: hidden; /* 防止部分显示滚动条 */
}

.section-title {
  padding: 15px 20px;
  font-size: 15px;
  color: #666;
  font-weight: 500;
  display: flex;
  align-items: center;
  background: linear-gradient(to right, #f8f9fc, #ffffff);
  border-left: 3px solid #1890ff;
  letter-spacing: 0.5px;
  margin-bottom: 15px;
  box-shadow: 0 1px 0 rgba(0, 0, 0, 0.05);
}

.title-icon {
  display: inline-block;
  width: 4px;
  height: 16px;
  margin-right: 8px;
  background-color: #1890ff;
  border-radius: 2px;
}

.table-container {
  overflow-x: scroll; /* 强制显示横向滚动条 */
  width: 100%;
  max-width: 100%;
  padding-bottom: 15px; /* 为滚动条预留空间 */
  /* 确保滚动条始终可见 */
  scrollbar-width: thin;
  scrollbar-color: #ccc transparent;
}

/* 自定义滚动条样式 */
.table-container::-webkit-scrollbar {
  height: 8px;
  width: 8px;
}

.table-container::-webkit-scrollbar-thumb {
  background-color: #ccc;
  border-radius: 4px;
}

.table-container::-webkit-scrollbar-track {
  background-color: transparent;
}

.machine-table {
  table-layout: fixed; /* 固定布局，保持列宽稳定 */
  border-collapse: collapse;
  border: 1px solid #ccc;
  width: auto; /* 重要：使用auto而不是max-content */
  display: table; /* 确保表格作为表格渲染 */
}

.machine-table th,
.machine-table td {
  border: 1px solid #ccc;
  padding: 6px;
  text-align: center;
  font-size: 14px;
  height: 30px;
  white-space: nowrap; /* 防止文本换行 */
}

/* 为每个单元格类型设置固定宽度 */
.machine-table th {
  width: 80px; /* 固定列宽 */
}

.machine-table .unit-header {
  background-color: #ffff00;
  font-weight: bold;
  color: #000;
  height: 36px;
  width: 150px; /* 固定机组标题宽度 */
}

.machine-table .item-header {
  background-color: #f0f0f0;
  font-weight: bold;
  vertical-align: middle;
  width: 60px;
  text-align: center;
}

.machine-table .category {
  /* 类别单元格样式 */
  font-weight: bold;
  width: 40px;
  text-align: center;
}

.machine-table .subcategory {
  /* 子类别单元格样式 */
  font-weight: bold;
  width: 40px;
  text-align: center;
}

.machine-table .work-in,
.machine-table .repair,
.machine-table .part,
.machine-table .window,
.machine-table .material,
.machine-table .total {
  /* 各种状态行样式 */
  font-weight: bold;
  text-align: center;
  line-height: 1.2;
  width: 60px;
}

.machine-table tr:last-child td {
  font-weight: bold;
}

/* 确保每列都有固定宽度 */
.machine-table tr td:nth-child(n) {
  width: 80px;
}
</style>
