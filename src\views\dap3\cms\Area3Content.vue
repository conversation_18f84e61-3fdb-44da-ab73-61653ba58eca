<template>
  <div class="area3-container">
    <div class="area3-top">
      <div class="milestone-title">大修准备里程碑</div>
      <div class="kpi-boxes">
        <div class="kpi-box">
          <div class="kpi-number">33</div>
          <div class="kpi-label">总计完成</div>
        </div>
        <div class="kpi-box">
          <div class="kpi-number">11</div>
          <div class="kpi-label">本月计划</div>
        </div>
        <div class="kpi-box">
          <div class="kpi-number">10</div>
          <div class="kpi-label">已完成</div>
        </div>
        <div class="kpi-box">
          <div class="kpi-number">11</div>
          <div class="kpi-label">下月计划</div>
        </div>
        <div class="kpi-box">
          <div class="kpi-number">2</div>
          <div class="kpi-label">累计偏差</div>
        </div>
      </div>
      <div class="milestone-info">
        <div class="milestone-title-wrapper">
          <span class="milestone-text">下月开始里程碑：</span>
          <div class="milestone-row">
            <span class="milestone-item">P12浓缩大修项目大修;</span>
            <span class="milestone-item">P3完成大修外委项目;</span>
            <span class="milestone-item">P4大修组织机构发布P7提出大修工器具准备需求P7提出大修工器具准备需求P7提出大修工器具准备需求P7提出大修工器具准备需求P7提出大修工器具准备需求P7提出大修工器具准备需求</span>
          </div>
        </div>
        <div class="milestone-title-wrapper">
          <span class="milestone-text">下月完成里程碑：</span>
          <div class="milestone-row">
            <span class="milestone-item">P5确定大修关键路径 (1版);</span>
            <span class="milestone-item">P6确定大修综合性外委项目;</span>
            <span class="milestone-item">P7提出大修工器具准备需求P7提出大修工器具准备需求P7提出大修工器具准备需求P7提出大修工器具准备需求P7提出大修工器具准备需求P7提出大修工器具准备需求P7提出大修工器具准备需求P7提出大修工器具准备需求P7提出大修工器具准备需求P7提出大修工器具准备需求P7提出大修工器具准备需求P7提出大修工器具准备需求</span>
          </div>
        </div>
      </div>
    </div>
    <div class="area3-bottom">
      <Bottom />
    </div>
  </div>
</template>

<script setup>
import Bottom from './Bottom.vue';
// 区域3的逻辑
</script>

<style scoped>
.area3-container {
  background-color: #192347;
  color: #fff;
  height: 100%;
  width: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  border-radius: 8px;
  overflow: hidden;
}

.area3-top {
  height: 35%;
  background-color: #192347;
  display: flex;
  flex-direction: column;
  border-bottom: 1px solid #2a3b67;
  padding: 5px;
}

.milestone-title {
  text-align: center;
  font-size: 15px;
  font-weight: bold;
  color: #03d2f6;
  margin-bottom: 5px;
}

.kpi-boxes {
  display: flex;
  justify-content: space-between;
}

.kpi-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 1px solid #61aef9;
  border-radius: 6px;
  padding: 0px 30px;
  min-width: 60px;
  box-shadow: 0 0 8px rgba(97, 174, 249, 0.4);
}

.kpi-number {
  font-size: 16px;
  font-weight: bold;
  color: #61aef9;
}

.kpi-label {
  font-size: 11px;
  color: #ffffff;
  margin-top: 3px;
}

.milestone-info {
  margin-top: 10px;
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.milestone-title-wrapper {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 5px;
}

.milestone-row {
  background-color: #091129;
  border: 1px solid #009cff;
  padding: 2px 0;
  border-radius: 3px;
  font-size: 11px;
  color: #61aef9;
  flex: 1;
  text-indent:5px;
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.area3-bottom {
  background-color: #192347;
  overflow: hidden;
}

.milestone-text {
  color: #fff;
  font-weight: 400;
  font-size: 10px;
}

.milestone-item {
  color:#05b2d2
  /* display: inline-flex;
  align-items: center;
  margin-right: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%; */
}

.milestone-dot {
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #61aef9;
  margin-right: 4px;
  margin-left: 5px;
}

.milestone-row .milestone-text {
  color: #fff;
  font-weight: bold;
}
</style> 