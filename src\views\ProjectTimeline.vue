<template>
  <div class="project-timeline-page">
    <div class="header">
      <div class="title-section">
        <span class="title-icon"></span>
        <span class="title-text">项目时间线</span>
      </div>
      <div class="actions">
        <button class="action-button">导出</button>
        <button class="action-button">打印</button>
        <button class="action-button">设置</button>
      </div>
    </div>
    
    <div class="timeline-wrapper">
      <ProjectGanttChart />
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import ProjectGanttChart from './features/gantt-chart/ProjectGanttChart.vue';
</script>

<style scoped>
.project-timeline-page {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #0a1629;
  color: white;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  height: 50px;
  background-color: #0d1a2f;
  border-bottom: 1px solid #1c3865;
}

.title-section {
  display: flex;
  align-items: center;
}

.title-icon {
  width: 4px;
  height: 16px;
  background-color: #00ccff;
  border-radius: 2px;
  margin-right: 8px;
}

.title-text {
  font-size: 16px;
  font-weight: 600;
  color: #ffffff;
}

.actions {
  display: flex;
  gap: 10px;
}

.action-button {
  background-color: #1a3563;
  color: #00ccff;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  cursor: pointer;
  font-size: 14px;
}

.action-button:hover {
  background-color: #234785;
}

.timeline-wrapper {
  flex: 1;
  padding: 10px;
  overflow: auto;
}
</style> 