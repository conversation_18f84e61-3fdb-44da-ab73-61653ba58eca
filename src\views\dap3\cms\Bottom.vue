<template>
    <div class="gantt-chart-container">
  
      <div class="table-container">
        <table class="gantt-table">
          <thead>
            <tr class="year-header">
              <th class="category-cell"></th>
              <th class="time-year-header" colspan="19">2024年度</th>
            </tr>
            <tr class="timeline-header">
              <th class="category-cell">列表</th>
              <th v-for="(label, index) in timeLabels" :key="index" class="time-cell">
                {{ label }}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="item in dataSource" :key="item.key" class="data-row">
              <td class="category-cell">
                <div class="category-item">
                  <span class="category-text">{{ item.category }}</span>
                  <span class="category-arrow" :style="{ borderLeftColor: item.color }"></span>
                </div>
              </td>
              <td v-for="(label, index) in timeLabels" :key="index" class="timeline-cell">
                <div class="cell-container">
                  <!-- 如果该字段有数据则显示节点 -->
                  <div v-if="hasPointForLabel(item.points, index)" class="time-point">
                    <!-- 添加popover组件用于显示type信息 - 应用于所有数据点 -->
                    <a-popover
                      placement="top"
                      trigger="hover"
                      :overlayStyle="{ maxWidth: '250px' }"
                    >
                      <template #content>
                        <div class="popover-content">
                          <p v-for="(type, i) in getPointTypeForLabel(item.points, index)" :key="i"><span style="color: #04c1e5;">{{ type }}</span></p>
                        </div>
                      </template>
                      <div class="point-marker" :style="getPointMarkerStyle(item.color, index, item.points)">
                        {{ getPointValueForLabel(item.points, index) }}
                      </div>
                    </a-popover>
                    
                    <!-- 第一个数据点 - 只有右延伸线 -->
                    <div 
                      v-if="isFirstPoint(item.points, index)" 
                      class="right-extend-line" 
                      :style="getConnectorStyle(item.color)">
                    </div>
                    
                    <!-- 中间数据点 - 左右延伸线 -->
                    <div v-if="isMiddlePoint(item.points, index)" class="middle-line-container">
                      <div class="left-extend" :style="getConnectorStyle(item.color)"></div>
                      <div class="right-extend" :style="getConnectorStyle(item.color)"></div>
                    </div>
                    
                    <!-- 最后一个数据点 - 只有左延伸线 -->
                    <div 
                      v-if="isLastPoint(item.points, index)" 
                      class="left-extend-line" 
                      :style="getConnectorStyle(item.color)">
                    </div>
          </div>
                  
                  <!-- 连接线和箭头 - 如果当前单元格有数据点且下一个单元格也有数据点，则显示连接线和箭头 -->
                  <div 
                    v-if="hasPointForLabel(item.points, index) && getNextPointIndex(item.points, index) !== -1" 
                    class="connector-line" 
                    :style="getConnectorStyle(item.color, getNextPointIndex(item.points, index) - index)">
                    <div class="timeline-arrow" :style="getArrowStyle(item.color)"></div>
            </div>
                  
                  <!-- 如果当前格没有点，但是前后都有点，显示通过线 -->
                  <div 
                    v-if="!hasPointForLabel(item.points, index) && 
                        isPointAnyBefore(item.points, index) && 
                        isPointAnyAfter(item.points, index)" 
                    class="pass-through-line" 
                    :style="getConnectorStyle(item.color)">
          </div>
      </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </template>
  
  <script setup>
  import { ref, computed } from "vue";
  import { Popover as APopover } from 'ant-design-vue';
  
  // 时间标签
  const timeLabels = [
    '-24M', '-22M', '-20M', '-14M', '-12M', '-11M', '-10M', '-9M', 
    '-8M', '-7M', '-6M', '-5M', '-4M', '-3M', '-2M', '-1M', 
    '-2W', '-3D', '-1D'
  ];
  
  // 表格列定义
  const columns = [
    {
      title: '类别',
      dataIndex: 'category',
      key: 'category',
    
    },
    {
      title: '时间线',
      dataIndex: 'timeline',
      key: 'timeline'
    }
  ];
  
  // 统一的数据源格式 - 修改为新格式，包含value、type和status
  const dataSource = [
    {
      key: '1',
      category: '组织管理',
      color: '#00ccff',
      points: {
        '-20M': {value: 4, type: ['初期规划', '团队组建'], status: 1},
        '-11M': {value: 1, type: ['第一阶段审核'], status: 0},  
        '-5M': {value: 1, type: ['中期检查'], status: 1},
        '-3M': {value: 1, type: ['团队调整'], status: 0},
        '-1M': {value: 4, type: ['最终确认'], status: 1},
        '-1D': {value: 1, type: ['项目启动'], status: 1}
      }
    },
    {
      key: '2',
      category: '项目',
      color: '#00ff91',
      points: {
        '-14M': {value: 1, type: ['项目立项'], status: 1},
        '-9M': {value: 12, type: ['需求分析'], status: 0},
        '-6M': {value: 12, type: ['方案设计'], status: 1},
        '-2M': {value: 1222, type: ['项目评审'], status: 0},
        '-3D': {value: 1, type: ['项目排期'], status: 1}
      }
    },
    {
      key: '3',
      category: '物资',
      color: '#ffcc00',
      points: {
        '-24M': {value: 1, type: ['物资预算'], status: 0},
        '-12M': {value: 1, type: ['初步采购'], status: 1},
        '-7M': {value: 1, type: ['设备检查'], status: 0},
        '-4M': {value: 1, type: ['物资清点'], status: 1}
      }
    },
    {
      key: '4',
      category: '合同',
      color: '#ca3cff',
      points: {
        '-22M': {value: 2, type: ['合同起草','合同起草','合同起草'], status: 0},
        '-10M': {value: 1, type: ['合同评审'], status: 1},
        '-6M': {value: 1, type: ['合同修订'], status: 0},
        '-2M': {value: 1, type: ['合同确认'], status: 1},
        '-2W': {value: 1, type: ['法务审核'], status: 0},
        '-1D': {value: 4, type: ['合同签订'], status: 1}
      }
    },
    {
      key: '5',
      category: '工具箱',
      color: '#ff944d',
      points: {
        '-5M': {value: 6, type: ['工具采购'], status: 1},
        '-3M': {value: 1, type: ['工具验收'], status: 0},
        '-1M': {value: 1, type: ['工具培训'], status: 1},
        '-3D': {value: 1, type: ['工具交付'], status: 0}
      }
    },
    {
      key: '6',
      category: '计划',
      color: '#00ccff',
      points: {
        '-9M': {value: 1, type: ['计划制定'], status: 0},
        '-7M': {value: 1, type: ['计划调整'], status: 1},
        '-5M': {value: 1, type: ['计划审核'], status: 0},
        '-2M': {value: 1, type: ['计划确认'], status: 1},
        '-1D': {value: 1, type: ['计划发布'], status: 0}
      }
    },
    {
      key: '7',
      category: '文件',
      color: '#ff66cc',
      points: {
        '-11M': {value: 2, type: ['文档编写'], status: 1},
        '-4M': {value: 1, type: ['文档审核'], status: 0},
        '-2W': {value: 5, type: ['文档归档'], status: 1}
      }
    }
  ];
  
  // 检查某个标签是否有对应的数据点 - 适应新数据结构
  const hasPointForLabel = (points, index) => {
    if (index < 0 || index >= timeLabels.length) return false;
    const label = timeLabels[index];
    return points[label] !== undefined;
  };
  
  // 获取某个标签对应的数据点值 - 适应新数据结构
  const getPointValueForLabel = (points, index) => {
    const label = timeLabels[index];
    return points[label]?.value;
  };
  
  // 获取某个标签对应的数据点类型 - 新增函数
  const getPointTypeForLabel = (points, index) => {
    const label = timeLabels[index];
    return points[label]?.type || [];
  };
  
  // 检查特定索引是否有数据点 - 适应新数据结构
  const isPointAtIndex = (points, index) => {
    if (index < 0 || index >= timeLabels.length) return false;
    const label = timeLabels[index];
    return points[label] !== undefined;
  };
  
  // 检查索引之前是否有任何数据点
  const isPointAnyBefore = (points, index) => {
    for (let i = 0; i < index; i++) {
      if (isPointAtIndex(points, i)) {
        return true;
      }
    }
    return false;
  };
  
  // 检查索引之后是否有任何数据点
  const isPointAnyAfter = (points, index) => {
    for (let i = index + 1; i < timeLabels.length; i++) {
      if (isPointAtIndex(points, i)) {
        return true;
      }
    }
    return false;
  };
  
  // 检查是否是该数据行的第一个数据点
  const isFirstPoint = (points, index) => {
    if (!hasPointForLabel(points, index)) return false;
    
    // 检查是否有更早的数据点
    for (let i = 0; i < index; i++) {
      if (isPointAtIndex(points, i)) {
        return false;
      }
    }
    return true;
  };
  
  // 检查是否是该数据行的最后一个数据点
  const isLastPoint = (points, index) => {
    if (!hasPointForLabel(points, index)) return false;
    
    // 检查是否有更晚的数据点
    for (let i = index + 1; i < timeLabels.length; i++) {
      if (isPointAtIndex(points, i)) {
        return false;
      }
    }
    return true;
  };
  
  // 检查是否是该数据行的中间数据点(既不是第一个也不是最后一个)
  const isMiddlePoint = (points, index) => {
    return hasPointForLabel(points, index) && !isFirstPoint(points, index) && !isLastPoint(points, index);
  };
  
  // 获取某个标签对应的数据点status - 新增函数
  const getPointStatusForLabel = (points, index) => {
    const label = timeLabels[index];
    return points[label]?.status !== undefined ? points[label].status : 1; // 默认为实心(1)
  };
  
  // 获取节点标记样式
  const getPointMarkerStyle = (color, index, points) => {
    // 获取当前点的status
    const label = timeLabels[index];
    const status = points[label]?.status !== undefined ? points[label].status : 1; // 默认为实心(1)
    
    // status为1时显示实心，为0时显示镂空
    if (status === 1) {
      // 实心样式
      return {
        backgroundColor: color,
        boxShadow: `0 0 10px ${color}`
      };
    } else {
      // 镂空样式 - 添加白色背景阻止线穿过
      return {
        backgroundColor: '#192347', // 使用与甘特图背景相同的颜色
        border: `2px solid ${color}`,
        boxShadow: `0 0 10px ${color}`,
        width: '12px',
        height: '12px'
      };
    }
  };
  
  // 获取连接线样式
  const getConnectorStyle = (color, span = 1) => {
    // 如果间隔超过1个单元格，则调整宽度
    const width = span > 1 ? `calc(${span * 100}% - 0px)` : 'calc(100% - 0px)';
    
    return {
      backgroundColor: color,
      boxShadow: `0 0 5px ${color}`,
      width: width
    };
  };
  
  // 获取箭头样式
  const getArrowStyle = (color) => {
    return {
      borderLeftColor: color
    };
  };
  
  // 获取下一个数据点的索引
  const getNextPointIndex = (points, currentIndex) => {
    for (let i = currentIndex + 1; i < timeLabels.length; i++) {
      if (isPointAtIndex(points, i)) {
        return i;
      }
    }
    return -1; // 没有找到下一个点
  };
  
  // 全局注册Popover组件
  const components = {
    'a-popover': APopover
  };
  </script>
  
  <style scoped>
  .gantt-chart-container {
    padding: 0;
    border-radius: 0;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
    width: 100%;
    height: 100%;
  }
  
  .table-container {
    position: relative;
    width: 100%;
    overflow: auto;
  }
  
  .gantt-table {
    width: 100%;
    border-collapse: collapse;
    background: #192347;
    color: white;
    table-layout: fixed;
    border-spacing: 0;
  }
  
  /* 表头样式 */
  .timeline-header {
    border-bottom: 1px solid #11457b;
  }
  
  .category-cell {
    width: 65px;
    background-color: #192347;
    text-align: center;
    border-right: 1px solid #11457b;
    vertical-align: middle;
    color: #04c1e5;
     font-size: 14px;
  }
  
  .time-cell {
    background-color: #192347;
    text-align: center;
    color: #ffffff;
    font-weight: 400;
    font-size: 12px;
    /* padding: 8px 4px; */
    width: 50px;
    border-bottom: none;
  }
  
  /* 年份表头样式 */
  .year-header {
    /* height: 30px; */
  }
  
  .time-year-header {
    background-color: #192347;
    text-align: center;
    color: #04c1e5;
    font-size: 16px;
    font-weight: 400;
    /* padding: 5px 0; */
    border-bottom: none;
  }
  
  /* 数据行样式 */
  .data-row {
    height: 28px;
  }
  
  /* 时间单元格 */
  .timeline-cell {
    padding: 0;
    background-color: #192347;
    position: relative;
    width: 20px;
    height: 10px;
    border-right: 1px solid rgba(78, 160, 212, 0.15);
    border-bottom: 1px solid rgba(78, 160, 212, 0.15);
  }
  
  /* 单元格容器 */
  .cell-container {
    position: relative;
    width: 100%;
    height: 100%;
  }
  
  /* 连接线样式 */
  .connector-line {
    position: absolute;
    top: 50%;
    left: 50%; /* 从圆点半径位置开始(24px/2) */
    width: calc(100% - 0px); /* 减去起始偏移量 */
    height: 2px;
    background-color: #00ccff;
    transform: translateY(-50%);
    box-shadow: 0 0 5px rgba(0, 204, 255, 0.5);
    z-index: 2;
  }
  
  /* 通过线样式 */
  .pass-through-line {
    position: absolute;
    top: 50%;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: #00ccff;
    transform: translateY(-50%);
    box-shadow: 0 0 5px rgba(0, 204, 255, 0.5);
    opacity: 0.6;
    z-index: 2;
  }
  
  /* 时间线箭头 */
  .timeline-arrow {
    position: absolute;
    top: 50%;
    left: 50%; /* 箭头位置调整到线的中间 */
    transform: translate(-50%, -50%); /* 居中对齐 */
    width: 0;
    height: 0;
    border-top: 6px solid transparent;
    border-bottom: 6px solid transparent;
    border-left: 10px solid #00ccff;
    filter: drop-shadow(0 0 2px rgba(0, 204, 255, 0.5));
    animation: arrowPulse 1.5s infinite;
    z-index: 2;
  }
  
  /* 第一个点的右延伸线 */
  /* .right-extend-line {
    position: absolute;
    top: 50%;
    left: 100%;
    width: 25px;
    height: 2px;
    transform: translateY(-50%);
    z-index: 1;
  } */
  
  /* 中间点的线容器 */
  .middle-line-container {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 2;
    pointer-events: none; /* 允许鼠标事件穿透此容器及其子元素 */
  }
  
  /* 左侧延伸 */
  /* .left-extend {
    position: absolute;
    top: 50%;
    right: 100%;
    width: 45px;
    height: 2px;
    transform: translateY(-50%);
  } */
  
  /* 右侧延伸 */
  /* .right-extend {
    position: absolute;
    top: 50%;
    left: 100%;
    width: 45px;
    height: 2px;
    transform: translateY(-50%);
  } */
  
  /* 最后一个点的左延伸线 */
  /* .left-extend-line {
    position: absolute;
    top: 50%;
    right: 100%;
    width: 25px;
    height: 2px;
    transform: translateY(-50%);
    z-index: 1;
  } */
  
  @keyframes arrowPulse {
    0% {
      opacity: 0.7;
      transform: translate(-50%, -50%) scale(0.9);
    }
    50% {
      opacity: 1;
      transform: translate(-50%, -50%) scale(1.1);
    }
    100% {
      opacity: 0.7;
      transform: translate(-50%, -50%) scale(0.9);
    }
  }
  
  /* 时间点样式 */
  .time-point {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 13px;
    height: 13px;
    z-index: 5;
  }
  
  .point-marker {
    width: 100%;
    height: 100%;
    background-color: #00ccff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 10px;
    box-shadow: 0 0 8px rgba(0, 204, 255, 0.7);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    cursor: pointer;
    z-index: 6;
  }
  
  .point-marker:hover {
    transform: scale(1.15);
    box-shadow: 0 0 12px rgba(0, 204, 255, 0.9);
    z-index: 10;
  }
  
  /* 中间点的左右延伸线 */
  .middle-extend-lines {
    position: absolute;
    width: 100%;
    height: 100%;
  }
  
  .middle-extend-lines::before, 
  .middle-extend-lines::after {
    content: "";
    position: absolute;
    top: 50%;
    height: 2px;
    transform: translateY(-50%);
    z-index: 1;
    background-color: inherit; /* 继承父元素的背景色 */
  }
  
  /* 左延伸线 */
  .middle-extend-lines::before {
    right: 50%;
    width: 25px; /* 左延伸线宽度 */
  }
  
  /* 右延伸线 */
  .middle-extend-lines::after {
    left: 50%;
    width: 25px; /* 右延伸线宽度 */
  }
  
  /* 类别项目容器 */
  .category-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
  }
  
  /* 类别文本 */
  .category-text {
    color: white;
    font-weight: 400;
    font-size: 12px;
    flex: 1;
    text-align: left;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  
  /* 类别箭头 */
  .category-arrow {
    width: 0;
    height: 0;
    border-top: 5px solid transparent;
    border-bottom: 5px solid transparent;
    border-left: 7px solid;
    margin-left: 8px;
  }
  
  /* Popover内容样式 */
  :deep(.ant-popover-inner-content) {
    padding: 10px;
    background-color: rgba(25, 35, 71, 0.95);
    border-radius: 4px;
    color: white;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
  }
  
  .popover-content {
    max-width: 200px;
    word-break: break-all;
  }
  
  .popover-content p {
    margin: 5px 0;
    line-height: 1.5;
    word-wrap: break-word;
    white-space: normal;
    color: #fff !important;
    font-size: 13px;
    display: flex;
    align-items: center;
  }
  
  .popover-content p::before {
    content: "•";
    color: #04c1e5;
    margin-right: 5px;
    font-size: 16px;
  }
  </style>