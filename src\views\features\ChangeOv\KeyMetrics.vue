<template>
  <div class="key-metrics-container">
    <div v-for="(metric, index) in metrics" :key="index" class="metric-item">
      <span class="metric-label">{{ metric.label }}</span>
      <span class="metric-value">{{ metric.value }}</span>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';

const metrics = ref([
  { label: '重点变更跟踪', value: 5 },
  { label: '需要申报NNSA', value: 5 },
  { label: '涉及修改FSAR', value: 1 },
  { label: '涉及SPV设备', value: 3 },
  { label: '工单未创建', value: 4 },
  { label: '服务合同签订中', value: 2 },
  { label: '采购未提出', value: 4 },
  { label: '大修前不能到货', value: 1 },
]);
</script>

<style scoped>
.key-metrics-container {
  padding: 5px; /* Add slight padding around the list */
  height: 100%;
  overflow-y: auto; /* Enable vertical scrolling if content overflows */
  box-sizing: border-box;
}

.metric-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #fff;
  /* Attempting subtle wave background - adjust as needed */
  background-image: linear-gradient(135deg, #e6f7ff 25%, transparent 25%, transparent 50%, #e6f7ff 50%, #e6f7ff 75%, transparent 75%, transparent 100%);
  background-size: 40px 40px; /* Adjust size of the pattern */
  border-radius: 8px; /* More rounded corners */
  padding: 10px 15px; /* Adjust padding */
  margin-bottom: 10px; /* Space between items */
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08); /* Subtle shadow */
  transition: box-shadow 0.3s ease;
}

.metric-item:last-child {
  margin-bottom: 0;
}

.metric-item:hover {
   box-shadow: 0 3px 8px rgba(0, 0, 0, 0.12);
}

.metric-label {
  font-size: 14px;
  color: #3b82f6; /* Blue color for label */
  font-weight: 500;
}

.metric-value {
  font-size: 18px; /* Larger font size for value */
  color: #2563eb; /* Darker blue for value */
  font-weight: 600;
}
</style> 