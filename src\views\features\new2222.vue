<!-- 父组件中 -->
<template>
  <button @click="openAddModal">打开</button>
  <AddModal ref="addModalRef" />
</template>

<script setup>
import { ref } from "vue";
import AddModal from "./add.vue";

const addModalRef = ref(null);

const openAddModal = () => {
  addModalRef.value?.openModal();
};

// 检查时间重叠的方法
const checkTimeOverlap = (startTime, endTime) => {
  const periods = [
    [new Date("2024/06/01").getTime(), new Date("2024/09/20").getTime()],
    [new Date("2024/11/16").getTime(), new Date("2031/02/10").getTime()],
  ];

  const start = new Date(startTime).getTime();
  const end = new Date(endTime).getTime();

  return periods.some(
    ([periodStart, periodEnd]) =>
      (periodStart <= start && start <= periodEnd) ||
      (start <= periodStart && end >= periodStart)
  );
};

// 测试方法
const testTime = () => {
  console.log("是否有重叠:", checkTimeOverlap("2024/04/01", "2030/04/01"));
};

testTime();
</script>
