<template>
  <div class="gantt-chart-container">
    <table class="gantt-table">
      <thead>
        <tr class="timeline-header">
          <th class="category-cell"></th>
          <th class="time-header" colspan="26">
            <div class="time-header-content">
              <div class="year">2024年8月</div>
              <div class="months">
                <span v-for="(label, index) in timeLabels" :key="index" class="time-label">
                  {{ label }}
                </span>
              </div>
            </div>
          </th>
        </tr>
      </thead>
      <tbody>
        <!-- 组织管理行 -->
        <tr class="data-row">
          <td class="category-cell">组织管理</td>
          <td class="timeline-cell" colspan="26">
            <div class="timeline-container">
              <!-- 时间节点标记 -->
              <div v-for="(point, index) in organizationPoints" :key="'org-'+index"
                class="time-point" :style="getPointStyle(point)">
                <div class="point-marker">{{ point.value }}</div>
              </div>
              <!-- 时间轴线 -->
              <div class="timeline-line" :style="getLineStyle(organizationPoints)"></div>
            </div>
          </td>
        </tr>
        <!-- 项目行 -->
        <tr class="data-row">
          <td class="category-cell">项目</td>
          <td class="timeline-cell" colspan="26">
            <div class="timeline-container">
              <!-- 时间节点标记 -->
              <div v-for="(point, index) in projectPoints" :key="'proj-'+index"
                class="time-point" :style="getPointStyle(point)">
                <div class="point-marker">{{ point.value }}</div>
              </div>
              <!-- 时间轴线 -->
              <div class="timeline-line" :style="getLineStyle(projectPoints)"></div>
            </div>
          </td>
        </tr>
        <!-- 物资行 -->
        <tr class="data-row">
          <td class="category-cell">物资</td>
          <td class="timeline-cell" colspan="26">
            <div class="timeline-container">
              <!-- 时间节点标记 -->
              <div v-for="(point, index) in supplyPoints" :key="'sup-'+index"
                class="time-point" :style="getPointStyle(point)">
                <div class="point-marker">{{ point.value }}</div>
              </div>
              <!-- 时间轴线 -->
              <div class="timeline-line" :style="getLineStyle(supplyPoints)"></div>
            </div>
          </td>
        </tr>
        <!-- 合同行 -->
        <tr class="data-row">
          <td class="category-cell">合同</td>
          <td class="timeline-cell" colspan="26">
            <div class="timeline-container">
              <!-- 时间节点标记 -->
              <div v-for="(point, index) in contractPoints" :key="'cont-'+index"
                class="time-point" :style="getPointStyle(point)">
                <div class="point-marker">{{ point.value }}</div>
              </div>
              <!-- 时间轴线 -->
              <div class="timeline-line" :style="getLineStyle(contractPoints)"></div>
            </div>
          </td>
        </tr>
        <!-- 工具箱行 -->
        <tr class="data-row">
          <td class="category-cell">工具箱</td>
          <td class="timeline-cell" colspan="26">
            <div class="timeline-container">
              <!-- 时间节点标记 -->
              <div v-for="(point, index) in toolboxPoints" :key="'tool-'+index"
                class="time-point" :style="getPointStyle(point)">
                <div class="point-marker">{{ point.value }}</div>
              </div>
              <!-- 时间轴线 -->
              <div class="timeline-line" :style="getLineStyle(toolboxPoints)"></div>
            </div>
          </td>
        </tr>
        <!-- 计划行 -->
        <tr class="data-row">
          <td class="category-cell">计划</td>
          <td class="timeline-cell" colspan="26">
            <div class="timeline-container">
              <!-- 时间节点标记 -->
              <div v-for="(point, index) in planPoints" :key="'plan-'+index"
                class="time-point" :style="getPointStyle(point)">
                <div class="point-marker">{{ point.value }}</div>
              </div>
              <!-- 时间轴线 -->
              <div class="timeline-line" :style="getLineStyle(planPoints)"></div>
            </div>
          </td>
        </tr>
        <!-- 文件行 -->
        <tr class="data-row">
          <td class="category-cell">文件</td>
          <td class="timeline-cell" colspan="26">
            <div class="timeline-container">
              <!-- 时间节点标记 -->
              <div v-for="(point, index) in filePoints" :key="'file-'+index"
                class="time-point" :style="getPointStyle(point)">
                <div class="point-marker">{{ point.value }}</div>
              </div>
              <!-- 时间轴线 -->
              <div class="timeline-line" :style="getLineStyle(filePoints)"></div>
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<script setup>
import { ref } from 'vue';

// 时间标签
const timeLabels = [
  '-24M', '-22M', '-20M', '-14M', '-12M', '-11M', '-10M', '-9M', 
  '-8M', '-7M', '-6M', '-5M', '-4M', '-3M', '-2M', '-1M', 
  '-2W', '-3D', '-1D', null, null, null, null, null, null, null
];

// 各类别的时间点数据
// 位置基于百分比（0-100），值表示节点上显示的数字
const organizationPoints = [
  { position: 9, value: 4 },
  { position: 16, value: 1 },
  { position: 29, value: 1 },
  { position: 42, value: 1 },
  { position: 57, value: 1 },
  { position: 70, value: 4 },
  { position: 85, value: 1 }
];

const projectPoints = [
  { position: 33, value: 1 },
  { position: 44, value: 1 },
  { position: 70, value: 1 },
  { position: 85, value: 1 },
  { position: 94, value: 1 }
];

const supplyPoints = [
  { position: 5, value: 1 },
  { position: 12, value: 1 },
  { position: 19, value: 1 },
  { position: 26, value: 1 }
];

const contractPoints = [
  { position: 10, value: 2 },
  { position: 20, value: 1 },
  { position: 30, value: 1 },
  { position: 50, value: 1 },
  { position: 70, value: 1 },
  { position: 85, value: 4 }
];

const toolboxPoints = [
  { position: 70, value: 1 },
  { position: 80, value: 1 },
  { position: 90, value: 1 },
  { position: 95, value: 1 }
];

const planPoints = [
  { position: 55, value: 1 },
  { position: 65, value: 1 },
  { position: 75, value: 1 },
  { position: 85, value: 1 },
  { position: 95, value: 1 }
];

const filePoints = [
  { position: 45, value: 2 },
  { position: 55, value: 1 },
  { position: 85, value: 5 }
];

// 获取时间点的样式
const getPointStyle = (point) => {
  return {
    left: `${point.position}%`,
    zIndex: 2
  };
};

// 获取连接线的样式
const getLineStyle = (points) => {
  // 查找最左和最右的点位置，用于确定线的起点和终点
  if (points.length < 2) return { display: 'none' };
  
  let leftmost = 100;
  let rightmost = 0;
  
  points.forEach(point => {
    if (point.position < leftmost) leftmost = point.position;
    if (point.position > rightmost) rightmost = point.position;
  });
  
  return {
    left: `${leftmost}%`,
    width: `${rightmost - leftmost}%`
  };
};
</script>

<style scoped>
.gantt-chart-container {
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: #0d1a2f;
  color: white;
}

.gantt-table {
  width: 100%;
  border-collapse: collapse;
  table-layout: fixed;
}

/* 表头样式 */
.timeline-header {
  height: 60px;
}

.category-cell {
  width: 100px;
  background-color: #0d1a2f;
  text-align: center;
  padding: 10px;
  border-right: 2px solid #0d1a2f;
  vertical-align: middle;
  color: #9ce5ff;
}

.time-header {
  background-color: #0d1a2f;
  padding: 0;
}

.time-header-content {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
}

.year {
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #9ce5ff;
  font-size: 18px;
}

.months {
  display: flex;
  height: 30px;
  width: 100%;
}

.time-label {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #9ce5ff;
  font-size: 12px;
}

/* 数据行样式 */
.data-row {
  height: 40px;
}

.timeline-cell {
  padding: 0;
  background-color: #152642;
  position: relative;
}

.timeline-container {
  position: relative;
  width: 100%;
  height: 100%;
}

/* 时间轴样式 */
.timeline-line {
  position: absolute;
  top: 50%;
  height: 2px;
  background-color: #00ccff;
  transform: translateY(-50%);
}

/* 时间点样式 */
.time-point {
  position: absolute;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 24px;
  height: 24px;
}

.point-marker {
  width: 100%;
  height: 100%;
  background-color: #00ccff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 12px;
}

/* 行色彩样式 */
tr:nth-child(1) .category-cell {
  color: #00ccff;
}

tr:nth-child(1) .timeline-line, 
tr:nth-child(1) .point-marker {
  background-color: #00ccff;
}

tr:nth-child(2) .category-cell {
  color: #00ff91;
}

tr:nth-child(2) .timeline-line, 
tr:nth-child(2) .point-marker {
  background-color: #00ff91;
}

tr:nth-child(3) .category-cell {
  color: #ffcc00;
}

tr:nth-child(3) .timeline-line, 
tr:nth-child(3) .point-marker {
  background-color: #ffcc00;
}

tr:nth-child(4) .category-cell {
  color: #ca3cff;
}

tr:nth-child(4) .timeline-line, 
tr:nth-child(4) .point-marker {
  background-color: #ca3cff;
}

tr:nth-child(5) .category-cell {
  color: #ff944d;
}

tr:nth-child(5) .timeline-line, 
tr:nth-child(5) .point-marker {
  background-color: #ff944d;
}

tr:nth-child(6) .category-cell {
  color: #00ccff;
}

tr:nth-child(6) .timeline-line, 
tr:nth-child(6) .point-marker {
  background-color: #00ccff;
}

tr:nth-child(7) .category-cell {
  color: #ff66cc;
}

tr:nth-child(7) .timeline-line, 
tr:nth-child(7) .point-marker {
  background-color: #ff66cc;
}
</style> 