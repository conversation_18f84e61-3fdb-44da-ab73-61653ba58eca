<template>
  <div class="special-performance-evaluation">
    <!-- 专项绩效评价表格 -->
    <a-table
      :dataSource="specialPerformanceData"
      :columns="specialPerformanceColumns"
      :pagination="paginationConfig"
      :scroll="{ y: 400 }"
      bordered
      size="middle"
      class="performance-table"
      :customRow="
        (record) => ({
          onDblclick: () => handleSpecialRowDblClick(record),
        })
      "
      @change="handleTableChange"
    >
      <!-- 操作列 -->
      <template #bodyCell="{ column, text, record }">
        <template v-if="column.dataIndex === 'operation'">
          <div class="operation-buttons">
            <template v-if="isSpecialEditing(record)">
              <!-- 编辑状态下的保存和取消按钮 -->
              <a-button
                type="primary"
                shape="circle"
                size="small"
                @click="saveSpecialEdit(record)"
              >
                <template #icon><CheckOutlined /></template>
              </a-button>
              <a-button
                type="danger"
                shape="circle"
                size="small"
                @click="cancelSpecialEdit"
              >
                <template #icon><CloseOutlined /></template>
              </a-button>
            </template>
            <template v-else>
              <!-- 非编辑状态下的编辑按钮 -->
              <a-button
                type="primary"
                shape="circle"
                size="small"
                @click="handleSpecialRowDblClick(record)"
              >
                <template #icon><EditOutlined /></template>
              </a-button>
            </template>
          </div>
        </template>

        <!-- 可编辑的列 -->
        <template
          v-else-if="
            isSpecialEditing(record) &&
            [
              'score',
              'addScore',
              'addScoreDesc',
              'finalScore',
              'deductScoreDesc',
            ].includes(column.dataIndex)
          "
        >
          <!-- 多行文本 -->
          <template
            v-if="
              ['addScoreDesc', 'deductScoreDesc'].includes(column.dataIndex)
            "
          >
            <a-textarea
              v-model:value="record[column.dataIndex]"
              :rows="1"
              style="width: 100%"
            />
          </template>

          <!-- 普通输入框 -->
          <template v-else>
            <a-input
              v-model:value="record[column.dataIndex]"
              style="width: 100%"
            />
          </template>
        </template>

        <!-- 加分项描述和扣分项描述列的"---"替换 -->
        <template v-else-if="isScoreDesc(column.dataIndex) && text === '---'">
          <span class="dash-text">---</span>
        </template>

        <!-- 渲染其他所有非特殊处理列 -->
        <template v-else>
          {{ text }}
        </template>
      </template>
    </a-table>
  </div>
</template>

<script setup>
import { ref, computed } from "vue";
import {
  CheckOutlined,
  CloseOutlined,
  EditOutlined,
} from "@ant-design/icons-vue";
import { Input } from "ant-design-vue";

// 专项绩效评价表格数据
const specialPerformanceData = ref([
  {
    key: "1",
    serialNo: 1,
    projectName: "主发电机检修",
    projectType: "处室级",
    unit: "核电集团",
    department: "维修一处",
    personInCharge: "张三",
    responsibleDirector: "李四",
    score: "89",
    addScore: "10",
    addScoreDesc: "222222",
    finalScore: "10",
    deductScoreDesc: "---",
  },
  {
    key: "2",
    serialNo: 2,
    projectName: "核级泵电机检修",
    projectType: "处室级",
    unit: "核电集团",
    department: "维修一处",
    personInCharge: "张三",
    responsibleDirector: "李四",
    score: "87",
    addScore: "12",
    addScoreDesc: "---",
    finalScore: "12",
    deductScoreDesc: "---",
  },
  {
    key: "3",
    serialNo: 3,
    projectName: "02厂房非IE级频地控制柜更换",
    projectType: "处室级",
    unit: "核电集团",
    department: "维修一处",
    personInCharge: "张三",
    responsibleDirector: "李四",
    score: "100",
    addScore: "9",
    addScoreDesc: "---",
    finalScore: "9",
    deductScoreDesc: "---",
  },
  {
    key: "4",
    serialNo: 4,
    projectName: "主发电机检修",
    projectType: "处室级",
    unit: "核电集团",
    department: "维修一处",
    personInCharge: "张三",
    responsibleDirector: "李四",
    score: "100",
    addScore: "7",
    addScoreDesc: "---",
    finalScore: "7",
    deductScoreDesc: "---",
  },
  {
    key: "5",
    serialNo: 5,
    projectName: "核级泵电机检修",
    projectType: "处室级",
    unit: "核电集团",
    department: "维修一处",
    personInCharge: "张三",
    responsibleDirector: "李四",
    score: "100",
    addScore: "0",
    addScoreDesc: "---",
    finalScore: "0",
    deductScoreDesc: "---",
  },
  {
    key: "6",
    serialNo: 6,
    projectName: "02厂房非IE级频地控制柜更换",
    projectType: "处室级",
    unit: "核电集团",
    department: "维修一处",
    personInCharge: "张三",
    responsibleDirector: "李四",
    score: "89",
    addScore: "10",
    addScoreDesc: "---",
    finalScore: "10",
    deductScoreDesc: "---",
  },
  {
    key: "7",
    serialNo: 7,
    projectName: "主发电机检修",
    projectType: "处室级",
    unit: "核电集团",
    department: "维修一处",
    personInCharge: "张三",
    responsibleDirector: "李四",
    score: "87",
    addScore: "12",
    addScoreDesc: "---",
    finalScore: "12",
    deductScoreDesc: "---",
  },
  {
    key: "8",
    serialNo: 8,
    projectName: "核级泵电机检修",
    projectType: "处室级",
    unit: "核电集团",
    department: "维修一处",
    personInCharge: "张三",
    responsibleDirector: "李四",
    score: "100",
    addScore: "9",
    addScoreDesc: "---",
    finalScore: "9",
    deductScoreDesc: "---",
  },
  {
    key: "9",
    serialNo: 9,
    projectName: "02厂房非IE级频地控制柜更换",
    projectType: "处室级",
    unit: "核电集团",
    department: "维修一处",
    personInCharge: "张三",
    responsibleDirector: "李四",
    score: "100",
    addScore: "7",
    addScoreDesc: "---",
    finalScore: "7",
    deductScoreDesc: "---",
  },
  {
    key: "10",
    serialNo: 10,
    projectName: "主发电机检修",
    projectType: "处室级",
    unit: "核电集团",
    department: "维修一处",
    personInCharge: "张三",
    responsibleDirector: "李四",
    score: "100",
    addScore: "0",
    addScoreDesc: "---",
    finalScore: "0",
    deductScoreDesc: "---",
  },
  {
    key: "11",
    serialNo: 11,
    projectName: "核级泵电机检修",
    projectType: "处室级",
    unit: "核电集团",
    department: "维修一处",
    personInCharge: "张三",
    responsibleDirector: "李四",
    score: "89",
    addScore: "10",
    addScoreDesc: "---",
    finalScore: "10",
    deductScoreDesc: "---",
  },
  {
    key: "12",
    serialNo: 12,
    projectName: "02厂房非IE级频地控制柜更换",
    projectType: "处室级",
    unit: "核电集团",
    department: "维修一处",
    personInCharge: "张三",
    responsibleDirector: "李四",
    score: "87",
    addScore: "12",
    addScoreDesc: "---",
    finalScore: "12",
    deductScoreDesc: "---",
  },
  {
    key: "13",
    serialNo: 13,
    projectName: "主发电机检修",
    projectType: "处室级",
    unit: "核电集团",
    department: "维修一处",
    personInCharge: "张三",
    responsibleDirector: "李四",
    score: "100",
    addScore: "9",
    addScoreDesc: "---",
    finalScore: "9",
    deductScoreDesc: "---",
  },
  {
    key: "14",
    serialNo: 14,
    projectName: "核级泵电机检修",
    projectType: "处室级",
    unit: "核电集团",
    department: "维修一处",
    personInCharge: "张三",
    responsibleDirector: "李四",
    score: "100",
    addScore: "7",
    addScoreDesc: "---",
    finalScore: "7",
    deductScoreDesc: "---",
  },
  {
    key: "15",
    serialNo: 15,
    projectName: "02厂房非IE级频地控制柜更换",
    projectType: "处室级",
    unit: "核电集团",
    department: "维修一处",
    personInCharge: "张三",
    responsibleDirector: "李四",
    score: "100",
    addScore: "0",
    addScoreDesc: "---",
    finalScore: "0",
    deductScoreDesc: "---",
  },
]);

// 专项绩效评价表格列
const specialPerformanceColumns = [
  { title: "序号", dataIndex: "serialNo", width: 60, align: "center" },
  { title: "操作", dataIndex: "operation", width: 60, align: "center" },
  { title: "专项名称", dataIndex: "projectName", width: 180, align: "center" },
  { title: "专项类别", dataIndex: "projectType", width: 100, align: "center" },
  { title: "单位", dataIndex: "unit", width: 100, align: "center" },
  { title: "责任部门", dataIndex: "department", width: 100, align: "center" },
  {
    title: "专项负责人",
    dataIndex: "personInCharge",
    width: 100,
    align: "center",
  },
  {
    title: "责任处长",
    dataIndex: "responsibleDirector",
    width: 100,
    align: "center",
  },
  { title: "大修绩效", dataIndex: "score", width: 100, align: "center" },
  { title: "大修绩效加分", dataIndex: "addScore", width: 120, align: "center" },
  {
    title: "加分项说明",
    dataIndex: "addScoreDesc",
    width: 120,
    align: "center",
  },
  {
    title: "大修绩效扣分",
    dataIndex: "finalScore",
    width: 120,
    align: "center",
  },
  {
    title: "扣分项说明",
    dataIndex: "deductScoreDesc",
    width: 120,
    align: "center",
  },
];

// 分页配置
const pagination = ref({
  current: 1,
  pageSize: 10,
  total: computed(() => specialPerformanceData.value.length),
  showSizeChanger: true,
  showQuickJumper: true,
  pageSizeOptions: ['10', '20', '50'],
  showTotal: (total) => `共 ${total} 条记录`,
});

// 分页配置对象
const paginationConfig = computed(() => ({
  ...pagination.value,
  // 分页本地化设置
  showTotal: (total) => `共 ${total} 条`,
  showSizeChanger: true,
  showQuickJumper: true,
  pageSize: pagination.value.pageSize,
  current: pagination.value.current,
  // 分页文案设置
  locale: {
    items_per_page: '条/页',
    jump_to: '跳至',
    jump_to_confirm: '确定',
    page: '页',
    prev_page: '上一页',
    next_page: '下一页',
    prev_5: '向前 5 页',
    next_5: '向后 5 页',
    prev_3: '向前 3 页',
    next_3: '向后 3 页',
  }
}));

// 表格变化处理（包含分页、筛选、排序）
const handleTableChange = (pagination, filters, sorter) => {
  console.log('Table params changed:', { pagination, filters, sorter });
  handlePageChange(pagination.current, pagination.pageSize);
};

// 页码变化处理
const handlePageChange = (page, pageSize) => {
  console.log('Page changed:', page, pageSize);
  pagination.value.current = page;
  pagination.value.pageSize = pageSize;
};

// 每页条数变化处理
const handlePageSizeChange = (current, size) => {
  console.log('Page size changed:', current, size);
  pagination.value.current = 1; // 重置到第一页
  pagination.value.pageSize = size;
};

// 用于判断是否为得分描述列的辅助函数
const isScoreDesc = (dataIndex) => {
  return ["addScoreDesc", "deductScoreDesc"].includes(dataIndex);
};

// 专项绩效评价相关状态
const specialEditingKey = ref("");
const isSpecialEditing = (record) => record.key === specialEditingKey.value;

// 处理专项绩效双击行事件，开始编辑
const handleSpecialRowDblClick = (record) => {
  console.log("Special row double clicked:", record);
  specialEditingKey.value = record.key;
};

// 保存专项绩效编辑后的行数据
const saveSpecialEdit = (record) => {
  console.log("Saving special edited row:", record);

  // 获取并打印被编辑的5个字段数据
  const editedData = {
    score: record.score, // 大修绩效
    addScore: record.addScore, // 大修绩效加分
    addScoreDesc: record.addScoreDesc, // 加分项说明
    finalScore: record.finalScore, // 大修绩效扣分
    deductScoreDesc: record.deductScoreDesc, // 扣分项说明
  };

  console.log("专项绩效编辑后的数据:", editedData);

  // 将修改后的数据更新到表格数据中
  const index = specialPerformanceData.value.findIndex(
    (item) => item.key === record.key
  );
  if (index !== -1) {
    specialPerformanceData.value[index] = { ...record };
  }

  // 退出编辑状态
  specialEditingKey.value = "";
};

// 取消专项绩效编辑，恢复原始数据
const cancelSpecialEdit = () => {
  console.log("Canceling special edit");

  // 直接退出编辑状态，不保存修改
  specialEditingKey.value = "";
};
</script>

<style scoped>
.special-performance-evaluation {
  width: 100%;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
}

.operation-buttons {
  display: flex;
  justify-content: space-around;
  gap: 4px;
}

.dash-text {
  color: #999;
}

/* 确保表格内容垂直居中 */
:deep(.ant-table-cell) {
  vertical-align: middle;
}

/* 分页组件样式覆盖 */
:deep(.ant-pagination-options-size-changer.ant-select) {
  margin-right: 8px;
}

:deep(.ant-pagination-options-quick-jumper) {
  margin-left: 8px;
}

:deep(.ant-pagination-item-active) {
  background-color: #1890ff;
  border-color: #1890ff;
}

:deep(.ant-pagination-item-active a) {
  color: white;
}
</style>
