<template>
  <div class="risk-container">
    <!-- 操作按钮区域 -->
    <div class="operation-section">
      <a-space>
        <a-button type="primary" @click="handleAdd">
          <plus-outlined />
          新增
        </a-button>
        <a-button @click="handleImport">
          <upload-outlined />
          导入
        </a-button>
        <a-button type="primary" danger @click="handleDelete">
          <delete-outlined />
          删除
        </a-button>
        <a-button type="primary" @click="handleSave">
          <save-outlined />
          保存
        </a-button>
        <a-button type="primary" @click="handleEdit">
          <edit-outlined />
          编辑
        </a-button>
      </a-space>
    </div>

    <!-- 表格区域 -->
    <div class="table-container">
      <a-table
        :columns="columns"
        :data-source="tableData"
        :scroll="{ x: 1500, y: 'calc(100vh - 180px)' }"
        :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
        :pagination="false"
        :hover="false"
        class="custom-table"
      >
        <template #headerCell="{ column }">
          <template v-if="column.key === 'controlMeasures'">
            <span>
              管控措施
              <plus-circle-outlined
                class="icon-button"
                @click="handleAddMeasure"
              />
              <minus-circle-outlined
                class="icon-button"
                @click="handleRemoveMeasure"
              />
            </span>
          </template>
        </template>

        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'controlMeasures'">
            <div
              v-for="(measure, index) in record.controlMeasures"
              :key="index"
              class="measure-item"
            >
              <div
                class="checkbox-container"
                v-if="selectedRowKeys.includes(record.key)"
              >
                <a-checkbox v-model:checked="record.measureChecked[index]" />
              </div>
              <a-popover
                placement="top"
                trigger="hover"
                :content="record.controlMeasures[index]"
                :overlayStyle="{ maxWidth: '400px' }"
                :open="
                  showPopover &&
                  hoverIndex === `${record.key}-${column.key}-${index}` &&
                  !selectedRowKeys.includes(record.key)
                "
                @mouseenter="
                  handleMouseEnter(`${record.key}-${column.key}-${index}`)
                "
                @mouseleave="handleMouseLeave"
              >
                <a-input
                  v-model:value="record.controlMeasures[index]"
                  :bordered="false"
                  class="measure-input"
                />
              </a-popover>
            </div>
          </template>
          <template v-else-if="column.dataIndex === 'implementationStatus'">
            <div
              v-for="(status, index) in record.implementationStatus"
              :key="index"
              class="measure-item"
            >
              <a-popover
                placement="top"
                trigger="hover"
                :content="record.implementationStatus[index]"
                :overlayStyle="{ maxWidth: '400px' }"
                :open="
                  showPopover &&
                  hoverIndex ===
                    `${record.key}-implementationStatus-${index}` &&
                  !selectedRowKeys.includes(record.key)
                "
                @mouseenter="
                  handleMouseEnter(
                    `${record.key}-implementationStatus-${index}`
                  )
                "
                @mouseleave="handleMouseLeave"
              >
                <a-input
                  v-model:value="record.implementationStatus[index]"
                  :bordered="false"
                  class="measure-input"
                />
              </a-popover>
            </div>
          </template>
        </template>
      </a-table>
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue";
import {
  PlusOutlined,
  DeleteOutlined,
  SaveOutlined,
  EditOutlined,
  UploadOutlined,
  PlusCircleOutlined,
  MinusCircleOutlined,
} from "@ant-design/icons-vue";

import { message } from "ant-design-vue";

// 表格选择相关
const selectedRowKeys = ref([]);

// 表格列定义
const columns = [
  {
    title: "序号",
    dataIndex: "index",
    width: 80,
    align: "center",
  },
  {
    title: "风险类型",
    dataIndex: "riskType",
    width: 150,
    align: "center",
  },
  {
    title: "风险点",
    dataIndex: "riskPoint",
    width: 200,
    align: "center",
  },
  {
    title: "风险后果",
    dataIndex: "riskConsequence",
    width: 200,
    align: "center",
  },
  {
    title: "管控要求",
    dataIndex: "controlRequirement",
    width: 200,
    align: "center",
  },
  {
    title: "管控措施",
    dataIndex: "controlMeasures",
    key: "controlMeasures",
    width: 300,
    align: "center",
  },
  {
    title: "措施落实情况",
    dataIndex: "implementationStatus",
    width: 200,
    align: "center",
  },
];

// 表格数据
const tableData = ref([]);

// 假设从后端获取数据的方法
const fetchData = (data) => {
  // 处理数据，为每行添加 measureChecked
  const processedData = data.map((item, index) => ({
    ...item,
    key: index,
    index,
    measureChecked: Array(item.controlMeasures.length).fill(false),
    // 如果需要，也可以初始化 statusChecked
    statusChecked: Array(item.implementationStatus.length).fill(false),
  }));

  tableData.value = processedData;
};

// 使用示例
const mockData = [
  {
    riskType: "安全风险",
    riskPoint: "高空作业",
    riskConsequence: "人员伤亡",
    controlRequirement: "必须佩戴安全带",
    controlMeasures: [
      "严格按照规程步骤执行，开口进行防异物封堵，回装前进行异物检查",
    ],
    implementationStatus: ["措施落实在检修规程中"],
  },
  {
    riskType: "质量风险",
    riskPoint: "设备检修",
    riskConsequence: "设备损坏",
    controlRequirement: "按照检修规程执行",
    controlMeasures: ["执行工作前技术交底", "专业人员现场指导"],
    implementationStatus: ["已完成技术交底", "已安排专业人员"],
  },
  {
    riskType: "环境风险",
    riskPoint: "油品泄漏",
    riskConsequence: "环境污染",
    controlRequirement: "做好防泄漏措施",
    controlMeasures: ["检查密封情况", "准备防泄漏工具", "配备吸油材料"],
    implementationStatus: ["已检查完成", "工具已到位", "材料已准备"],
  },
  {
    riskType: "进度风险",
    riskPoint: "工期延误",
    riskConsequence: "影响生产计划",
    controlRequirement: "合理安排工序",
    controlMeasures: ["制定详细施工计划", "关键路径管控"],
    implementationStatus: ["计划已编制", "重点监控中"],
  },
  {
    riskType: "技术风险",
    riskPoint: "新设备安装",
    riskConsequence: "设备运行不稳定",
    controlRequirement: "严格执行安装规范",
    controlMeasures: ["技术人员现场指导", "分步骤验收确认", "试运行测试"],
    implementationStatus: ["技术人员已到位", "验收表已准备", "测试方案已制定"],
  },
];
fetchData(mockData);
// 新增弹框ref
const addModalRef = ref(null);
// 方法定义
const handleAdd = () => {
  addModalRef.value.openModal();
};
const handleImport = () => {
  console.log("导入");
};
const handleDelete = () => {
  console.log("删除", selectedRowKeys.value);
};
// 保存
const handleSave = () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning("请先选择要保存的数据");
    return;
  }
  // 获取选中的行数据
  const selectedData = tableData.value
    .filter((item) => selectedRowKeys.value.includes(item.key))
    .map((item) => ({
      key: item.key,
      index: item.index,
      riskType: item.riskType,
      riskPoint: item.riskPoint,
      riskConsequence: item.riskConsequence,
      controlRequirement: item.controlRequirement,
      controlMeasures: item.controlMeasures,
      implementationStatus: item.implementationStatus,
    }));

  console.log("保存的数据：", selectedData);
  message.success("保存成功");
};
// 编辑
const handleEdit = () => {
  console.log("编辑");
};
const onSelectChange = (keys) => {
  selectedRowKeys.value = keys;
};
// 添加管控措施
const handleAddMeasure = () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning("请先选择要操作的数据");
    return;
  }
  tableData.value = tableData.value.map((item) => {
    // 只对选中的行添加新的管控措施
    if (selectedRowKeys.value.includes(item.key)) {
      return {
        ...item,
        controlMeasures: [
          ...item.controlMeasures,
          "", // 空字符串，允许用户输入
        ],
        measureChecked: [...item.measureChecked, false],
        implementationStatus: [
          ...item.implementationStatus,
          "", // 空字符串，允许用户输入
        ],
      };
    }
    return item;
  });
};

// 移除选中的管控措施
const handleRemoveMeasure = () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning("请先选择要操作的数据");
    return;
  }

  tableData.value = tableData.value.map((item) => {
    // 只对选中的行移除管控措施
    if (selectedRowKeys.value.includes(item.key)) {
      const newControlMeasures = [];
      const newImplementationStatus = [];
      const newMeasureChecked = [];

      item.controlMeasures.forEach((measure, index) => {
        if (!item.measureChecked[index]) {
          newControlMeasures.push(measure);
          newMeasureChecked.push(false);
          newImplementationStatus.push(item.implementationStatus[index]);
        }
      });

      return {
        ...item,
        controlMeasures: newControlMeasures.length ? newControlMeasures : [""],
        measureChecked: newMeasureChecked.length ? newMeasureChecked : [false],
        implementationStatus: newImplementationStatus.length
          ? newImplementationStatus
          : [""],
      };
    }
    return item;
  });
};
// 添加 popover 控制变量
const showPopover = ref(false);
const hoverIndex = ref(null);

// 鼠标移入事件处理
const handleMouseEnter = (key) => {
  showPopover.value = true;
  hoverIndex.value = key;
};

// 鼠标移出事件处理
const handleMouseLeave = () => {
  showPopover.value = false;
  hoverIndex.value = null;
};
</script>

<style scoped lang="scss">
.risk-container {
  padding: 16px;
}

.operation-section {
  margin-bottom: 16px;
  display: flex;
  align-items: center;
}

/* 自定义表格样式 */
:deep(.custom-table) {
  /* 表头样式 */
  .ant-table-thead > tr > th {
    background-color: #5b9bd5 !important;
    color: white !important;
    text-align: center !important;
    height: 50px !important;
    padding: 0 !important;
    line-height: 50px !important;
    border-radius: 0 !important; /* 移除圆角 */
  }

  /* 移除表头上方的线 */
  .ant-table-thead > tr > th::before {
    display: none !important;
  }

  /* 表格内容背景色 */
  .ant-table-tbody > tr > td {
    background-color: #bdd7ee !important;
    padding: 0 !important;
  }

  /* 表格边框颜色 */
  .ant-table-cell {
    border-right: 1px solid white !important;
    border-bottom: 1px solid white !important;
  }

  /* 去除表格hover效果 */
  .ant-table-tbody > tr:hover > td {
    background: #bdd7ee !important;
  }
}

.icon-button {
  margin-left: 8px;
  font-size: 16px;
  cursor: pointer;
}

.icon-button:hover {
  color: #1890ff;
}

.measure-item {
  display: flex;
  align-items: center;
  border-bottom: 1px solid white;
  min-height: 32px;
}

/* 移除第一个和最后一个 measure-item 的边框 */
.measure-item:first-child {
  border-top: none;
}

.measure-item:last-child {
  border-bottom: none;
}

.checkbox-container {
  width: 40px;
  min-width: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-right: 1px solid white;
  height: 32px;
}

.measure-input {
  flex: 1;
}

/* 普通单元格的输入框样式 - 居中对齐 */
:deep(.ant-table-cell) .ant-input {
  text-align: center;
  background-color: transparent !important;
  height: 32px;
}

/* 管控措施和措施落实情况的输入框样式 - 左对齐 */
:deep(.ant-table-cell) .measure-item .ant-input {
  text-align: left;
  padding-left: 8px; /* 添加左侧内边距，避免文字贴边 */
}

/* 移除之前的全局输入框样式 */
:deep(.ant-input) {
  background-color: transparent !important;
  height: 32px;
}

:deep(.ant-table-cell) {
  padding: 0 !important;
}

:deep(.ant-checkbox-wrapper) {
  margin: 0;
}

:deep(.ant-popover-inner-content) {
  max-height: 300px;
  overflow: auto;
  word-break: break-all;
  white-space: pre-wrap;
}

/* 设置复选框为圆形 */
:deep(.ant-checkbox) {
  border-radius: 50% !important;

  /* 未选中状态 */
  .ant-checkbox-inner {
    border-radius: 50% !important;
    width: 16px !important;
    height: 16px !important;
  }

  /* 选中状态 */
  &.ant-checkbox-checked .ant-checkbox-inner {
    background-color: #1890ff !important;
    border-color: #1890ff !important;
    border-radius: 50% !important;

    /* 调整对勾的位置和大小 */
    &::after {
      top: 45% !important;
      left: 22% !important;
      width: 5px !important;
      height: 9px !important;
    }
  }
}
</style>
