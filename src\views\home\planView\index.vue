<template>
  <div class="box">
    <div class="planBox">
      <plan1 />
      <containerBox
        width="27%"
        height="100%"
        name="大修类型/次数"
        componentsName="Stacking"
      />
      <containerBox
        width="27%"
        height="100%"
        name="大修类型/次数"
        componentsName="plan2"
      />
      <containerBox
        width="27%"
        height="100%"
        name="大修类型/次数"
        componentsName="plan2"
      />
    </div>
    <div class="planbottomBox">
      <containerBox
        width="49%"
        height="98%"
        name="大修类型/次数"
        componentsName="plan2"
      />
      <containerBox
        width="49%"
        height="98%"
        name="大修类型/次数"
        componentsName="plan2"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import plan2 from "@/components/plan2.vue";
import plan1 from "@/views/home/<USER>/plan1.vue";
import containerBox from "@/views/home/<USER>/containerBox.vue";
</script>

<style scoped lang="scss">
.box {
  width: 100%;
  height: 100%;
}
.planBox {
  width: 100%;
  height: 40%;
  display: flex;
  margin-bottom: 15px;
  justify-content: space-between;
  /* background-color: #fff; */
}

.planbottomBox {
  width: 100%;
  height: 60%;
  display: flex;
  justify-content: space-between;
}
</style>
