<template>
  <a-modal
    v-model:visible="visible"
    title="新增数据"
    @ok="handleAdd"
    @cancel="handleCancel"
    :maskClosable="false"
    width="800px"
  >
    <a-form :model="formData" layout="vertical">
      <a-form-item label="经验反馈主题" required>
        <a-input
          v-model:value="formData.subject"
          placeholder="请输入经验反馈主题"
        />
      </a-form-item>
      <a-form-item label="概述" required>
        <a-input v-model:value="formData.summary" placeholder="请输入概述" />
      </a-form-item>

      <!-- 管控措施和措施落实情况 -->
      <div class="measures-container">
        <div class="measures-header">
          <div class="measure-title">管控措施</div>
          <div class="measure-title">措施落实情况</div>
          <div style="width: 80px"></div>
        </div>

        <div
          v-for="(_, index) in formData.controlMeasures"
          :key="index"
          class="measure-row"
        >
          <a-input
            v-model:value="formData.controlMeasures[index]"
            placeholder="请输入管控措施"
            class="measure-input"
          />
          <a-input
            v-model:value="formData.implementationStatus[index]"
            placeholder="请输入措施落实情况"
            class="measure-input"
          />
          <div class="measure-actions">
            <a-button
              type="link"
              danger
              @click="removeMeasure(index)"
              v-if="formData.controlMeasures.length > 1"
            >
              删除
            </a-button>
          </div>
        </div>

        <a-button
          type="dashed"
          block
          @click="addMeasure"
          class="add-measure-btn"
        >
          <plus-outlined />添加管控措施
        </a-button>
      </div>
    </a-form>
  </a-modal>
</template>

<script setup>
import { ref, defineExpose } from "vue";
import { PlusOutlined } from "@ant-design/icons-vue";

const visible = ref(false);
const formData = ref({
  subject: "",
  summary: "",
  controlMeasures: [""],
  implementationStatus: [""],
});

const emit = defineEmits(["add"]);

const show = () => {
  formData.value = {
    subject: "",
    summary: "",
    controlMeasures: [""],
    implementationStatus: [""],
  };
  visible.value = true;
};

const hide = () => {
  visible.value = false;
};

const addMeasure = () => {
  formData.value.controlMeasures.push("");
  formData.value.implementationStatus.push("");
};

const removeMeasure = (index) => {
  formData.value.controlMeasures.splice(index, 1);
  formData.value.implementationStatus.splice(index, 1);
};

const handleAdd = () => {
  if (!formData.value.subject.trim()) {
    return message.warning("请输入经验反馈主题");
  }
  if (!formData.value.summary.trim()) {
    return message.warning("请输入概述");
  }

  emit("add", {
    subject: formData.value.subject,
    summary: formData.value.summary,
    controlMeasures: formData.value.controlMeasures,
    implementationStatus: formData.value.implementationStatus,
  });
  hide();
};

const handleCancel = () => {
  hide();
};

defineExpose({
  show,
  hide,
});
</script>

<style scoped lang="scss">
.measures-container {
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  padding: 16px;
  margin-top: 16px;
}

.measures-header {
  display: flex;
  margin-bottom: 8px;

  .measure-title {
    flex: 1;
    font-weight: bold;
    color: #000000d9;
  }
}

.measure-row {
  display: flex;
  margin-bottom: 8px;
  align-items: flex-start;

  .measure-input {
    flex: 1;
    margin-right: 8px;
  }

  .measure-actions {
    width: 80px;
    text-align: center;
    padding-top: 4px;
  }
}

.add-measure-btn {
  margin-top: 8px;
}

:deep(.ant-form-item) {
  margin-bottom: 16px;
}
</style>
