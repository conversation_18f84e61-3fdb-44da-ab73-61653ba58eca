<template>
  <div class="coordination-work-section">
    <div class="section-title">
      <span class="title-icon"></span>
      机械明日配合工作
    </div>
    <div class="table-wrapper">
      <el-table
        :data="coordinationWorkData"
        border
        stripe
        style="width: 100%"
        :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
      >
        <el-table-column prop="workOrderNo" label="工单号" width="120" />
        <el-table-column prop="taskNo" label="任务号" width="120" />
        <el-table-column prop="title" label="工单任务标题" min-width="200" />
        <el-table-column
          prop="responsiblePerson"
          label="工作负责人姓名"
          width="120"
        />
        <el-table-column prop="plannedStartDate" label="计划开工" width="120" />
        <el-table-column prop="plannedEndDate" label="计划完工" width="120" />
        <el-table-column prop="department" label="专业" width="120" />
        <el-table-column prop="coordinationTeam" label="配合班组" width="120" />
        <el-table-column prop="remark" label="备注" min-width="150" />
      </el-table>
    </div>
  </div>
</template>

<script>
export default {
  name: "CoordinationWorkTable",
  props: {
    queryParams: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      coordinationWorkData: [
        {
          workOrderNo: "WO-2023-0030",
          taskNo: "T-0060",
          title: "5号机组凝汽器真空泵检修",
          responsiblePerson: "张工",
          plannedStartDate: "2023-10-22",
          plannedEndDate: "2023-10-25",
          department: "电气",
          coordinationTeam: "机械二班",
          remark: "需配合电气检修，确保设备隔离",
        },
        {
          workOrderNo: "WO-2023-0031",
          taskNo: "T-0061",
          title: "2号锅炉引风机联轴器更换",
          responsiblePerson: "李工",
          plannedStartDate: "2023-10-22",
          plannedEndDate: "2023-10-24",
          department: "热控",
          coordinationTeam: "机械一班",
          remark: "需配合热控专业调试",
        },
        {
          workOrderNo: "WO-2023-0032",
          taskNo: "T-0062",
          title: "6号机组汽轮机调节系统检修",
          responsiblePerson: "王工",
          plannedStartDate: "2023-10-22",
          plannedEndDate: "2023-10-28",
          department: "热控",
          coordinationTeam: "机械三班",
          remark: "配合调节阀门检修",
        },
        {
          workOrderNo: "WO-2023-0033",
          taskNo: "T-0063",
          title: "7号机组发电机定子冷却水系统改造",
          responsiblePerson: "刘工",
          plannedStartDate: "2023-10-22",
          plannedEndDate: "2023-10-30",
          department: "电气",
          coordinationTeam: "机械二班",
          remark: "需提前准备材料，配合电气工作",
        },
        {
          workOrderNo: "WO-2023-0034",
          taskNo: "T-0064",
          title: "3号锅炉过热器管路修复",
          responsiblePerson: "赵工",
          plannedStartDate: "2023-10-22",
          plannedEndDate: "2023-10-26",
          department: "锅炉",
          coordinationTeam: "机械一班",
          remark: "紧急修复任务，优先安排",
        },
      ],
    };
  },
  mounted() {},
  watch: {
    queryParams: {
      handler(newVal) {
        console.log("机械明日配合工作组件收到查询参数:", newVal);

        this.handleQueryParamsChange(newVal);
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    // 处理查询参数变化
    handleQueryParamsChange(params) {
      console.log(`${this.$options.name}组件处理查询参数:`, params);
    },
  },
};
</script>

<style scoped>
/* 配合工作区域样式 */
.coordination-work-section {
  margin-bottom: 30px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  width: 100%;
}

.table-wrapper {
  padding: 15px;
  overflow-x: auto;
}

/* 标题样式 */
.section-title {
  padding: 15px 20px;
  font-size: 15px;
  color: #666;
  font-weight: 500;
  display: flex;
  align-items: center;
  background: linear-gradient(to right, #f8f9fc, #ffffff);
  border-left: 3px solid #1890ff;
  letter-spacing: 0.5px;
  margin-bottom: 0;
  box-shadow: 0 1px 0 rgba(0, 0, 0, 0.05);
}

.title-icon {
  display: inline-block;
  width: 4px;
  height: 16px;
  margin-right: 8px;
  background-color: #1890ff;
  border-radius: 2px;
}
</style>
