<template>
  <div class="area8-container">
    <div class="area8-main-title">
      <span class="title-text">合同准备</span>
      <span class="completion-rate">立项签订率：156</span>
    </div>
    <div class="content-area">
      <!-- 左侧饼图 -->
      <div class="chart-container">
        <div id="pieChartArea8" class="pie-chart"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import * as echarts from 'echarts/core';
import { PieChart } from 'echarts/charts';
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
} from 'echarts/components';
import { LabelLayout } from 'echarts/features';
import { CanvasRenderer } from 'echarts/renderers';

// 注册必要的ECharts组件
echarts.use([
  PieChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  LabelLayout,
  CanvasRenderer
]);

// 新的饼图数据 for Area8
const rawPieDataArea8 = [
  { name: '待立项', value: 250, color: '#4b9cfa' }, 
  { name: '合同已签订', value: 350, color: '#89d9ad' },    
  { name: '待审批', value: 180, color: '#6e68f7' }, 
  { name: '合同未签订', value: 220, color: '#ebbe39' } 
];

// 计算总数 for Area8
const totalArea8 = rawPieDataArea8.reduce((sum, item) => sum + item.value, 0);

// 处理后的饼图数据，包含百分比
const pieData = rawPieDataArea8.map(item => ({
  ...item,
  percentage: totalArea8 === 0 ? '0%' : ((item.value / totalArea8) * 100).toFixed(0) + '%'
}));

// 当前悬停项目 (使用新的pieData)
const currentHoverItem = ref(pieData[0] || {}); // Ensure pieData[0] exists

// 饼图实例引用
const chartInstance = ref(null);

// 初始化饼图
const initChart = () => {
  const chartDom = document.getElementById('pieChartArea8');
  if (!chartDom) return;
  
  // 初始化ECharts实例
  chartInstance.value = echarts.init(chartDom);
  
  // 配置项
  const option = {
    backgroundColor: 'transparent',
    color: pieData.map(item => item.color),
    
    legend: {
      orient: 'vertical',
      right: '5%',
      top: 'center',
      itemWidth: 15,
      itemHeight: 10,
      icon: 'circle',
      textStyle: {
        color: '#fff',
        fontSize: 12
      },
      formatter: function(name) {
        const item = pieData.find(pItem => pItem.name === name);
        if (item) {
          return `${name}: ${item.value} (${item.percentage})`;
        }
        return name;
      }
    },
    series: [
      {
        name: '合同状态分析',
        type: 'pie',
        radius: ['45%', '90%'],
        center: ['35%', '50%'],
        avoidLabelOverlap: true, 
        itemStyle: {
          borderRadius: 2,
          borderColor: '#192347',
          borderWidth: 1.5
        },
        emphasis: {
          scale: false, 
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        },
        label: {
          show: true,
          formatter: function(params) {
            const item = pieData.find(pItem => pItem.name === params.name);
            if (item) {
                return [
                `{name|${item.name}}`,
                `{value|${item.value} (${item.percentage})}`
                ].join('\n');
            }
            return params.name;
          },
          backgroundColor: 'transparent'
        },
        data: pieData.map(item => ({
          value: item.value,
          name: item.name,
          itemStyle: {
            color: item.color
          },
          label: {
            rich: {
              name: { color: item.color },
              value: { color: item.color }
            }
          },
          labelLine: {
            lineStyle: {
              color: item.color,
              opacity: 0.7
            }
          }
        }))
      }
    ],
  };
  
  chartInstance.value.setOption(option);
  
  chartInstance.value.on('mouseover', 'series', (params) => {
    if (params.seriesType === 'pie') {
      const hoverData = pieData.find(pItem => pItem.name === params.name);
      if (hoverData) {
        currentHoverItem.value = hoverData;
      }
    }
  });
};

const handleResize = () => {
  if (chartInstance.value) {
    chartInstance.value.resize();
  }
};

onMounted(() => {
  initChart();
  window.addEventListener('resize', handleResize);
});
onUnmounted(() => {
  if (chartInstance.value) {
    chartInstance.value.dispose();
  }
  window.removeEventListener('resize', handleResize);
});
</script>

<style scoped>
.area8-container {
  background-color: #192347; 
  color: #fff;
  height: 100%;
  width: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  border-radius: 8px;
  overflow: hidden;
}

.area8-main-title {
  height: 30px;
  background-color: #61aef9;
  color: #ffffff;
  font-size: 16px;
  font-weight: 500;
  display: flex;
  align-items: center;
  padding-left: 15px;
  text-align: left;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  flex-shrink: 0;
  position: relative;
  justify-content: space-between;
  padding-right: 15px;
}

.area8-main-title::before {
  content: '';
  position: absolute;
  left: 0px;
  top: 5px;
  bottom: 6px;
  width: 4px;
  border-radius: 3px;
  background: linear-gradient(
    to bottom,
    rgba(255, 255, 255, 0.9),
    rgba(255, 255, 255, 0.3) );
}

.completion-rate {
  font-size: 14px;
}

.content-area {
  flex: 1;
  box-sizing: border-box;
  display: flex;
  align-items: stretch; /* 让子元素撑满高度 */
}

.chart-container {
  width: 100%; /* 饼图容器占满全部宽度，ECharts内部会根据center和radius布局 */
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pie-chart {
  width: 100%;
  height: 90%;
  position: relative;
}

/* 移除自定义图例相关样式 */
</style> 