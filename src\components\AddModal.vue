<template>
  <a-modal
    v-model:visible="visible"
    :title="modalType === 'edit' ? '修改' : '新增'"
    @ok="handleOk"
    @cancel="handleCancel"
    :maskClosable="false"
    width="800px"
  >
    <a-form
      :model="formState"
      :label-col="{ flex: '100px' }"
      :wrapper-col="{ flex: 'auto' }"
      :label-align="'left'"
    >
      <a-form-item
        label="专项岗位"
        name="position"
        :rules="[{ required: true, message: '请输入专项岗位!' }]"
      >
        <a-input
          v-model:value="formState.position"
          placeholder="请输入专项岗位"
        />
      </a-form-item>

      <a-form-item
        label="姓名"
        name="name"
        :rules="[{ required: true, message: '请输入姓名!' }]"
        class="name-item"
      >
        <name-select
          v-model:value="formState.name"
          @select="handleNameSelect"
        />
      </a-form-item>

      <a-form-item
        label="职工号"
        name="employeeId"
        :rules="[{ required: true, message: '请输入职工号!' }]"
      >
        <a-input
          v-model:value="formState.employeeId"
          placeholder="请输入职工号"
        />
      </a-form-item>

      <a-form-item
        label="处室"
        name="department"
        :rules="[{ required: true, message: '请输入处室!' }]"
      >
        <a-input
          v-model:value="formState.department"
          placeholder="请输入处室"
        />
      </a-form-item>

      <a-form-item
        label="所属单位"
        name="unit"
        :rules="[{ required: true, message: '请输入所属单位!' }]"
      >
        <a-input v-model:value="formState.unit" placeholder="请输入所属单位" />
      </a-form-item>

      <a-form-item
        label="授权级别"
        name="authLevel"
        :rules="[{ required: true, message: '请输入授权级别!' }]"
      >
        <a-input
          v-model:value="formState.authLevel"
          placeholder="请输入授权级别"
        />
      </a-form-item>

      <a-form-item
        label="有效期"
        name="validPeriod"
        :rules="[{ required: true, message: '请选择有效期!' }]"
      >
        <a-date-picker
          v-model:value="formState.validPeriod"
          style="width: 100%"
          placeholder="请选择有效期"
          format="YYYY-MM-DD"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { ref, reactive } from "vue";
import { message } from "ant-design-vue";
import dayjs from "dayjs";
import NameSelect from "./NameSelect.vue";

const visible = ref(false);
const modalType = ref("add"); // 模态框类型：add/edit

const formState = reactive({
  position: "",
  name: "",
  employeeId: "",
  department: "",
  unit: "",
  authLevel: "",
  validPeriod: null,
  id: null, // 添加 id 字段
});

// 显示弹窗
const showModal = (options) => {
  // 确保 options 存在
  if (!options) {
    options = { type: "add" };
  }

  console.log("接收到的数据:", options);

  visible.value = true;
  modalType.value = options.type || "add";

  // 重置表单
  Object.keys(formState).forEach((key) => {
    formState[key] = null;
  });

  // 如果是编辑模式且有数据
  if (options.type === "edit" && options.data) {
    const editData = options.data;
    console.log("编辑数据:", editData);

    // 逐个赋值
    formState.id = editData.id;
    formState.position = editData.position;
    formState.name = editData.name;
    formState.employeeId = editData.employeeId;
    formState.department = editData.department;
    formState.unit = editData.unit;
    formState.authLevel = editData.authLevel;
    // 使用 dayjs 处理日期
    formState.validPeriod = editData.validPeriod
      ? dayjs(editData.validPeriod)
      : null;
  }
};

// 确定
const handleOk = () => {
  try {
    // 验证所有必填字段
    if (!formState.position) return message.warning("请输入专项岗位");
    if (!formState.name) return message.warning("请输入姓名");
    if (!formState.employeeId) return message.warning("请输入职工号");
    if (!formState.department) return message.warning("请输入处室");
    if (!formState.unit) return message.warning("请输入所属单位");
    if (!formState.authLevel) return message.warning("请输入授权级别");
    if (!formState.validPeriod) return message.warning("请选择有效期");

    console.log("保存的数据：", formState); // 调试日志
    console.log("操作类型：", modalType.value);

    handleCancel();
    message.success(`${modalType.value === "edit" ? "修改" : "新增"}成功`);
  } catch (error) {
    console.error("handleOk error:", error);
    message.error("保存失败");
  }
};

// 取消
const handleCancel = () => {
  try {
    visible.value = false;
    modalType.value = "add";
    // 重置表单
    Object.keys(formState).forEach((key) => {
      formState[key] = null;
    });
  } catch (error) {
    console.error("handleCancel error:", error);
    message.error("取消操作失败");
  }
};

// 处理人员选择
const handleNameSelect = (row) => {
  console.log(row);
  // 自动填充其他字段
  formState.department = row.section;
  formState.employeeId = row.staffAccount;
  formState.unit = row.unit;
  formState.name = row.name;
};

defineExpose({
  showModal,
});
</script>

<style scoped>
.ant-form-item {
  margin-bottom: 16px;
}

:deep(.ant-form-item-label) {
  text-align: left;
  min-width: 100px;
}

:deep(.name-item .ant-form-item-control-input-content) {
  display: flex;
  align-items: center;
}
</style>
