<template>
  <div class="area11-container">
    <div class="area11-main-title">
      <span class="title-text">承诺项工单准备</span>
      <span class="completion-rate">跟踪中承诺项数：111</span>
    </div>
    <div class="content-area">
      <div class="chart-container">
        <div id="barLineChartArea11" class="bar-chart"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import * as echarts from 'echarts/core';

const chartInstance = ref(null);

// 五个分类及其数据
const categories = ['机械维修处', '电气维修处', '仪控维修处', '技术支持处', '工程管理处'];
const barDataTotal = [80, 90, 70, 55, 25];      // 总工单数量 - 蓝色
const barDataCompleted = [55, 60, 40, 30, 10];  // 完成准备工单数 - 绿色
const lineDataProjects = [55, 50, 45, 58, 40];   // 项目数量 - 黄色

// 渐变色配置
const totalBarGradient = {
  type: 'linear',
  x: 0, y: 0, x2: 0, y2: 1,
  colorStops: [
    { offset: 0, color: '#4a9af6' },      // 蓝色顶部
    { offset: 1, color: 'rgba(74, 154, 246, 0.3)' }  // 蓝色底部（透明度降低）
  ]
};

const completedBarGradient = {
  type: 'linear',
  x: 0, y: 0, x2: 0, y2: 1,
  colorStops: [
    { offset: 0, color: '#7ec8a3' },      // 绿色顶部
    { offset: 1, color: 'rgba(126, 200, 163, 0.3)' }  // 绿色底部（透明度降低）
  ]
};

const initChart = () => {
  const chartDom = document.getElementById('barLineChartArea11');
  if (!chartDom) return;
  chartInstance.value = echarts.init(chartDom);

  const option = {
    backgroundColor: 'transparent',
    legend: {
      data: ['总工单数量', '完成准备工单数', '项目数量'],
      textStyle: { color: '#ffffff' },
      itemWidth: 10,
      itemHeight: 10,
      icon: 'circle',
      top: 0,
      selectedMode: true
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '12%',
      containLabel: true
    },
 
    xAxis: [
      {
        type: 'category',
        data: categories,
        axisPointer: {
          type: 'shadow'
        },
        axisLine: { lineStyle: { color: '#6e7079' } },
        axisLabel: { color: '#ffffff', fontSize: 10, interval: 0, rotate: 0 }
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: '',
        min: 0,
        max: 100,
        interval: 20,
        axisLabel: {
          formatter: '{value}',
          color: '#ffffff'
        },
        splitLine: { lineStyle: { type: 'dashed', color: '#3a3f5a' } },
        axisLine: { show: false }
      },
      {
        type: 'value',
        name: '',
        show: false,
        interval: 20,
        axisLabel: {
          formatter: '{value}',
          color: '#ffffff'
        },
        splitLine: { show: false },
        axisLine: { show: false }
      }
    ],
    series: [
      {
        name: '总工单数量',
        type: 'bar',
        barWidth: '25%',
        barGap: '0%',
        itemStyle: {
          color: totalBarGradient,
          borderRadius: [5, 5, 0, 0]  // 左上角、右上角、右下角、左下角
        },
        data: barDataTotal,
        // 在柱子上显示数值
        label: {
          show: false
        },
        z: 2
      },
      {
        name: '完成准备工单数',
        type: 'bar',
        barWidth: '25%',
        barGap: '0%',
        itemStyle: {
          color: completedBarGradient,
          borderRadius: [5, 5, 0, 0]  // 左上角、右上角、右下角、左下角
        },
        data: barDataCompleted,
        // 在柱子上显示数值
        label: {
          show: false
        },
        z: 2
      },
      {
        name: '项目数量',
        type: 'line',
        yAxisIndex: 1,
        symbol: 'circle',
        symbolSize: 8,
        itemStyle: {
          color: '#e8bb38' // 黄色
        },
        lineStyle: {
          width: 2,
          color: '#e8bb38'
        },
        data: lineDataProjects,
    
      }
    ]
  };
  
  chartInstance.value.setOption(option);
};

const handleResize = () => {
  if (chartInstance.value) {
    chartInstance.value.resize();
  }
};

onMounted(() => {
  initChart();
  window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
  if (chartInstance.value) {
    chartInstance.value.dispose();
  }
  window.removeEventListener('resize', handleResize);
});

</script>

<style scoped>
.area11-container {
  background-color: #192347;
  color: #fff;
  height: 100%;
  width: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  border-radius: 8px;
  overflow: hidden;
}

.area11-main-title {
  height: 30px;
  background-color: #61aef9;
  color: #ffffff;
  font-size: 16px;
  font-weight: 500;
  display: flex;
  align-items: center;
  padding-left: 15px;
  padding-right: 15px;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  flex-shrink: 0;
  position: relative;
  justify-content: space-between;
}

.area11-main-title::before {
  content: '';
  position: absolute;
  left: 0px;
  top: 5px;
  bottom: 6px;
  width: 4px;
  border-radius: 3px;
  background: linear-gradient(
    to bottom,
    rgba(255, 255, 255, 0.9),
    rgba(255, 255, 255, 0.3)
  );
}

.completion-rate {
  font-size: 14px;
  font-weight: normal;
}

.content-area {
  flex: 1;
  box-sizing: border-box;
  display: flex;
  align-items: stretch;
}

.chart-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.bar-chart {
  width: 100%;
  height: 90%;
  position: relative;
}
</style> 