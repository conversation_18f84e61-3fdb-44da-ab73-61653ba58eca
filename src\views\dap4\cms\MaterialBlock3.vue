<template>
  <div class="material-block material-block-2">
    <div class="title-section">
      <div class="title-icon">🔧</div>
      <h3>物资配置模块3</h3>
      <div class="title-icon">🛠️</div>
    </div>

    <!-- 统计区域 -->
    <div class="stats-section">
      <div class="stat-item">
        <span class="stat-label">累计调配物资行数</span>
        <span class="stat-value">{{ materialLineCount }}</span>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="chart-section">
      <!-- 图例区域 -->
      <div class="legend-container">
        <div class="legend-item">
          <div class="legend-dot legend-dot-in"></div>
          <span class="legend-text">调入</span>
        </div>
        <div class="legend-item">
          <div class="legend-dot legend-dot-out"></div>
          <span class="legend-text">调出</span>
        </div>
      </div>

      <div class="progress-container">
        <div
          v-for="(item, index) in chartData.categories"
          :key="index"
          class="progress-row"
        >
          <!-- 地点名称 -->
          <div class="location-label">
            <span style="z-index: 2">{{ item }}</span>
          </div>

          <!-- 双向进度条 -->
          <div class="bidirectional-progress">
            <!-- 左侧调入进度条 -->
            <div class="progress-left">
              <div class="progress-wrapper">
                <a-progress
                  :percent="getLeftPercent(index)"
                  :show-info="false"
                  stroke-color="#1293ea"
                  trail-color="transparent"
                  :stroke-width="8"
                  class="left-progress"
                />
                <div
                  class="progress-end-dot left-dot"
                  :style="{ right: getLeftPercent(index) + '%' }"
                >
                  <span class="dot-value left-value">{{
                    chartData.transferIn[index]
                  }}</span>
                </div>
              </div>
            </div>

            <!-- 中间分界线 -->
            <div class="center-line"></div>

            <!-- 右侧调出进度条 -->
            <div class="progress-right">
              <div class="progress-wrapper">
                <a-progress
                  :percent="getRightPercent(index)"
                  :show-info="false"
                  stroke-color="#70d2ac"
                  trail-color="transparent"
                  :stroke-width="8"
                  class="right-progress"
                />
                <div
                  class="progress-end-dot right-dot"
                  :style="{ left: getRightPercent(index) - 5 + '%' }"
                >
                  <span class="dot-value right-value">{{
                    chartData.transferOut[index]
                  }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from "vue";

// 模拟数据
const materialLineCount = ref(8680);

// 图表数据
const chartData = reactive({
  categories: ["黄山", "田湾", "福清", "海南", "三门", "漳州"],
  transferIn: [0, 12, 36, 37, 40, 20], // 调入数据（正值）
  transferOut: [20, 210, 34.41, 37, 20, 21], // 调出数据（正值，显示在右侧）
});

// 计算最大值用于缩放
const maxValue = computed(() => {
  const maxIn = Math.max(...chartData.transferIn);
  const maxOut = Math.max(...chartData.transferOut);
  return Math.max(maxIn, maxOut);
});

// 计算缩放后的数据，确保图表显示合理
const scaledData = computed(() => {
  const scale = 50 / maxValue.value; // 最大显示长度为50
  return {
    transferIn: chartData.transferIn.map((val) => -val * scale),
    transferOut: chartData.transferOut.map((val) => val * scale),
  };
});

// 计算左侧（调入）的百分比 - 基于左侧所有数据的总和
const getLeftPercent = (index: number) => {
  const currentValue = chartData.transferIn[index] + 50;
  const totalLeft = chartData.transferIn.reduce((sum, val) => sum + val, 0);
  return totalLeft > 0 ? (currentValue / totalLeft) * 100 : 0;
};

// 计算右侧（调出）的百分比 - 基于右侧所有数据的总和
const getRightPercent = (index: number) => {
  const currentValue = chartData.transferOut[index] + 50;
  const totalRight = chartData.transferOut.reduce((sum, val) => sum + val, 0);
  return totalRight > 0 ? (currentValue / totalRight) * 100 : 0;
};
</script>

<style scoped>
.material-block {
  background-color: #192347;
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  border-radius: 4px;
  color: white;
  box-sizing: border-box;
}

.material-block-2 {
  border-color: #4ecdc4; /* 青色边框 */
}

.title-section {
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 4px;
}

.title-icon {
  font-size: 16px;
  color: #37b1fe;
  margin: 0 10px;
}

.title-section h3 {
  margin: 0;
  font-size: 16px;
  color: #03d0f4;
}

.stats-section {
  margin: 0 auto 15px auto;
  width: 80%;
  display: flex;
  justify-content: center;
}

.stat-item {
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  border: 1px solid #4ecdc4;
  border-radius: 4px;
  padding: 2px;
  width: 100%;
  overflow: hidden;
}

.stat-label {
  font-size: 16px;
  color: #ccc;
  flex-shrink: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.stat-value {
  font-size: 19px;
  font-weight: bold;
  color: #4ecdc4;
  flex-shrink: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.chart-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0; /* 确保flex子元素可以缩小 */
  border: 1px solid #4ecdc4;
}

/* 图例容器样式 */
.legend-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding-left: 50px;
  gap: 20px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 6px;
}

.legend-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  flex-shrink: 0;
}

.legend-dot-in {
  background-color: #009cff;
}

.legend-dot-out {
  background-color: #61ddaa;
}

.legend-text {
  font-size: 12px;
  color: #ccc;
  font-weight: 500;
}

/* 进度条容器样式 */
.progress-container {
  flex: 1;
  padding: 8px;
  display: flex;
  flex-direction: column;
  gap: 6px; /* 添加行间距 */
  min-height: 0; /* 确保flex子元素可以缩小 */
  overflow: hidden;
}

.progress-row {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1; /* 让每行平均分配高度 */
  min-height: 20px; /* 最小高度 */
}

.location-label {
  width: 40px;
  font-size: 16px;
  color: #ccc;
  text-align: center;
  flex-shrink: 0;
  position: relative;
  padding: 4px 2px;
  z-index: 2; /* 确保文字在色块上面 */
}

/* 地点标签托底渐变色块 */
.location-label::before {
  content: "";
  position: absolute;
  bottom: 5px;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(to right, transparent, #0097ff, transparent);
  border-radius: 2px;
  z-index: 1; /* 色块在文字下面 */
}

/* 双向进度条布局 */
.bidirectional-progress {
  flex: 1;
  display: flex;
  align-items: center;
  position: relative;
  height: 70%; /* 缩小高度，不占满行高 */
  background-color: #131d3e; /* 深蓝色背景 */
  border-radius: 15px; /* 四个角都添加圆角 */
  border: 1px solid #122850; /* 深蓝色边框 */
  padding: 1px; /* 更小的内边距 */
  margin: 0 10px; /* 进一步增加左右边距，确保字体在背景框内 */
}

.progress-left {
  flex: 1;
  display: flex;
  align-items: center;
  margin-right: 2px;
  height: 100%;
}

.progress-right {
  flex: 1;
  display: flex;
  align-items: center;
  margin-left: 2px;
  height: 100%;
}

.progress-wrapper {
  position: relative;
  width: 100%;
  height: 100%; /* 占满父容器高度 */
  display: flex;
  align-items: center;
}

/* 左侧进度条样式 */
.left-progress {
  transform: scaleX(-1); /* 水平翻转，让进度条从右向左填充 */
}

.left-progress :deep(.ant-progress-bg) {
  background: linear-gradient(to right, transparent, #1293ea) !important;
}

.left-progress :deep(.ant-progress-inner) {
  background-color: transparent !important;
  border-radius: 10px !important;
}

/* 右侧进度条样式 */
.right-progress :deep(.ant-progress-bg) {
  background: linear-gradient(to right, transparent, #70d2ac) !important;
}

.right-progress :deep(.ant-progress-inner) {
  background-color: transparent !important;
  border-radius: 10px !important;
}

/* 进度条结束位置的小白圆 */
.progress-end-dot {
  position: absolute;
  width: 10px;
  height: 10px;
  background-color: #fff;
  border-radius: 50%;
  z-index: 10;
  top: 44%;
  transform: translateY(-50%);
}

.left-dot {
  border: 1px solid #1293ea;
}

.right-dot {
  border: 1px solid #70d2ac;
}

/* 小白圆上的数据值显示 */
.dot-value {
  position: absolute;
  font-size: 10px;
  color: #fff;
  font-weight: bold;
  white-space: nowrap;
  z-index: 11;
}

.left-value {
  right: 16px;
  top: -1px;
  transform: none;
  max-width: 30px; /* 进一步限制最大宽度 */
  overflow: hidden; /* 超出隐藏 */
  text-overflow: ellipsis; /* 超长显示省略号 */
  font-size: 9px; /* 稍微缩小字体 */
}

.right-value {
  left: 16px;
  top: 0px;
  transform: none;
  max-width: 30px; /* 进一步限制最大宽度 */
  overflow: hidden; /* 超出隐藏 */
  text-overflow: ellipsis; /* 超长显示省略号 */
  font-size: 9px; /* 稍微缩小字体 */
}

/* 中间分界线 */
.center-line {
  width: 2px;
  height: 80%; /* 占行高的80% */
  background-color: #094b9f;
  flex-shrink: 0;
}
</style>
