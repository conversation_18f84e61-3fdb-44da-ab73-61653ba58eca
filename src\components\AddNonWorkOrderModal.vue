<template>
  <a-modal
    :open="isOpen"
    title="新增非工单物资"
    @ok="handleOk"
    @cancel="handleCancel"
    width="800px"
    okText="确定"
    cancelText="取消"
  >
    <a-form
      ref="formRef"
      :model="formState"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 18 }"
    >
      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="物资编码：" name="materialCode">
            <a-input
              v-model:value="formState.materialCode"
              placeholder="请输入物资编码"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="物资名称：" name="materialName">
            <a-input
              v-model:value="formState.materialName"
              placeholder="请输入物资名称"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="数量：" name="quantity">
            <a-input-number
              v-model:value="formState.quantity"
              style="width: 100%"
              placeholder="请输入数量"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="功能/用途：" name="function">
            <a-input
              v-model:value="formState.function"
              placeholder="请输入功能/用途"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="物资准备情况：" name="prepStatus">
            <a-input
              v-model:value="formState.prepStatus"
              placeholder="请输入物资准备情况"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="缺货影响评价：" name="shortageImpact">
            <a-input
              v-model:value="formState.shortageImpact"
              placeholder="请输入缺货影响评价"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="缺货应对措施：" name="shortageMeasures">
            <a-input
              v-model:value="formState.shortageMeasures"
              placeholder="请输入缺货应对措施"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="准备情况判断：" name="prepJudgment">
            <a-input
              v-model:value="formState.prepJudgment"
              placeholder="请输入准备情况判断"
            />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </a-modal>
</template>

<script setup>
import { ref, reactive } from "vue";

const isOpen = ref(false);
const formRef = ref(null);

const formState = reactive({
  materialCode: "",
  materialName: "",
  quantity: undefined,
  function: "",
  prepStatus: "",
  shortageImpact: "",
  shortageMeasures: "",
  prepJudgment: "",
});

const handleOk = () => {
  console.log("表单数据：", formState);
  isOpen.value = false;
};

const handleCancel = () => {
  isOpen.value = false;
};

// 暴露方法给父组件
defineExpose({
  openModal: () => {
    isOpen.value = true;
  },
});
</script>
