<template>
  <div class="risk-container">
    <!-- 操作按钮区域 -->
    <div class="operation-section">
      <a-space>
        <a-button type="primary" @click="handleAdd">
          <plus-outlined />
          新增
        </a-button>
        <a-button @click="handleImport">
          <upload-outlined />
          导入
        </a-button>
        <a-button @click="handleDelete" :disabled="!selectedRowKeys.length">
          <delete-outlined />
          删除
        </a-button>
        <a-button @click="handleEdit" :disabled="selectedRowKeys.length !== 1">
          <edit-outlined />
          编辑
        </a-button>
      </a-space>
    </div>

    <!-- 表格 -->
    <a-table
      :columns="columns"
      :data-source="tableData"
      :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
      :pagination="false"
      class="custom-table"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'isShow'">
          <a-switch v-model:checked="record.isShow" />
        </template>

        <template v-if="column.key === 'attachment'">
          <a-popover
            trigger="click"
            placement="right"
            :overlayStyle="{ width: '300px' }"
          >
            <template #content>
              <div class="attachment-list">
                <div v-if="record.attachments && record.attachments.length">
                  <div
                    v-for="(file, index) in record.attachments"
                    :key="index"
                    class="attachment-item"
                  >
                    <paper-clip-outlined />
                    <span class="file-name">{{ file.name }}</span>
                    <a-button
                      type="link"
                      size="small"
                      @click="handleDownload(file)"
                    >
                      下载
                    </a-button>
                  </div>
                </div>
                <div v-else class="no-attachments">暂无附件</div>
              </div>
            </template>
            <div class="attachment-icon">
              <paper-clip-outlined
                :style="{ fontSize: '16px', cursor: 'pointer' }"
              />
              <span v-if="record.attachments?.length" class="attachment-count">
                {{ record.attachments.length }}
              </span>
            </div>
          </a-popover>
        </template>
      </template>
    </a-table>
  </div>
</template>

<script setup>
import { ref } from "vue";
import { message } from "ant-design-vue";
import {
  PlusOutlined,
  DeleteOutlined,
  EditOutlined,
  UploadOutlined,
  PaperClipOutlined,
} from "@ant-design/icons-vue";

// 表格列定义
const columns = [
  {
    title: "序号",
    dataIndex: "index",
    width: 60,
    align: "center",
  },
  {
    title: "行动项来源",
    dataIndex: "source",
    width: 120,
    align: "center",
  },
  {
    title: "行动项描述",
    dataIndex: "description",
    width: 200,
    align: "center",
  },
  {
    title: "责任处室",
    dataIndex: "department",
    width: 120,
    align: "center",
  },
  {
    title: "责任人",
    dataIndex: "responsible",
    width: 100,
    align: "center",
  },
  {
    title: "目标时间",
    dataIndex: "targetDate",
    width: 120,
    align: "center",
  },
  {
    title: "延期时间",
    dataIndex: "delayDate",
    width: 120,
    align: "center",
  },
  {
    title: "完成情况",
    dataIndex: "completion",
    width: 120,
    align: "center",
  },
  {
    title: "是否展示",
    dataIndex: "isShow",
    width: 100,
    align: "center",
  },
  {
    title: "附件",
    key: "attachment",
    width: 80,
    align: "center",
  },
];

// 表格数据
const tableData = ref([
  {
    key: "1",
    index: 1,
    source: "安全检查",
    description: "完成设备安全隐患排查",
    department: "设备部",
    responsible: "张三",
    targetDate: "2024-01-20",
    delayDate: "2024-01-25",
    completion: "进行中",
    isShow: true,

    attachments: [
      { name: "文档1.xlsx", url: "url1" },
      { name: "文档2.docx", url: "url2" },
    ],
  },
  {
    key: "2",
    index: 2,
    source: "例会决议",
    description: "更新安全操作规程",
    department: "安全部",
    responsible: "李四",
    targetDate: "2024-01-15",
    delayDate: "",
    completion: "已完成",
    isShow: true,

    attachments: [],
  },
  {
    key: "3",
    index: 3,
    source: "上级要求",
    description: "组织安全培训",
    department: "培训部",
    responsible: "王五",
    targetDate: "2024-02-01",
    delayDate: "",
    completion: "未开始",
    isShow: false,

    attachments: [],
  },
]);

const selectedRowKeys = ref([]);

// 选择行变化
const onSelectChange = (keys) => {
  selectedRowKeys.value = keys;
};

// 弹窗引用
const actionModalRef = ref(null);

// 修改处理新增方法
const handleAdd = () => {
  actionModalRef.value.openModal();
};

// 处理导入
const handleImport = () => {
  console.log("导入");
};

// 处理删除
const handleDelete = () => {
  if (!selectedRowKeys.value.length) {
    message.warning("请选择要删除的数据");
    return;
  }

  tableData.value = tableData.value.filter(
    (item) => !selectedRowKeys.value.includes(item.key)
  );
  selectedRowKeys.value = [];
  message.success("删除成功");
};

// 修改处理编辑方法
const handleEdit = () => {
  if (selectedRowKeys.value.length !== 1) {
    message.warning("请选择一条数据进行编辑");
    return;
  }

  const record = tableData.value.find(
    (item) => item.key === selectedRowKeys.value[0]
  );
  actionModalRef.value.openModal(record);
};

// 处理文件上传
const handleCustomRequest = ({ file, onSuccess }) => {
  console.log("上传文件:", file);
  // 这里添加实际的文件上传逻辑
  setTimeout(() => {
    onSuccess();
  }, 100);
};

// 处理成功提交的方法
const handleSuccess = () => {
  // 重新加载数据或更新表格
  message.success("操作成功");
};

// 处理文件下载
const handleDownload = (file) => {
  // 实现文件下载逻辑
  console.log("下载文件:", file);
  // 如果有文件URL，可以直接下载
  if (file.url) {
    window.open(file.url);
  }
};
</script>

<style lang="scss" scoped>
.risk-container {
  padding: 16px;
}

.operation-section {
  margin-bottom: 16px;
}

:deep(.custom-table) {
  .ant-table-thead > tr > th {
    background-color: #f5f5f5;
    font-weight: bold;
  }
}

:deep(.ant-upload-list) {
  display: none;
}

.attachment-icon {
  position: relative;
  display: inline-flex;
  align-items: center;
}

.attachment-count {
  position: absolute;
  top: -8px;
  right: -12px;
  background-color: #1890ff;
  color: white;
  border-radius: 10px;
  padding: 0 6px;
  font-size: 12px;
  line-height: 16px;
  min-width: 16px;
  text-align: center;
}

.attachment-list {
  max-height: 300px;
  overflow-y: auto;
}

.attachment-item {
  display: flex;
  align-items: center;
  padding: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.attachment-item:last-child {
  border-bottom: none;
}

.file-name {
  margin: 0 8px;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.no-attachments {
  padding: 16px;
  text-align: center;
  color: #999;
}

:deep(.ant-popover-inner-content) {
  padding: 0;
}

/* 确保图标在表格中垂直居中 */
:deep(.ant-table-cell) {
  vertical-align: middle;
}
</style>
