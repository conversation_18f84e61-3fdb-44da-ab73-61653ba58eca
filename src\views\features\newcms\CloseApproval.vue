<template>
  <div>
    <a-table
      :columns="closeColumns"
      :data-source="closeTableData"
      :pagination="false"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'actions'">
          <a-space>
            <a-button type="primary" size="small" @click="handleCloseApprove(record)">
              批准
            </a-button>
            <a-button type="primary" danger size="small" @click="handleCloseReject(record)">
              回退
            </a-button>
          </a-space>
        </template>
        <template v-if="column.key === 'attachments'">
          <a-popover
            trigger="click"
            placement="left"
            :overlayStyle="{ width: '300px' }"
          >
            <template #content>
              <div class="attachment-list">
                <div v-if="record.attachments && record.attachments.length">
                  <div 
                    v-for="(file, index) in record.attachments" 
                    :key="index"
                    class="attachment-item"
                  >
                    <paper-clip-outlined />
                    <span class="file-name">{{ file }}</span>
                    <a-button 
                      type="link" 
                      size="small" 
                      @click="downloadAttachment({ fileName: file })"
                    >
                      下载
                    </a-button>
                  </div>
                </div>
                <div v-else class="no-attachments">
                  暂无附件
                </div>
              </div>
            </template>
            <a-button type="link">
              <paper-clip-outlined 
                :style="{ fontSize: '16px', cursor: 'pointer' }"
              />
              <span v-if="record.attachments?.length" class="attachment-count">
                {{ record.attachments.length }}
              </span>
            </a-button>
          </a-popover>
        </template>
      </template>
    </a-table>
  </div>
</template>

<script setup>
import { ref } from "vue";
import { PaperClipOutlined } from "@ant-design/icons-vue";
import { message } from "ant-design-vue";

// 关闭审批表格列定义
const closeColumns = [
  {
    title: "责任部门",
    dataIndex: "department",
    key: "department",
  },
  {
    title: "科室",
    dataIndex: "office",
    key: "office",
  },
  {
    title: "协调人",
    dataIndex: "coordinator",
    key: "coordinator",
  },
  {
    title: "处室责任人",
    dataIndex: "officeResponsible",
    key: "officeResponsible",
  },
  {
    title: "申请关闭时间",
    dataIndex: "closeApplyTime",
    key: "closeApplyTime",
  },
  {
    title: "完成情况说明",
    dataIndex: "completionDescription",
    key: "completionDescription",
  },
  {
    title: "关闭说明",
    dataIndex: "closeDescription",
    key: "closeDescription",
  },
  {
    title: "审批",
    key: "actions",
    width: 150,
  },
  {
    title: "附件",
    key: "attachments",
    width: 80,
    align: "center",
  },
];

// 关闭审批表格数据
const closeTableData = ref([
  {
    id: 1,
    department: "技术部",
    office: "研发科",
    coordinator: "张三",
    officeResponsible: "王五",
    closeApplyTime: "2024-03-20",
    completionDescription: "任务已完成",
    closeDescription: "按计划完成所有工作",
    attachments: ["文件1.pdf", "文件2.doc"],
  },
  {
    id: 2,
    department: "运营部",
    office: "运维科",
    coordinator: "赵六",
    officeResponsible: "孙八",
    closeApplyTime: "2024-03-21",
    completionDescription: "任务完成度95%",
    closeDescription: "基本完成目标",
    attachments: ["报告.pdf"],
  },
]);

// 下载附件
const downloadAttachment = (file) => {
  console.log("下载附件", file);
  message.success(`开始下载: ${file.fileName}`);
};

// 处理关闭审批批准
const handleCloseApprove = (record) => {
  console.log("批准关闭", record);
  message.success("已批准关闭申请");
};

// 处理关闭审批回退
const handleCloseReject = (record) => {
  console.log("回退关闭", record);
  message.success("已回退关闭申请");
};
</script>

<style scoped>
:deep(.ant-table-thead > tr > th) {
  text-align: center;
}

:deep(.ant-table-tbody > tr > td) {
  text-align: center;
}

.attachment-list {
  max-height: 300px;
  overflow-y: auto;
}

.attachment-item {
  display: flex;
  align-items: center;
  padding: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.attachment-item:last-child {
  border-bottom: none;
}

.file-name {
  margin: 0 8px;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.no-attachments {
  padding: 16px;
  text-align: center;
  color: #999;
}

.attachment-count {
  position: absolute;
  top: -8px;
  right: -12px;
  background-color: #1890ff;
  color: white;
  border-radius: 10px;
  padding: 0 6px;
  font-size: 12px;
  line-height: 16px;
  min-width: 16px;
  text-align: center;
}

:deep(.ant-popover-inner-content) {
  padding: 0;
}
</style> 