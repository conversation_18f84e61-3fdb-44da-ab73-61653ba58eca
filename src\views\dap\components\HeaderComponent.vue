<template>
  <div class="header-container">
    <!-- 左侧时间信息 -->
    <div class="time-info">
      <span class="time">{{ currentTime }}</span>
      <span class="date">{{ currentDate }}</span>
    </div>
    
    <!-- 中间标题 -->
    <div class="title-container">
      <div class="title-decoration left"></div>
      <h1 class="main-title">大修数智管控平台</h1>
      <div class="title-decoration right"></div>
    </div>
    
    <!-- 右侧登录信息 -->
    <div class="login-info">
      <span class="login-text">进入系统</span>
      <div class="login-dots">
        <span class="dot"></span>
        <span class="dot"></span>
        <span class="dot"></span>
        <span class="dot"></span>
        <span class="dot"></span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';

// 时间和日期
const currentTime = ref('');
const currentDate = ref('');
const weekdays = ['日', '一', '二', '三', '四', '五', '六'];

// 更新时间的函数
const updateDateTime = () => {
  const now = new Date();
  
  // 格式化时间 HH:MM:SS
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  const seconds = String(now.getSeconds()).padStart(2, '0');
  currentTime.value = `${hours}:${minutes}:${seconds}`;
  
  // 格式化日期 YYYY/MM/DD 星期X
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const weekday = weekdays[now.getDay()];
  currentDate.value = `${year}/${month}/${day} 星期${weekday}`;
};

// 定时器引用
let timer = null;

// 组件挂载时启动定时器
onMounted(() => {
  // 立即更新一次
  updateDateTime();
  
  // 设置定时器，每秒更新一次
  timer = setInterval(updateDateTime, 1000);
});

// 组件卸载时清除定时器
onUnmounted(() => {
  if (timer) {
    clearInterval(timer);
    timer = null;
  }
});
</script>

<style scoped>
.header-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  background: linear-gradient(90deg, rgba(26, 35, 126, 0.7) 0%, rgba(21, 101, 192, 0.9) 50%, rgba(26, 35, 126, 0.7) 100%);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  box-sizing: border-box;
}

/* 左侧时间信息 */
.time-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.time {
  font-size: clamp(14px, 2vw, 18px);
  font-weight: bold;
  color: #ffffff;
}

.date {
  font-size: clamp(12px, 1.5vw, 14px);
  color: #b0bec5;
}

/* 中间标题 */
.title-container {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
}

.title-decoration {
  width: clamp(50px, 8vw, 100px);
  height: 2px;
  background: linear-gradient(90deg, transparent, #4fc3f7);
  position: relative;
}

.title-decoration.left {
  margin-right: clamp(10px, 2vw, 20px);
}

.title-decoration.right {
  margin-left: clamp(10px, 2vw, 20px);
  background: linear-gradient(90deg, #4fc3f7, transparent);
}

.title-decoration::before,
.title-decoration::after {
  content: '';
  position: absolute;
  width: 6px;
  height: 6px;
  background-color: #4fc3f7;
  border-radius: 50%;
}

.title-decoration.left::after {
  right: 0;
  top: -2px;
}

.title-decoration.right::before {
  left: 0;
  top: -2px;
}

.main-title {
  font-size: clamp(18px, 3vw, 28px);
  font-weight: bold;
  color: #ffffff;
  text-shadow: 0 0 10px rgba(79, 195, 247, 0.8);
  margin: 0;
  padding: 0;
  white-space: nowrap;
}

/* 右侧登录信息 */
.login-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.login-text {
  font-size: clamp(12px, 1.5vw, 14px);
  color: #b0bec5;
  cursor: pointer;
  transition: color 0.3s;
}

.login-text:hover {
  color: #ffffff;
}

.login-dots {
  display: flex;
  margin-top: 5px;
}

.dot {
  width: 4px;
  height: 4px;
  background-color: #4fc3f7;
  border-radius: 50%;
  margin: 0 2px;
}

/* 媒体查询，确保在不同尺寸下都能正常显示 */
@media (max-width: 768px) {
  .title-decoration {
    width: 40px;
  }
  
  .title-decoration.left {
    margin-right: 8px;
  }
  
  .title-decoration.right {
    margin-left: 8px;
  }
}

@media (max-width: 480px) {
  .header-container {
    padding: 0 10px;
  }
  
  .title-decoration {
    display: none;
  }
}
</style> 