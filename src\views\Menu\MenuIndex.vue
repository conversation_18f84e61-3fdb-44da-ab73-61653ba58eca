<script setup>
import { ref } from "vue";
import { useRouter } from "vue-router";

const router = useRouter();
// 控制当前激活的菜单项
const activeMenu = ref("/features"); // 根据您的需求，设定默认激活菜单的路径

// 处理菜单选择事件
const handleSelect = (key) => {
  // console.log("Selected menu path:", key);
  activeMenu.value = key; // 更新激活菜单
  router.push(activeMenu.value); // 导航到选中的菜单
};

// 路由数据
const routes = [
  // { path: "/home", name: "首页" },
  { path: "/features", name: "群堆大修规划" },
  { path: "/dap", name: "大屏" },
  { path: "/dap4", name: "大屏4" },
  { path: "/dap5", name: "大屏5" },
  { path: "/a", name: "工单选项卡" },
  { path: "/b", name: "物质选项卡" },
  { path: "/c", name: "进展及协调选项卡" },
  { path: "/d", name: "风险管控选项卡" },
  { path: "/f", name: "经验反馈选项卡" },
  { path: "/g", name: "行动项选项卡" },
  { path: "/timeline", name: "项目时间线图" },
  { path: "/visualization", name: "群堆大修准备" },
  { path: "/profile", name: "群堆大修绩效" },
  {
    path: "/settings",
    name: "基地首页",
    children: [
      { path: "user-settings", name: "大修规划" },
      { path: "app-settings", name: "大修准备" },
      {
        path: "single-machine-repair",
        name: "单机组大修准备",
        children: [
          { path: "single-machine-performance", name: "单机组大修绩效" },
        ],
      },
      {
        path: "single-machine-repair-implementation",
        name: "单机租大修实施",
      },
    ],
  },
];
</script>

<template>
  <el-menu
    :default-active="activeMenu"
    @select="handleSelect"
    class="el-menu-vertical-demo"
  >
    <template v-for="route in routes" :key="route.name">
      <el-sub-menu
        v-if="route.children && route.children.length"
        :index="route.path"
      >
        <template #title>
          <span>{{ route.name }}</span>
        </template>
        <template v-for="child in route.children" :key="child.name">
          <el-sub-menu
            v-if="child.children && child.children.length"
            :index="`${route.path}/${child.path}`"
          >
            <template #title>
              <span>{{ child.name }}</span>
            </template>
            <el-menu-item
              v-for="grandChild in child.children"
              :key="grandChild.name"
              :index="`${route.path}/${child.path}/${grandChild.path}`"
            >
              {{ grandChild.name }}
            </el-menu-item>
          </el-sub-menu>
          <el-menu-item v-else :index="`${route.path}/${child.path}`">
            {{ child.name }}
          </el-menu-item>
        </template>
      </el-sub-menu>
      <el-menu-item v-else :index="route.path">
        {{ route.name }}
      </el-menu-item>
    </template>
  </el-menu>
</template>



<style scoped>
.el-menu-vertical-demo {
  width: 200px; /* 设置菜单宽度 */
}
</style>
