<template>
  <a-modal
    v-model:visible="visible"
    title="选择工单"
    @ok="handleOk"
    @cancel="handleCancel"
    :maskClosable="false"
    okText="确定"
    cancelText="取消"
    width="1200px"
  >
    <!-- 查询条件 -->
    <div class="search-section">
      <a-form layout="inline" :model="searchForm">
        <a-form-item label="工单任务号">
          <a-input
            v-model:value="searchForm.taskNo"
            placeholder="请输入工单任务号"
          />
        </a-form-item>
        <a-form-item label="工单任务标题">
          <a-input
            v-model:value="searchForm.taskTitle"
            placeholder="请输入工单任务标题"
          />
        </a-form-item>
        <a-form-item label="工单任务状态描述">
          <a-input
            v-model:value="searchForm.statusDesc"
            placeholder="请输入状态描述"
          />
        </a-form-item>
        <a-form-item label="责任班组">
          <a-input
            v-model:value="searchForm.responsibleTeam"
            placeholder="请输入责任班组"
          />
        </a-form-item>
        <a-form-item label="工作负责人">
          <a-input
            v-model:value="searchForm.responsible"
            placeholder="请输入负责人"
          />
        </a-form-item>
        <a-form-item label="设备编码">
          <a-input
            v-model:value="searchForm.equipmentCode"
            placeholder="请输入设备编码"
          />
        </a-form-item>
        <a-form-item>
          <a-space>
            <a-button type="primary" @click="handleSearch">查询</a-button>
            <a-button @click="handleReset">重置</a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </div>

    <!-- 表格 -->
    <a-table
      :columns="columns"
      :data-source="tableData"
      :row-selection="{
        selectedRowKeys,
        onChange: onSelectChange,
        type: 'radio',
      }"
      :scroll="{ x: 2000, y: 400 }"
      size="small"
    />
  </a-modal>
</template>

<script setup>
import { ref } from "vue";
import { message } from "ant-design-vue";

const visible = ref(false);
const selectedRowKeys = ref([]);

// 查询条件表单
const searchForm = ref({
  taskNo: "",
  taskTitle: "",
  statusDesc: "",
  responsibleTeam: "",
  responsible: "",
  equipmentCode: "",
});

// 表格列定义
const columns = [
  { title: "序号", dataIndex: "index", width: 60, fixed: "left" },
  { title: "工单任务号", dataIndex: "taskNo", width: 120, fixed: "left" },
  { title: "工单任务标题", dataIndex: "taskTitle", width: 200 },
  { title: "工单状态", dataIndex: "status", width: 100 },
  { title: "工单任务状态描述", dataIndex: "statusDesc", width: 150 },
  { title: "维修分级", dataIndex: "repairLevel", width: 100 },
  { title: "专业", dataIndex: "specialty", width: 100 },
  { title: "责任班组", dataIndex: "responsibleTeam", width: 120 },
  { title: "工作班组", dataIndex: "workTeam", width: 120 },
  { title: "工作准备人", dataIndex: "preparer", width: 120 },
  { title: "工作负责人", dataIndex: "responsible", width: 120 },
  { title: "工作负责人联系方式", dataIndex: "responsibleContact", width: 150 },
  { title: "系统代码", dataIndex: "systemCode", width: 100 },
  { title: "设备编码", dataIndex: "equipmentCode", width: 120 },
  { title: "设备位置", dataIndex: "equipmentLocation", width: 150 },
  { title: "设备名称", dataIndex: "equipmentName", width: 150 },
  { title: "设备分级", dataIndex: "equipmentLevel", width: 100 },
  { title: "计划开始日期", dataIndex: "planStartDate", width: 120 },
  { title: "计划结束日期", dataIndex: "planEndDate", width: 120 },
  { title: "实际开始日期", dataIndex: "actualStartDate", width: 120 },
  { title: "实际结束日期", dataIndex: "actualEndDate", width: 120 },
  { title: "完工日期", dataIndex: "completionDate", width: 120 },
];

// 表格数据
const tableData = ref([
  {
    key: "1",
    index: 1,
    taskNo: "WO2024001",
    taskTitle: "设备维护保养",
    status: "进行中",
    statusDesc: "正常执行",
    repairLevel: "A级",
    specialty: "机械",
    responsibleTeam: "维修一班",
    workTeam: "维修二组",
    preparer: "张三",
    responsible: "李四",
    responsibleContact: "13800138000",
    systemCode: "SYS001",
    equipmentCode: "EQ001",
    equipmentLocation: "1号车间",
    equipmentName: "注塑机",
    equipmentLevel: "A级",
    planStartDate: "2024-01-15",
    planEndDate: "2024-01-20",
    actualStartDate: "2024-01-15",
    actualEndDate: "",
    completionDate: "",
  },
  // ... 可以添加更多示例数据
]);

// 选择行变化
const onSelectChange = (keys) => {
  selectedRowKeys.value = keys;
};

// 查询
const handleSearch = () => {
  console.log("查询条件：", searchForm.value);
};

// 重置
const handleReset = () => {
  searchForm.value = {
    taskNo: "",
    taskTitle: "",
    statusDesc: "",
    responsibleTeam: "",
    responsible: "",
    equipmentCode: "",
  };
};

// 确定
const handleOk = () => {
  if (!selectedRowKeys.value.length) {
    message.warning("请选择一条工单数据");
    return;
  }
  const selectedRecord = tableData.value.find(
    (item) => item.key === selectedRowKeys.value[0]
  );

  // 打印选中的数据
  console.log("选中的工单数据：", selectedRecord);

  emit("success", selectedRecord);
  handleCancel();
};

// 取消
const handleCancel = () => {
  visible.value = false;
  selectedRowKeys.value = [];
};

// 打开弹窗方法
const openModal = () => {
  visible.value = true;
};

defineExpose({
  openModal,
});

const emit = defineEmits(["success"]);
</script>

<style lang="scss" scoped>
.search-section {
  margin-bottom: 16px;

  :deep(.ant-form-item) {
    margin-bottom: 16px;
  }
}

:deep(.ant-table-thead > tr > th) {
  white-space: nowrap;
}
</style>
