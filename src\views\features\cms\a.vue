<template>
  <div class="gantt-container">
    <div class="gantt-header-row">
      <a-button
        type="primary"
        @click="exportGanttChart"
        :loading="exporting"
        icon="download"
      >
        导出甘特图
      </a-button>
    </div>
    <div class="table-container">
      <a-table
        ref="ganttTableRef"
        :columns="columns"
        :data-source="data"
        :pagination="false"
        bordered
        size="middle"
        :scroll="{ y: 500 }"
        class="gantt-table"
        :custom-row="customRowClass"
      >
        <!-- 自定义列渲染 -->
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'planDay'">
            {{ record.planDay }}
            <template v-if="record.actDay"> /{{ record.actDay }} </template>
          </template>
          <!-- 渲染甘特图区域 -->
          <template v-if="column.dataIndex === 'gantt'">
            <div class="gantt-row">
              <div class="gantt-timeline">
                <!-- 添加滚动区域的日期线 -->
                <div
                  v-if="showTodayLine"
                  class="scroll-area-today-line"
                  :style="todayLineStyle"
                ></div>

                <!-- 甘特图条 -->
                <div
                  v-if="shouldRenderBar(record)"
                  class="gantt-bar"
                  :style="getBarStyleNew(record)"
                >
                  <span v-if="record.actDay" class="bar-text">
                    {{ record.actDay }}
                  </span>
                </div>

                <!-- 超期/提前指示器，只对已完成项目（status=1）显示 -->
                <div
                  v-if="record.status === 1 && record.cqDay !== undefined"
                  class="delay-indicator"
                  :style="getDelayStyle(record)"
                >
                  <!-- 指示器内不显示文本 -->
                </div>
                <!-- 将超期/提前文本作为单独元素展示在指示器外部 -->
                <div
                  v-if="record.status === 1 && record.cqDay !== undefined"
                  class="delay-text"
                  :style="getDelayTextStyle(record)"
                >
                  {{
                    record.cqDay > 0
                      ? "+" + record.cqDay + "天"
                      : "-" + Math.abs(record.cqDay) + "天"
                  }}
                </div>
              </div>
            </div>
          </template>
        </template>

        <!-- 自定义表头 -->
        <template #headerCell="{ column }">
          <template v-if="column.dataIndex === 'gantt'">
            <div class="gantt-header" id="gantt-header">
              <div class="month-row">
                <div
                  v-for="(month, index) in months"
                  :key="index"
                  class="month-cell"
                >
                  {{ month }}
                </div>
              </div>
              <div class="repair-row">
                <div
                  v-for="(count, index) in repairCounts"
                  :key="index"
                  class="repair-cell"
                >
                  {{ count }}
                </div>
              </div>
              <!-- 表头区域的日期线 -->
              <div
                class="continuous-today-line"
                :style="todayLineStyle"
                v-if="showTodayLine"
              ></div>
            </div>
          </template>
          <template v-else>
            {{ column.title }}
          </template>
        </template>
      </a-table>
    </div>
    <!-- 隐藏的截图容器 -->
    <div id="screenshotContainer" style="display: none"></div>
  </div>
</template>

<script setup>
import { ref, computed } from "vue";
import { message } from "ant-design-vue"; // 导入消息提示组件
import html2canvas from "html2canvas"; // 导入html2canvas库

// 定义导出状态变量
const exporting = ref(false);
const ganttTableRef = ref(null); // 表格引用

// 定义月份
const months = [
  "1月",
  "2月",
  "3月",
  "4月",
  "5月",
  "6月",
  "7月",
  "8月",
  "9月",
  "10月",
  "11月",
  "12月",
];

// 生成xx次数数据（示例数据）
const repairCounts = [
  "3次",
  "2次",
  "4次",
  "1次",
  "2次",
  "5次",
  "2次",
  "3次",
  "4次",
  "2次",
  "1次",
  "3次",
];

// 自定义行属性，禁用行的hover状态
const customRowClass = () => {
  return {
    class: "no-hover-effect",
  };
};

// 甘特图列 - 一个大列包含所有月份
const ganttColumn = {
  title: "", // 使用自定义表头
  dataIndex: "gantt",
  key: "gantt",
  width: 1100, // 再次增加宽度，确保12月31日内容有足够空间显示
};

// 基本列定义
const baseColumns = [
  {
    title: "xx编号",
    dataIndex: "id",
    key: "id",
    width: 120,
    align: "center",
  },
  {
    title: "类型",
    dataIndex: "type",
    key: "type",
    width: 100,
    align: "center",
  },
  {
    title: "开始时间",
    dataIndex: "startDate",
    key: "startDate",
    width: 140,
    align: "center",
  },
  {
    title: "结束时间",
    dataIndex: "endDate",
    key: "endDate",
    width: 140,
    align: "center",
  },
  {
    title: "计划/实际",
    dataIndex: "planDay",
    key: "planDay",
    width: 90, // 稍微减小，释放更多空间给甘特图
    align: "center",
  },
];

// 合并所有列
const columns = [...baseColumns, ganttColumn];

// 模拟数据
const data = [
  {
    key: "1",
    id: "JH2023-001",
    title: "",
    type: "A",
    startDate: "2023-03-10",
    endDate: "2023-05-20",
    planDay: "70",
    actDay: "80",
    status: 1, // 完整色块
    cqDay: 10, // 超期10%
  },
  {
    key: "2",
    id: "JH2023-002",
    title: "",
    type: "A",
    startDate: "2023-05-15",
    endDate: "2023-07-05",
    planDay: "50",
    actDay: "45",
    status: 1, // 完整色块
    cqDay: -5, // 提前5%
  },
  {
    key: "3",
    id: "JH2023-003",
    title: "",
    type: "A",
    startDate: "2023-06-08",
    endDate: "2023-07-28",
    planDay: "50",
    actDay: "25", // 进行中，已完成部分
    status: 0.5, // 50%进度
  },
  {
    key: "4",
    id: "JH2023-004",
    title: "",
    type: "A",
    startDate: "2023-08-01",
    endDate: "2023-10-10",
    planDay: "70",
    actDay: "21", // 进行中，已完成部分
    status: 0.3, // 30%进度
  },
  {
    key: "5",
    id: "JH2023-005",
    title: "",
    type: "A",
    startDate: "2023-09-10",
    endDate: "2023-11-20",
    planDay: "70",
    actDay: "85",
    status: 1, // 完整色块
    cqDay: 15, // 超期15%
  },
  {
    key: "6",
    id: "JH2023-006",
    title: "",
    type: "A",
    startDate: "2023-11-01",
    endDate: "2023-12-31",
    planDay: "61",
    actDay: "66",
    status: 1, // 完整色块
    cqDay: 5, // 超期5%
  },
];

// 新的获取条形样式方法，将甘特图整体计算
const getBarStyleNew = (record) => {
  // 如果没有预计算的月份数据，根据日期计算
  if (!record.startMonth && record.startDate) {
    // 解析日期 - 确保正确解析
    const startDate = new Date(record.startDate.replace(/-/g, "/"));
    const endDate = new Date(record.endDate.replace(/-/g, "/"));
    const year = startDate.getFullYear();

    // 计算月份 - 正确性检查
    const startMonth = startDate.getMonth() + 1; // 0-11 转为 1-12
    const endMonth = endDate.getMonth() + 1;
    
    // 赋值前添加输出以便调试
    if (record.startDate === "2023-09-10") {
      console.log("Debug 2023-09-10:", {
        startDate: record.startDate,
        parsedDate: startDate.toISOString(),
        month: startMonth
      });
    }

    // 计算月份
    record.startMonth = startMonth;
    record.endMonth = endMonth;

    // 计算日偏移量 - 精确到日
    record.startOffset = startDate.getDate();
    record.endOffset = endDate.getDate();

    // 计算工期（天数）
    const diffTime = Math.abs(endDate - startDate);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    record.planDay = diffDays;

    // 保存计算年份，确保所有计算使用相同年份
    record.year = year;
  }

  // 针对 2023-09-10 的特殊处理 - 强制纠正可能存在的错误
  if (record.startDate === "2023-09-10" && (!record.startMonth || record.startMonth !== 9)) {
    console.log("Correcting 2023-09-10 month");
    record.startMonth = 9; // 强制设置为9月
    record.startOffset = 10; // 设置日期为10日
    // 如果年份没有设置，也设置一下
    if (!record.year) {
      record.year = 2023;
    }
  }

  // 根据类型设置颜色
  let color;
  switch (record.type) {
    case "A":
      color = "#1e88e5";
      break;
    case "B":
      color = "#4caf50";
      break;
    case "C":
      color = "#ffc107";
      break;
    default:
      color = "#673AB7"; // 默认颜色
  }

  // 获取实际月份天数的函数
  const getDaysInMonth = (month, year = new Date().getFullYear()) => {
    return new Date(year, month, 0).getDate();
  };

  // 获取整年的总天数（考虑闰年）
  const daysInYear = (year = new Date().getFullYear()) => {
    return (year % 4 === 0 && year % 100 !== 0) || year % 400 === 0 ? 366 : 365;
  };

  const year = record.year || new Date().getFullYear();
  const yearTotalDays = daysInYear(year);

  // 计算当年到起始月之前的总天数 - 精确计算
  let startDayOfYear = 0;
  for (let m = 1; m < record.startMonth; m++) {
    startDayOfYear += getDaysInMonth(m, year);
  }
  startDayOfYear += record.startOffset - 1; // 减1是因为我们需要从0开始计数

  // 计算当年到结束月之前的总天数 - 精确计算
  let endDayOfYear = 0;
  for (let m = 1; m < record.endMonth; m++) {
    endDayOfYear += getDaysInMonth(m, year);
  }
  endDayOfYear += record.endOffset; // 结束日包含当天

  // 计算在时间线中的准确位置
  const leftPercent = (startDayOfYear / yearTotalDays) * 92;
  
  // 计算精确的宽度百分比
  const durationDays = endDayOfYear - startDayOfYear;
  let widthPercent = (durationDays / yearTotalDays) * 92;
  
  // 特别处理12月份的项目 - 为了视觉上的正确性
  if (record.endMonth === 12) {
    // 根据12月的具体日期调整宽度
    if (record.endOffset >= 28) {
      // 28-31日：给予更多余量，以便足够显示
      widthPercent += 1.8;
    } else if (record.endOffset > 20) {
      widthPercent += 1.3;
    } else {
      widthPercent += 0.8;
    }
    
    // 确保不会超出范围
    if (leftPercent + widthPercent > 97.5) {
      widthPercent = 97.5 - leftPercent;
    }
  }

  // 保存数据标记以供导出使用
  record.dataStatus = record.status;

  let style = {
    left: `${leftPercent}%`,
    width: `${widthPercent}%`,
  };

  // 根据status设置背景样式
  if (record.status === 1) {
    // 完整显示色块
    style.backgroundColor = color;
  } else {
    // 按照比例显示色块，使用线性渐变
    const percentage = record.status * 100 || 0;
    style.background = `linear-gradient(to right, ${color} ${percentage}%, rgba(255,255,255,0.3) ${percentage}%)`;
    // 为status不为1的添加虚线边框
    style.border = `1px dashed ${color}`;
  }

  return style;
};

// 判断是否应该渲染甘特图条
const shouldRenderBar = (record) => {
  return (
    (record.startMonth && record.endMonth) ||
    (record.startDate && record.endDate)
  );
};

// 计算今日日期线的位置
const showTodayLine = computed(() => {
  const today = new Date();
  const currentYear = today.getFullYear();
  const currentMonth = today.getMonth() + 1; // JavaScript 月份是0-11

  // 检查当前月份是否在甘特图范围内
  return currentMonth >= 1 && currentMonth <= 12;
});

const todayLineStyle = computed(() => {
  const today = new Date();
  const year = today.getFullYear();
  const currentMonth = today.getMonth() + 1; // 1-12
  const currentDate = today.getDate();

  // 获取整年的总天数（考虑闰年）
  const daysInYear = (year) => {
    return (year % 4 === 0 && year % 100 !== 0) || year % 400 === 0 ? 366 : 365;
  };

  const yearTotalDays = daysInYear(year);

  // 计算从年初到当前日期的天数
  const getDaysInMonth = (month, year) => {
    return new Date(year, month, 0).getDate();
  };

  let dayOfYear = 0;
  for (let m = 1; m < currentMonth; m++) {
    dayOfYear += getDaysInMonth(m, year);
  }
  dayOfYear += currentDate;

  // 计算位置百分比（使用与甘特图条相同的92%宽度计算）
  const leftPosition = (dayOfYear / yearTotalDays) * 92;

  return {
    left: `${leftPosition}%`,
  };
});

// 获取延期/提前指示器的样式
const getDelayStyle = (record) => {
  if (record.status !== 1 || record.cqDay === undefined) return {};

  // 获取整年的总天数（考虑闰年）
  const daysInYear = (year = new Date().getFullYear()) => {
    return (year % 4 === 0 && year % 100 !== 0) || year % 400 === 0 ? 366 : 365;
  };
  
  // 使用与甘特图条相同的年份
  const year = record.year || new Date().getFullYear();
  const yearTotalDays = daysInYear(year);
  
  // 计算在整个时间线中的甘特图条位置
  const getDaysInMonth = (month, year = new Date().getFullYear()) => {
    return new Date(year, month, 0).getDate();
  };
  
  // 计算当年到起始月之前的总天数
  let startDayOfYear = 0;
  for (let m = 1; m < record.startMonth; m++) {
    startDayOfYear += getDaysInMonth(m, year);
  }
  startDayOfYear += record.startOffset - 1; // 调整为从0开始计数，与甘特图条一致
  
  // 计算当年到结束月之前的总天数
  let endDayOfYear = 0;
  for (let m = 1; m < record.endMonth; m++) {
    endDayOfYear += getDaysInMonth(m, year);
  }
  endDayOfYear += record.endOffset; // 结束日包含当天

  // 甘特图条的宽度和位置
  const durationDays = endDayOfYear - startDayOfYear;
  const widthPercent = (durationDays / yearTotalDays) * 92;
  const leftPercent = (startDayOfYear / yearTotalDays) * 92;

  // 将超期/提前天数精确转换为百分比
  const dayPercentage = (Math.abs(record.cqDay) / yearTotalDays) * 92;

  // 指示器的位置 - 应该与甘特图条的右侧对齐
  let delayLeft = leftPercent + widthPercent;
  
  // 特别处理12月份的项目 - 确保与甘特图条的处理一致
  if (record.endMonth === 12) {
    if (record.endOffset >= 28) {
      // 特别处理12月底(28-31日)
      delayLeft += 1.8; // 与甘特图条使用相同的调整值
    } else if (record.endOffset > 20) {
      delayLeft += 1.3;
    } else {
      delayLeft += 0.8;
    }
    
    // 确保不会超出范围
    if (delayLeft > 97.5) {
      delayLeft = 97.5;
    }
  }

  // 设置渐变色
  const isDelay = record.cqDay > 0; // 正数表示超期，负数表示提前
  const gradientColor = isDelay
    ? "linear-gradient(to right, rgba(255,0,0,0.3), rgba(255,0,0,0.7))" // 超期：红色
    : "linear-gradient(to right, rgba(0,150,0,0.3), rgba(0,150,0,0.7))"; // 提前：绿色
    
  // 缩小延期指示器的宽度，确保不会超出边界
  const adjustedDayPercentage = Math.min(dayPercentage, 97.5 - delayLeft);

  return {
    left: `${delayLeft}%`,
    width: `${adjustedDayPercentage}%`,
    background: gradientColor,
  };
};

// 获取延期/提前文本样式
const getDelayTextStyle = (record) => {
  if (record.status !== 1 || record.cqDay === undefined) return {};

  // 获取整年的总天数（考虑闰年）
  const daysInYear = (year = new Date().getFullYear()) => {
    return (year % 4 === 0 && year % 100 !== 0) || year % 400 === 0 ? 366 : 365;
  };
  
  // 使用与甘特图条相同的年份
  const year = record.year || new Date().getFullYear();
  const yearTotalDays = daysInYear(year);
  
  // 计算在整个时间线中的甘特图条位置
  const getDaysInMonth = (month, year = new Date().getFullYear()) => {
    return new Date(year, month, 0).getDate();
  };
  
  // 计算当年到起始月之前的总天数
  let startDayOfYear = 0;
  for (let m = 1; m < record.startMonth; m++) {
    startDayOfYear += getDaysInMonth(m, year);
  }
  startDayOfYear += record.startOffset - 1; // 调整为从0开始计数，与甘特图条一致
  
  // 计算当年到结束月之前的总天数
  let endDayOfYear = 0;
  for (let m = 1; m < record.endMonth; m++) {
    endDayOfYear += getDaysInMonth(m, year);
  }
  endDayOfYear += record.endOffset; // 结束日包含当天

  // 甘特图条的宽度和位置，同样只使用92%的宽度计算
  const durationDays = endDayOfYear - startDayOfYear;
  const widthPercent = (durationDays / yearTotalDays) * 92;
  const leftPercent = (startDayOfYear / yearTotalDays) * 92;

  // 将天数转换为百分比 (相对于年总天数)
  const dayPercentage = (Math.abs(record.cqDay) / yearTotalDays) * 92;

  // 指示器的位置，无论是超期还是提前，都显示在右侧
  let delayLeft = leftPercent + widthPercent;
  
  // 特别处理12月份的项目 - 确保与甘特图条的处理一致
  if (record.endMonth === 12) {
    if (record.endOffset >= 28) {
      // 特别处理12月底(28-31日)
      delayLeft += 1.8; // 与甘特图条使用相同的调整值
    } else if (record.endOffset > 20) {
      delayLeft += 1.3;
    } else {
      delayLeft += 0.8;
    }
    
    // 确保不会超出范围
    if (delayLeft > 97.5) {
      delayLeft = 97.5;
    }
  }
  
  // 缩小延期指示器的宽度，防止超出边界
  const adjustedDayPercentage = Math.min(dayPercentage, 97.5 - delayLeft);
  
  // 始终将延期文本放在延期色块的后面，稍微增加偏移以清晰分隔
  const textPosition = delayLeft + adjustedDayPercentage + 0.8; // 增加偏移量确保更清晰
  
  // 根据超期还是提前设置不同文本颜色
  const isDelay = record.cqDay > 0; // 正数表示超期，负数表示提前
  const textColor = isDelay ? "#ff4d4f" : "#52c41a";
  
  // 处理超出边界的情况
  if (textPosition > 96.5) {
    // 如果文本位置太靠近右边缘，改为显示在延期色块前面
    return {
      left: `${delayLeft - 10}%`, // 放在延期色块左侧位置
      color: textColor,
      fontWeight: "bold",
      textShadow: "0 0 1px rgba(0,0,0,0.5)",
    };
  }
  
  // 正常情况：始终将文本放在延期指示器后面
  return {
    left: `${textPosition}%`,
    color: textColor,
    fontWeight: "bold",
    textShadow: "0 0 1px rgba(0,0,0,0.5)",
  };
};

// 导出甘特图为图片
const exportGanttChart = async () => {
  exporting.value = true;
  message.loading({
    content: "正在准备截图...",
    key: "ganttExport",
    duration: 0,
  });

  try {
    // 在导出前预处理数据，确保所有条形图的日期计算正确
 
    const tableElement = ganttTableRef.value && ganttTableRef.value.$el;
    if (!tableElement) throw new Error("无法获取表格元素");

    // 创建一个新的div作为容器 
    const exportContainer = document.createElement("div");
    exportContainer.style.position = "absolute";
    exportContainer.style.left = "-9999px";
    exportContainer.style.backgroundColor = "#162e5a";
    exportContainer.style.width = "fit-content";
    document.body.appendChild(exportContainer);

    // 创建标题元素 - 可选，如果需要在截图中包含标题
    const titleElement = document.createElement("h2");
    titleElement.textContent = "xx项目甘特图";
    titleElement.style.color = "#fff";
    titleElement.style.margin = "20px";
    titleElement.style.fontSize = "22px";
    titleElement.style.fontWeight = "500";
    titleElement.style.textAlign = "center";
    titleElement.style.padding = "10px 0";
    exportContainer.appendChild(titleElement);

    // 获取原始列宽度配置
    const columnsConfig = [
      { width: 120 }, // xx编号
      { width: 100 }, // 类型
      { width: 140 }, // 计划开始时间
      { width: 140 }, // 计划结束时间
      { width: 90 }, // 计划工期
      { width: 1100 }, // 甘特图区域
    ];

    // 计算总宽度
    const totalWidth = columnsConfig.reduce((sum, col) => sum + col.width, 0);

    // 克隆表格
    const tableClone = tableElement.querySelector(".ant-table").cloneNode(true);
    exportContainer.appendChild(tableClone);

    // 直接移除所有测量行 - 这些行会导致额外的空行
    try {
      // 查找并移除所有测量行（这些是由Ant Design用于列宽度计算的隐藏行）
      const measureRows = tableClone.querySelectorAll(".ant-table-measure-row");
      if (measureRows.length > 0) {
        measureRows.forEach((row) => row.parentNode.removeChild(row));
      }

      // 直接移除第一个空表格行（如果存在）
      const tbody = tableClone.querySelector(".ant-table-tbody");
      if (tbody && tbody.firstElementChild) {
        const firstRow = tbody.firstElementChild;
        const cells = firstRow.querySelectorAll("td");
        let isEmpty = true;

        // 检查第一行是否为空行或只有隐藏内容
        cells.forEach((cell) => {
          if (
            cell.textContent.trim() !== "" ||
            cell.querySelector(".gantt-bar")
          ) {
            isEmpty = false;
          }
        });

        if (isEmpty) {
          tbody.removeChild(firstRow);
        }
      }
    } catch (e) {
      console.error("移除测量行时出错:", e);
    }

    // 设置表格样式
    tableClone.style.width = `${totalWidth}px`;
    tableClone.style.tableLayout = "fixed";
    tableClone.style.borderCollapse = "collapse";
    tableClone.style.borderSpacing = "0";
    tableClone.style.backgroundColor = "#162e5a";
    tableClone.style.color = "#fff";
    tableClone.style.maxWidth = `${totalWidth}px`;
    tableClone.style.boxShadow = "none";
    tableClone.style.border = "none";

    // 确保所有内容都可见
    const scrollContainers = tableClone.querySelectorAll('[style*="overflow"]');
    scrollContainers.forEach((el) => {
      el.style.overflow = "visible";
      el.style.height = "auto";
      el.style.maxHeight = "none";
      el.style.width = `${totalWidth}px`;
    });

    // 移除固定列
    const fixedCells = tableClone.querySelectorAll(
      ".ant-table-cell-fix-left, .ant-table-cell-fix-right"
    );
    fixedCells.forEach((cell) => {
      cell.style.position = "static";
      cell.style.left = "auto";
      cell.style.right = "auto";
    });

    // 处理表格容器
    const tableContainer = tableClone.querySelector(".ant-table-container");
    if (tableContainer) {
      tableContainer.style.width = `${totalWidth}px`;
      tableContainer.style.maxWidth = `${totalWidth}px`;
      tableContainer.style.border = "none";
    }

    // 处理表格内容区域
    const tableContent = tableClone.querySelector(".ant-table-content");
    if (tableContent) {
      tableContent.style.width = `${totalWidth}px`;
      tableContent.style.maxWidth = `${totalWidth}px`;
    }

    // 处理表头
    const tableHeader = tableClone.querySelector(".ant-table-thead");
    if (tableHeader) {
      tableHeader.style.backgroundColor = "#0f5391";

      const headerRows = tableHeader.querySelectorAll("tr");
      headerRows.forEach((row) => {
        row.style.height = "40px";
        row.style.backgroundColor = "#0f5391";
      });

      const headerCells = tableHeader.querySelectorAll("th");
      headerCells.forEach((cell, index) => {
        if (index < columnsConfig.length) {
          const width = columnsConfig[index].width;
          cell.style.width = `${width}px`;
          cell.style.minWidth = `${width}px`;
          cell.style.maxWidth = `${width}px`;
          cell.style.padding = "0";
          cell.style.margin = "0";
          cell.style.height = "40px";
          cell.style.lineHeight = "40px";
          cell.style.color = "#0ad0fd";
          cell.style.textAlign = "center";
          cell.style.fontWeight = "500";
          cell.style.boxSizing = "border-box";
          cell.style.borderBottom = "none";

          // 确保计划工期与甘特图表头正确对齐
          if (index === 4) {
            // 计划工期列
            cell.style.borderRight = "1px solid #4882c0";
          }
          if (index === 5) {
            // 甘特图列
            cell.style.borderLeft = "none";
            cell.style.padding = "0";
          }
        }
      });
    }

    // 处理表体
    const tableBody = tableClone.querySelector(".ant-table-tbody");
    if (tableBody) {
      // 设置斑马线样式
      const rows = tableBody.querySelectorAll("tr");
      rows.forEach((row, rowIndex) => {
        // 设置行高和背景色
        row.style.height = "40px";
        row.style.backgroundColor = rowIndex % 2 === 0 ? "#1d274b" : "#162e5a";

        // 设置单元格样式
        const cells = row.querySelectorAll("td");
        cells.forEach((cell, cellIndex) => {
          if (cellIndex < columnsConfig.length) {
            const width = columnsConfig[cellIndex].width;
            cell.style.width = `${width}px`;
            cell.style.minWidth = `${width}px`;
            cell.style.maxWidth = `${width}px`;
            cell.style.padding = "0";
            cell.style.margin = "0";
            cell.style.height = "40px";
            cell.style.lineHeight = "40px";
            cell.style.color = "white";
            cell.style.textAlign = "center";
            cell.style.boxSizing = "border-box";
            cell.style.borderBottom = "none";

            // 设置右边框
            if (cellIndex === 4) {
              // 计划工期列
              cell.style.borderRight = "1px solid #38618d";
            }
            if (cellIndex === 5) {
              // 甘特图列
              cell.style.borderLeft = "none";
              cell.style.padding = "0";
            }
          }
        });
      });
    }

    // 处理甘特图条
    const ganttBars = tableClone.querySelectorAll(".gantt-bar");
    ganttBars.forEach((bar) => {
      const barWidth = parseFloat(bar.style.width || "0");
      bar.style.zIndex = "5";
      bar.style.overflow = "visible"; // 确保内容可以溢出条形图
      bar.style.height = "26px"; // 增加高度以适应文本

      // 确保文字可见
      const barText = bar.querySelector(".bar-text");
      if (barText) {
        barText.style.width = "auto";
        barText.style.whiteSpace = "nowrap";
        barText.style.overflow = "visible";
        barText.style.position = "relative";
        barText.style.zIndex = "10";
        barText.style.color = "white";
        barText.style.textShadow = "0 0 2px rgba(0,0,0,0.8)";
        barText.style.fontWeight = "bold";
        barText.style.fontSize = "13px";
        barText.style.lineHeight = "26px"; // 确保文本垂直居中
        barText.style.display = "inline-block";
        barText.style.padding = "0 8px";

        // 处理文字位置
        if (barWidth < 30) {
          // 如果条形图太窄，确保文字居中显示
          bar.style.display = "flex";
          bar.style.justifyContent = "center";
          bar.style.alignItems = "center";
          bar.style.minWidth = "30px"; // 确保最小宽度足够显示文字

          // 针对不同状态的处理
          const status = bar.parentElement?.parentElement?.parentElement
            ?.querySelector("[data-status]")
            ?.getAttribute("data-status");
          if (status && parseFloat(status) < 1) {
            // 对于进度不完整的条，将文字放在条外部
            barText.style.position = "absolute";
            barText.style.left = "100%";
            barText.style.marginLeft = "6px";
            barText.style.color = "#333";
            barText.style.textShadow = "none";
          } else {
            // 对于完整的条，尝试将文字保持在内部
            barText.style.textAlign = "center";
            barText.style.minWidth = "auto";
          }
        } else {
          // 对于正常宽度的条，居中显示文字
          bar.style.display = "flex";
          bar.style.justifyContent = "center";
          bar.style.alignItems = "center";
          barText.style.position = "static";
          barText.style.textAlign = "center";
        }
      }
    });

    // 处理甘特图表头
    const ganttHeader = tableClone.querySelector(".gantt-header");
    if (ganttHeader) {
      const ganttWidth = columnsConfig[5].width;
      ganttHeader.style.width = `${ganttWidth}px`;
      ganttHeader.style.maxWidth = `${ganttWidth}px`;
      ganttHeader.style.boxSizing = "border-box";
      ganttHeader.style.padding = "0";
      ganttHeader.style.margin = "0";
      ganttHeader.style.height = "40px";
      ganttHeader.style.position = "relative";

      // 处理月份行
      const monthRow = ganttHeader.querySelector(".month-row");
      if (monthRow) {
        monthRow.style.display = "flex";
        monthRow.style.width = "100%";
        monthRow.style.backgroundColor = "#0f5391";
        monthRow.style.height = "24px";

        const monthCells = monthRow.querySelectorAll(".month-cell");
        if (monthCells.length) {
          const cellWidth = Math.floor(ganttWidth / monthCells.length);
          monthCells.forEach((cell) => {
            cell.style.flex = "1";
            cell.style.width = `${cellWidth}px`;
            cell.style.minWidth = `${cellWidth}px`;
            cell.style.display = "flex";
            cell.style.alignItems = "center";
            cell.style.justifyContent = "center";
            cell.style.color = "#0ad0fd";
            cell.style.fontWeight = "500";
            cell.style.fontSize = "13px";
          });
        }
      }

      // 处理修复行
      const repairRow = ganttHeader.querySelector(".repair-row");
      if (repairRow) {
        repairRow.style.display = "flex";
        repairRow.style.width = "100%";
        repairRow.style.backgroundColor = "#1a63a0";
        repairRow.style.height = "16px";
        repairRow.style.position = "absolute";
        repairRow.style.bottom = "0";

        const repairCells = repairRow.querySelectorAll(".repair-cell");
        if (repairCells.length) {
          const cellWidth = Math.floor(ganttWidth / repairCells.length);
          repairCells.forEach((cell) => {
            cell.style.flex = "1";
            cell.style.width = `${cellWidth}px`;
            cell.style.minWidth = `${cellWidth}px`;
            cell.style.display = "flex";
            cell.style.alignItems = "center";
            cell.style.justifyContent = "center";
            cell.style.color = "white";
            cell.style.fontSize = "11px";
          });
        }
      }
    }

    // 等待样式应用完成
    await new Promise((resolve) => setTimeout(resolve, 1000));

    // 使用html2canvas生成图片
    const canvas = await html2canvas(exportContainer, {
      width: totalWidth,
      height: exportContainer.scrollHeight,
      backgroundColor: "#162e5a",
      scale: 2, // 提高清晰度
      useCORS: true,
      logging: false,
      allowTaint: true,
      onclone: (clonedDoc, clonedElement) => {
        // 在克隆的元素上再次应用文本修复
        const clonedBars = clonedElement.querySelectorAll(".gantt-bar");
        clonedBars.forEach((bar) => {
          bar.style.overflow = "visible";
          bar.style.height = "26px";

          const barWidth = parseFloat(bar.style.width || "0");
          const barText = bar.querySelector(".bar-text");
          if (barText) {
            barText.style.width = "auto";
            barText.style.overflow = "visible";
            barText.style.whiteSpace = "nowrap";
            barText.style.lineHeight = "26px";
            barText.style.padding = "0 8px";
            barText.style.color = "white";
            barText.style.textShadow = "0 0 2px rgba(0,0,0,0.8)";
            barText.style.fontWeight = "bold";

            // 再次处理文字位置
            if (barWidth < 30) {
              // 如果条形图太窄，确保文字居中显示
              bar.style.display = "flex";
              bar.style.justifyContent = "center";
              bar.style.alignItems = "center";
              bar.style.minWidth = "30px";

              // 针对进度不同的处理
              const status = bar.parentElement?.parentElement?.parentElement
                ?.querySelector("[data-status]")
                ?.getAttribute("data-status");
              if (status && parseFloat(status) < 1) {
                // 对于进度不完整的条，将文字放在条外部
                barText.style.position = "absolute";
                barText.style.left = "100%";
                barText.style.marginLeft = "6px";
                barText.style.color = "#333";
                barText.style.textShadow = "none";
              } else {
                // 对于完整的条，尝试将文字保持在内部
                barText.style.textAlign = "center";
                barText.style.minWidth = "auto";
              }
            } else {
              // 对于正常宽度的条，居中显示文字
              bar.style.display = "flex";
              bar.style.justifyContent = "center";
              bar.style.alignItems = "center";
              barText.style.position = "static";
              barText.style.textAlign = "center";
            }
          }
        });
      },
    });

    // 下载图片
    const imageUrl = canvas.toDataURL("image/png");
    const link = document.createElement("a");
    link.download = `甘特图_${new Date()
      .toLocaleDateString()
      .replace(/\//g, "-")}.png`;
    link.href = imageUrl;
    link.click();

    message.success({
      content: "图片导出成功！",
      key: "ganttExport",
      duration: 2,
    });

    // 清理导出容器
    document.body.removeChild(exportContainer);
  } catch (error) {
    console.error("导出图片失败:", error);
    message.error({
      content: `导出图片失败: ${error.message || "未知错误"}`,
      key: "ganttExport",
      duration: 3,
    });
  } finally {
    exporting.value = false;
  }
};
</script>

<style scoped>
.gantt-container {
  padding: 0;
  background-color: #1271ff;
  border-radius: 0;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.gantt-title {
  font-size: 22px;
  color: #333;
  margin: 20px;
  font-weight: 500;
}

.table-container {
  position: relative;
  height: 100%;
}

.gantt-table {
  background: white;
  overflow: hidden;
  margin: 0;
  padding: 0;
  border-spacing: 0;
}

/* 甘特图自定义表头 */
.gantt-header {
  width: 100%;
  height: 40px; /* 设置固定高度 */
  position: relative;
  display: flex;
  flex-direction: column;
}

/* 月份行样式 */
.month-row {
  display: flex;
  width: 100%;
  background-color: #0f5391;
  height: 24px;
  position: relative;
}

/* xx次数行样式 */
.repair-row {
  display: flex;
  width: 100%;
  background-color: #1a63a0;
  height: 16px;
  position: absolute;
  bottom: 0; /* 贴近底部 */
  left: 0;
  right: 0;
}

/* 调整月份单元格样式 */
.month-cell {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #0ad0fd;
  font-weight: 500;
  font-size: 13px;
  padding: 0; /* 移除内边距 */
}

/* 调整xx次数单元格样式 */
.repair-cell {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 11px;
  padding: 0; /* 移除内边距 */
}

/* 连贯的今日指示线 */
.continuous-today-line {
  position: absolute;
  top: 40px;
  height: calc(100% - 40px);
  width: 0;
  border-left: 2px dashed #ff4d4f; /* 改用 border-left 实现虚线 */
  border-style: dashed;
  border-width: 0 0 0 2px;
  border-image: repeating-linear-gradient(
      to bottom,
      #ff4d4f 0,
      #ff4d4f 4px,
      transparent 4px,
      transparent 8px
    )
    1; /* 使用 border-image 实现更短更密的虚线 */
  opacity: 0.8;
  z-index: 1;
  pointer-events: none;
}

/* 表格相关样式 */
:deep(.ant-table) {
  position: relative;
}

:deep(.ant-table-container) {
  position: relative;
}

:deep(.ant-table-body) {
  position: relative;
  overflow-x: hidden;
  overflow-y: auto;
  max-height: 500px;
  margin: 0; /* 移除外边距 */
}

/* 确保内容区域的定位正确 */
:deep(.ant-table-tbody) {
  position: relative;
}

/* 添加一个新的类来处理滚动区域内的日期线 */
.scroll-area-today-line {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 0;
  border-left: 2px dashed #ff4d4f;
  border-style: dashed;
  border-width: 0 0 0 2px;
  border-image: repeating-linear-gradient(
      to bottom,
      #ff4d4f 0,
      #ff4d4f 4px,
      transparent 4px,
      transparent 8px
    )
    1;
  opacity: 0.8;
  z-index: 999;
  pointer-events: none;
}

/* 调整表格滚动区域的样式 */
:deep(.ant-table-body) {
  position: relative;
  height: 500px; /* 固定高度 */
  overflow-y: auto;
}

:deep(.ant-table-header) {
  position: relative;
  z-index: 1000; /* 确保表头在日期线上方 */
  background-color: #0f5391; /* 保持表头背景色 */
}

/* 确保甘特图区域可以显示日期线 */
.gantt-timeline {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center; /* 垂直居中 */
}

/* 甘特图行样式 */
.gantt-row {
  display: flex;
  width: 100%;
  height: 40px;
  position: relative;
  padding: 4px 0; /* 增加上下内边距 */
  margin: 0;
  box-sizing: border-box;
}

.gantt-cell {
  flex: 1;
  height: 100%;
  position: relative;
  border: none !important;
}

.gantt-cell:last-child {
  border-right: none;
}

/* 甘特图条形样式 */
.gantt-bar {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  height: 23px; /* 调整高度为23px */
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  box-shadow: none;
  z-index: 1;
}

/* 调整首尾样式 */
.bar-start {
  border-radius: 0;
  padding-left: 8px;
  justify-content: center;
}

.bar-middle {
  border-radius: 0;
  justify-content: center;
}

.bar-end {
  border-radius: 0;
  padding-right: 8px;
  justify-content: center;
}

.bar-full {
  border-radius: 0;
  padding: 0 8px;
  justify-content: center;
}

.bar-text {
  white-space: nowrap;
  overflow: visible;
  text-overflow: clip;
  font-weight: 500;
  text-align: center;
  width: auto;
  padding: 0 4px;
}

/* 禁用表格行悬停效果的自定义类 */
:deep(.no-hover-effect) {
  background-color: inherit !important;
}

:deep(.no-hover-effect:hover) {
  background-color: inherit !important;
}

/* 禁用表格悬停引起的闪烁问题 */
:deep(.ant-table-tbody > tr > td) {
  padding: 0;
  margin: 0;
  border: none !important;
  height: 40px;
  line-height: 40px;
}

/* 完全禁用所有hover效果 */
:deep(.ant-table-tbody > tr:hover > td),
:deep(.ant-table-tbody > tr:hover),
:deep(.ant-table-tbody > tr.ant-table-row:hover > td),
:deep(.ant-table-tbody > tr > td.ant-table-cell-hover),
:deep(.ant-table-tbody > tr.ant-table-row-hover),
:deep(.ant-table-row:hover),
:deep(.ant-table-row-hover),
:deep(.ant-table-cell-hover),
:deep(.ant-table-tbody > tr.ant-table-row:hover),
:deep(.ant-table-tbody > tr:hover td),
:deep(.ant-table-tbody > tr.ant-table-row:focus),
:deep(.ant-table-tbody > tr.ant-table-row:active),
:deep(.ant-table-cell:focus),
:deep(.ant-table-cell:active) {
  background-color: initial !important;
  background: none !important;
  transition: none !important;
  animation: none !important;
}

/* 强制斑马线样式 */
:deep(.ant-table-tbody > tr:nth-child(odd)),
:deep(.ant-table-tbody > tr:nth-child(odd)):hover,
:deep(.ant-table-tbody > tr:nth-child(odd) td):hover,
:deep(.ant-table-tbody > tr:nth-child(odd).ant-table-row):hover,
:deep(.ant-table-tbody > tr:nth-child(odd) > td),
:deep(.ant-table-tbody > tr:nth-child(odd) > td:hover) {
  background-color: #1d274b !important;
}

:deep(.ant-table-tbody > tr:nth-child(even)),
:deep(.ant-table-tbody > tr:nth-child(even)):hover,
:deep(.ant-table-tbody > tr:nth-child(even) td):hover,
:deep(.ant-table-tbody > tr:nth-child(even).ant-table-row):hover,
:deep(.ant-table-tbody > tr:nth-child(even) > td),
:deep(.ant-table-tbody > tr:nth-child(even) > td:hover) {
  background-color: #162e5a !important;
}

/* 覆盖所有Ant Design CSS变量 */
:deep(.ant-table) {
  --ant-table-row-hover-bg: unset !important;
  --ant-table-cell-hover-bg: unset !important;
}

:root {
  --ant-table-row-hover-bg: unset !important;
  --ant-table-cell-hover-bg: unset !important;
}

/* 表格样式调整 */
:deep(.ant-table-tbody > tr > td) {
  padding: 0;
  margin: 0;
  border: none !important;
  height: 40px;
  line-height: 40px;
  color: white !important; /* 设置字体颜色为白色 */
}

/* 设置表格行高 */
:deep(.ant-table-tbody > tr) {
  height: 40px;
  line-height: 40px;
  padding: 0;
  margin: 0;
}

/* 设置表格斑马条纹 */
:deep(.ant-table-tbody > tr:nth-child(odd)),
:deep(.ant-table-tbody > tr:nth-child(odd)):hover,
:deep(.ant-table-tbody > tr:nth-child(odd) > td:hover),
:deep(.ant-table-tbody > tr:nth-child(odd).ant-table-row:hover),
:deep(.ant-table-tbody > tr:nth-child(odd).ant-table-row-hover) {
  background-color: #1d274b !important;
}

:deep(.ant-table-tbody > tr:nth-child(even)),
:deep(.ant-table-tbody > tr:nth-child(even):hover),
:deep(.ant-table-tbody > tr:nth-child(even) > td:hover),
:deep(.ant-table-tbody > tr:nth-child(even).ant-table-row:hover),
:deep(.ant-table-tbody > tr:nth-child(even).ant-table-row-hover) {
  background-color: #162e5a !important;
}

/* 表头与表体之间的分隔线也可以移除 */
:deep(.ant-table-thead > tr:last-child > th) {
  border-bottom: none !important; /* 移除底部边框 */
}

/* 移除表格所有边框 */
:deep(.ant-table-container) {
  border: none !important;
}

:deep(.ant-table) {
  border: none !important;
}

:deep(.ant-table-container table) {
  border: none !important;
  border-collapse: collapse;
}

/* 基本信息列样式 */
:deep(.ant-table-thead > tr > th) {
  background-color: #0f5391;
  color: #0ad0fd;
  font-weight: 500;
  padding: 0; /* 移除内边距 */
  height: 40px;
  line-height: 40px;
  border: none !important;
}

/* 隐藏表头分割线 */
:deep(.ant-table-thead > tr > th::before) {
  display: none !important; /* 完全隐藏伪元素分隔线 */
}

/* 表头单元格之间的间隔处理 */
:deep(.ant-table-container table > thead > tr:first-child th) {
  border-top: none;
}

:deep(.ant-table-container table > thead > tr th) {
  border-right: none;
  border-bottom: none;
}

/* 处理甘特图表头特殊样式 */
:deep(.ant-table-thead > tr > th:last-child) {
  padding: 0 !important; /* 移除甘特图表头内边距 */
  overflow: hidden; /* 确保内容不溢出 */
  border: none !important;
}

/* 调整甘特图表头高度以匹配基本列表头 */
:deep(.ant-table-thead > tr > th.ant-table-cell) {
  height: auto;
  padding: 4px;
}

/* 确保表格行之间没有间隙 */
:deep(.ant-table) {
  border-collapse: collapse;
  padding: 0;
  margin: 0;
}

:deep(.ant-table-container) {
  padding: 0;
  margin: 0;
}

/* 设置最后一列甘特图区域的相对定位 */
:deep(.ant-table-thead > tr > th:last-child),
:deep(.ant-table-tbody > tr > td:last-child) {
  position: relative;
  overflow: visible;
  border: none !important; /* 确保甘特图区域没有边框 */
}

/* 移除表格边框和悬停效果 */
:deep(.ant-table-wrapper) {
  border: none !important;
}

:deep(.ant-table),
:deep(.ant-table-container) {
  border-radius: 0 !important;
}

/* 阻止鼠标事件处理 */
:deep(.ant-table) {
  pointer-events: auto !important;
}

/* 为基本信息列添加竖向边框 */
:deep(.ant-table-tbody > tr > td:nth-child(-n + 5)) {
  border-right: 1px solid #38618d !important;
}

/* 确保表头单元格没有边框 */
:deep(.ant-table-thead > tr > th) {
  border: none !important;
}

/* 为计划工期表头添加右侧边框 */
:deep(.ant-table-thead > tr > th:nth-child(5)) {
  border-right: 1px solid #4882c0 !important;
}

/* 时间线整体容器 */
.gantt-timeline {
  width: 100%;
  height: 100%;
  position: relative;
}

/* 月份分隔线 */
.month-divider {
  position: absolute;
  top: 0;
  height: 100%;
  width: 1px;
  /* background-color: rgba(255, 255, 255, 0.1); */
  z-index: 1;
  pointer-events: none;
}

/* 延期/提前指示器样式 */
.delay-indicator {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  height: 23px; /* 调整高度为23px */
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  text-overflow: ellipsis;
  z-index: 3;
}

/* 延期/提前文本样式 */
.delay-text {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  height: 23px; /* 调整高度为23px */
  display: flex;
  align-items: center;
  justify-content: flex-start;
  color: white;
  font-size: 12px;
  white-space: nowrap;
  overflow: visible;
  text-overflow: clip;
  font-weight: 500;
  z-index: 4;
  text-shadow: 0 0 2px rgba(0, 0, 0, 0.5);
  padding: 0 4px;
}

/* 移除表头圆角 */
:deep(.ant-table) {
  border-radius: 0 !important;
}

:deep(.ant-table-container) {
  border-radius: 0 !important;
}

:deep(.ant-table-thead > tr:first-child > th:first-child) {
  border-top-left-radius: 0 !important;
}

:deep(.ant-table-thead > tr:first-child > th:last-child) {
  border-top-right-radius: 0 !important;
}

/* 额外确保表格没有任何圆角 */
:deep(.ant-table-wrapper) {
  border-radius: 0 !important;
}

:deep(.ant-table-tbody > tr:last-child > td:first-child) {
  border-bottom-left-radius: 0 !important;
}

:deep(.ant-table-tbody > tr:last-child > td:last-child) {
  border-bottom-right-radius: 0 !important;
}

/* 调整表格单元格样式 */
:deep(.ant-table-cell) {
  padding: 0 !important;
  margin: 0 !important;
  height: 40px !important;
  height: 25px !important;
  line-height: 25px !important;
}

/* 确保基本信息列内容垂直居中和字体颜色 */
:deep(.ant-table-tbody > tr > td:not(:last-child)) {
  text-align: center;
  vertical-align: middle;
  padding: 0 8px;
  color: white !important; /* 确保字体颜色为白色 */
}

/* 甘特图标题行样式 */
.gantt-header-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 20px;
}

/* 移除表格边框和悬停效果 */
:deep(.ant-table-wrapper) {
  border: none !important;
}
</style>
