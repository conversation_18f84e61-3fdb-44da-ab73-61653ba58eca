<template>
  <div class="home-container">
    <div class="page-header">
      <div class="page-title">规划信息调整工作平台</div>
      <div class="divider"></div>
    </div>
    <div class="GridLayoutBox">
      <div class="draggable-elements">
        <a-button type="primary" @click="saveLayoutItem"
          >Primary Button</a-button
        >
        <div
          @drag="drag(item)"
          class="elementUi"
          @dragend="dragend(item)"
          draggable="true"
          unselectable="on"
          v-for="item in cmsList"
          :key="item.i"
        >
          {{ item.name || "--" }}
        </div>
      </div>
      <div id="content">
        <GridLayout
          class="GridLayout"
          :prevent-collision="true"
          :ref="setLayoutRef"
          v-model:layout="state.layout"
          :col-num="7"
          :row-height="30"
          :is-draggable="true"
          :is-resizable="false"
          :vertical-compact="true"
          :use-css-transforms="true"
        >
          <GridItem
            class="gridItem"
            v-for="item in state.layout"
            :key="item.i"
            :ref="(e) => setItemRef(item, e)"
            :x="item.x"
            :y="item.y"
            :w="item.w"
            :h="item.h"
            :i="item.i"
          >
            <span class="close" @click="delItem(item)">x</span>
            <component :is="item.componentName" :title="item.componentName" />
          </GridItem>
        </GridLayout>
      </div>
    </div>
  </div>
</template>
<script setup>
import { reactive, nextTick, ref } from "vue";
import { GridLayout, GridItem } from "vue-grid-layout-v3";
import { message } from "ant-design-vue";
const mouseXY = { x: null, y: null };
const DragPos = { x: null, y: null, w: 1, h: 1, i: null };
const cmsList = ref([
  {
    i: 1,
    name: "CMS1",
    w: 2,
    h: 16,
    componentName: "aboutIndex",
  },
  {
    i: 2,
    name: "CMS2",
    w: 2,
    h: 20,
    componentName: "addindex",
  },
  {
    i: 3,
    name: "CMS3",
    w: 2,
    h: 8,
    componentName: "delindex",
  },
  {
    i: 4,
    name: "CMS4",
    w: 1,
    h: 8,
    componentName: "plan2",
  },
]);
const state = reactive({
  layout: [],
  colNum: 12,
  layoutRef: {},
  itemRefs: {},
});

document.addEventListener(
  "dragover",
  (e) => {
    mouseXY.x = e.clientX;
    mouseXY.y = e.clientY;
  },
  false
);

async function drag(el) {
  const parentRect = document.getElementById("content").getBoundingClientRect();
  let mouseInGrid = false;
  if (
    mouseXY.x > parentRect.left &&
    mouseXY.x < parentRect.right &&
    mouseXY.y > parentRect.top &&
    mouseXY.y < parentRect.bottom
  ) {
    mouseInGrid = true;
  }
  if (
    mouseInGrid === true &&
    state.layout.findIndex((item) => item.i === "drop") === -1
  ) {
    state.layout.push({
      x: (state.layout.length * 2) % (state.colNum || 12),
      y: state.layout.length + (state.colNum || 12), // puts it at the bottom
      w: el.w,
      h: el.h,
      i: "drop",
    });
    await nextTick();
  }
  if (!state.itemRefs?.drop) {
    return;
  }
  const index = state.layout.findIndex((item) => item.i === "drop");
  if (index !== -1) {
    if (state.itemRefs?.drop?.el?.style) {
      state.itemRefs.drop.el.style.display = "none";
    }
    const itemRef = state.itemRefs.drop;
    const new_pos = itemRef.calcXY(
      mouseXY.y - parentRect.top,
      mouseXY.x - parentRect.left
    );
    if (mouseInGrid === true) {
      state.layoutRef.emitter.emit("dragEvent", [
        "dragstart",
        "drop",
        new_pos.x,
        new_pos.y,
        1,
        1,
      ]);
      DragPos.i = String(index);
      DragPos.x = state.layout[index].x;
      DragPos.y = state.layout[index].y;
    }
    if (mouseInGrid === false) {
      state.layoutRef.emitter.emit("dragEvent", [
        "dragend",
        "drop",
        new_pos.x,
        new_pos.y,
        1,
        1,
      ]);
      // 这里改了
      state.layout = state.layout.filter((obj) => obj.i !== "drop");
      await nextTick();
    }
  }
}

async function dragend(item) {
  const parentRect = document.getElementById("content").getBoundingClientRect();
  let mouseInGrid = false;
  if (
    mouseXY.x > parentRect.left &&
    mouseXY.x < parentRect.right &&
    mouseXY.y > parentRect.top &&
    mouseXY.y < parentRect.bottom
  ) {
    mouseInGrid = true;
  }
  if (mouseInGrid === true) {
    state.layoutRef.emitter.emit("dragEvent", [
      "dragend",
      "drop",
      DragPos.x,
      DragPos.y,
      1,
      1,
    ]);
    state.layout = state.layout.filter((obj) => obj.i !== "drop");

    if (!state.layout.some((el) => el.i === item.i)) {
      state.layout.push({
        x: DragPos.x,
        y: DragPos.y,
        w: item.w,
        h: item.h,
        i: item.i,
        componentName: item.componentName,
      });
      await nextTick();
      state.layoutRef.emitter.emit("dragEvent", [
        "dragend",
        DragPos.i,
        DragPos.x,
        DragPos.y,
        1,
        1,
      ]);
    } else {
      message.error("该组件已在容器中，请勿重复添加");
    }
  }
}
const delItem = async (item) => {
  let delIndex = state.layout.findIndex((el) => el.i === item.i);
  state.layout.splice(delIndex, 1);
};
function setItemRef(item, e) {
  state.itemRefs[item.i] = e;
  if (state.itemRefs[item.i]) {
  }
}
function setLayoutRef(e) {
  state.layoutRef = e;
}
const updateArrayWithDimensions = (arr) => {
  arr.forEach((item) => {
    const domElement = state.itemRefs[item.i].el;
    console.log(domElement);
    if (domElement) {
      item.width = domElement.clientWidth; // 获取宽度
      item.height = domElement.clientHeight; // 获取高度
    } else {
      console.log(`No DOM element found for key: ${item.i}`);
    }
  });
};

const saveLayoutItem = () => {
  updateArrayWithDimensions(state.layout);
  localStorage.setItem("layout", JSON.stringify(state.layout));
};
</script>

<style scoped lang="scss">
.home-container {
  padding: 16px;
}

.page-header {
  margin-bottom: 24px;
  text-align: center;
}

.page-title {
  font-size: 20px;
  font-weight: bold;
  color: #000000;
  margin-bottom: 8px;
}

.divider {
  height: 1px;
  background-color: #d9d9d9;
}

.GridLayoutBox {
  width: 100%;
  display: flex;
}
#content {
  width: 100%;
  height: 100vh;
  overflow: auto;
}
.GridLayout {
  width: 100%;
  height: 100vh !important;
  background-color: lightblue;
  overflow: auto;
}

.gridItem {
  width: 100%;
  height: 100%;
  border: 1px solid #250a0a;
}

.draggable-elements {
  width: 200px;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: lightgreen;
}
.close {
  display: inline-block;
  height: 16px;
  width: 16px;
  position: absolute;
  top: 10px;
  right: 10px;
  cursor: pointer;
  z-index: 100;
  color: rgba(252, 4, 4, 0.6);
  &:hover {
    color: #fff;
  }
}
.elementUi {
  width: 40px;
  height: 40px;
  background-color: #409eff;
  margin: 10px;
  border-radius: 5px;
  cursor: pointer;
}
</style>
