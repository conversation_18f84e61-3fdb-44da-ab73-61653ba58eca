<template>
  <div class="material-block material-block-1">
    <div class="title-section">
      <div class="title-icon">📦</div>
      <h3>物资配置模块1</h3>
      <div class="title-icon">📋</div>
    </div>

    <!-- 统计区域 -->
    <div class="stats-section">
      <div class="stat-item">
        <span class="stat-label">累计调配工单数</span>
        <span class="stat-value">{{ workOrderCount }}</span>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="chart-section">
      <div ref="chartContainer" class="chart-container"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from "vue";
import * as echarts from "echarts/core";
import { BarChart } from "echarts/charts";
import {
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
} from "echarts/components";
import { CanvasRenderer } from "echarts/renderers";

// 注册必要的组件
echarts.use([
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  BarChart,
  CanvasRenderer,
]);

// 模拟数据
const workOrderCount = ref(1250);

// 图表数据
const chartData = reactive({
  categories: ["Q2-0001", "Q2-0002", "Q2-0003", "Q2-0004", "Q2-0005"],
  values: [120, 200, 150, 80, 220],
});

// 图表实例
let chart: echarts.ECharts | null = null;
const chartContainer = ref<HTMLElement>();

// 初始化图表
const initChart = () => {
  if (!chartContainer.value) return;

  chart = echarts.init(chartContainer.value);

  const option = {
    backgroundColor: "transparent",
    tooltip: {
      trigger: "item",
      backgroundColor: "rgba(0, 0, 0, 0.8)",
      borderColor: "#37b1fe",
      textStyle: {
        color: "#fff",
      },
    },
    grid: {
      left: "3%",
      right: "3%",
      bottom: "3%",
      top: "3%",
      containLabel: true,
    },
    xAxis: {
      type: "value",
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        lineStyle: {
          color: "#11325b",
        },
      },
      axisLabel: {
        color: "#ccc",
        fontSize: 10,
      },
    },
    yAxis: {
      type: "category",
      data: chartData.categories,
      axisLine: {
        lineStyle: {
          color: "#0194f1",
        },
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        color: "#ccc",
        fontSize: 10,
      },
    },
    series: [
      {
        name: "物资配置",
        type: "bar",
        barWidth: "40%",
        itemStyle: {
          color: {
            type: "linear",
            x: 0,
            y: 0,
            x2: 1,
            y2: 0,
            colorStops: [
              {
                offset: 0,
                color: "rgba(72, 177, 244, 0)", // 起始位置透明
              },
              {
                offset: 0.9,
                color: "#48b1f4", // 蓝色
              },
              {
                offset: 1,
                color: "#ffffff", // 结束位置白色
              },
            ],
          },
          borderRadius: [0, 10, 10, 0], // 右侧圆头
        },
        data: chartData.values,
      },
    ],
  };

  chart.setOption(option);
};

// 处理窗口大小变化
const handleResize = () => {
  chart?.resize();
};

// 组件挂载时初始化图表
onMounted(() => {
  initChart();
  window.addEventListener("resize", handleResize);
});

// 组件卸载时清理资源
onUnmounted(() => {
  if (chart) {
    chart.dispose();
    chart = null;
  }
  window.removeEventListener("resize", handleResize);
});
</script>

<style scoped>
.material-block {
  /* background-color: #192347; */
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  border-radius: 4px;
  color: white;
  box-sizing: border-box;
}

.material-block-1 {
  border-color: #ff6b6b; /* 红色边框 */
}

.title-section {
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 4px;
}

.title-icon {
  font-size: 16px;
  color: #37b1fe;
  margin: 0 10px;
}

.title-section h3 {
  margin: 0;
  font-size: 16px;
  color: #03d0f4;
}

.stats-section {
  margin: 0 auto 15px auto;
  width: 80%;
  display: flex;
  justify-content: center;
}

.stat-item {
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  border: 1px solid #ff6b6b;
  border-radius: 4px;
  padding: 2px;
  width: 100%;
  overflow: hidden;
}

.stat-label {
  font-size: 16px;
  color: #ccc;
  flex-shrink: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.stat-value {
  font-size: 19px;
  font-weight: bold;
  color: #ff6b6b;
  flex-shrink: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.chart-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  /* padding: 10px; */
  border: 1px solid #ff6b6b;
}

.chart-container {
  flex: 1;
  width: 100%;
  border-radius: 4px;
}
</style>
