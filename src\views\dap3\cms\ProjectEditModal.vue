<template>
  <a-modal
    v-model:visible="visible"
    title="编辑"
    @ok="handleOk"
    @cancel="handleCancel"
    :maskClosable="false"
    width="1200px"
    :bodyStyle="{
      padding: '12px',
      maxHeight: 'calc(100vh - 180px)',
      overflow: 'auto',
    }"
  >
    <div class="project-sections">
      <!-- 第一块：核岛主要项目 -->
      <div class="project-section">
        <div class="section-header">
          <span class="section-title">一、核岛主要项目</span>
          <div class="action-buttons">
            <a-button type="primary" size="small" @click="prepareAddProject(0)"
              >新增</a-button
            >
          </div>
        </div>
        <a-table
          :dataSource="getProjectsByType(0)"
          :columns="columns"
          :pagination="false"
          size="small"
          bordered
          :scroll="{ y: 150 }"
        >
          <template #bodyCell="{ column, record, index }">
            <template v-if="column.dataIndex === 'sequence'">
              {{ index + 1 }}
            </template>
            <template v-if="column.dataIndex === 'name'">
              {{ record.routineName }}
            </template>
            <template v-if="column.dataIndex === 'remark'">
              {{ record.remark || "无" }}
            </template>
            <template v-if="column.dataIndex === 'action'">
              <a-space>
                <a-button
                  type="link"
                  size="small"
                  @click="handleEditItem(record)"
                  >修改</a-button
                >
                <a-popconfirm
                  title="确定要删除这个项目吗?"
                  ok-text="确定"
                  cancel-text="取消"
                  @confirm="confirmDeleteItem(record)"
                >
                  <a-button
                    type="link"
                    danger
                    size="small"
                  >删除</a-button>
                </a-popconfirm>
              </a-space>
            </template>
          </template>
          <template #emptyText>
            <div class="no-data">暂无数据</div>
          </template>
        </a-table>
      </div>

      <!-- 第二块：常规岛主要项目 -->
      <div class="project-section">
        <div class="section-header">
          <span class="section-title">二、常规岛主要项目</span>
          <div class="action-buttons">
            <a-button type="primary" size="small" @click="prepareAddProject(1)"
              >新增</a-button
            >
          </div>
        </div>
        <a-table
          :dataSource="getProjectsByType(1)"
          :columns="columns"
          :pagination="false"
          size="small"
          bordered
          :scroll="{ y: 150 }"
        >
          <template #bodyCell="{ column, record, index }">
            <template v-if="column.dataIndex === 'sequence'">
              {{ index + 1 }}
            </template>
            <template v-if="column.dataIndex === 'name'">
              {{ record.routineName }}
            </template>
            <template v-if="column.dataIndex === 'remark'">
              {{ record.remark || "无" }}
            </template>
            <template v-if="column.dataIndex === 'action'">
              <a-space>
                <a-button
                  type="link"
                  size="small"
                  @click="handleEditItem(record)"
                  >修改</a-button
                >
                <a-popconfirm
                  title="确定要删除这个项目吗?"
                  ok-text="确定"
                  cancel-text="取消"
                  @confirm="confirmDeleteItem(record)"
                >
                  <a-button
                    type="link"
                    danger
                    size="small"
                  >删除</a-button>
                </a-popconfirm>
              </a-space>
            </template>
          </template>
          <template #emptyText>
            <div class="no-data">暂无数据</div>
          </template>
        </a-table>
      </div>

      <!-- 第三块：技术改造的主要项目 -->
      <div class="project-section">
        <div class="section-header">
          <span class="section-title">三、技术改造的主要项目</span>
          <div class="action-buttons">
            <a-button type="primary" size="small" @click="prepareAddProject(2)"
              >新增</a-button
            >
          </div>
        </div>
        <a-table
          :dataSource="getProjectsByType(2)"
          :columns="columns"
          :pagination="false"
          size="small"
          bordered
          :scroll="{ y: 150 }"
        >
          <template #bodyCell="{ column, record, index }">
            <template v-if="column.dataIndex === 'sequence'">
              {{ index + 1 }}
            </template>
            <template v-if="column.dataIndex === 'name'">
              {{ record.routineName }}
            </template>
            <template v-if="column.dataIndex === 'remark'">
              {{ record.remark || "无" }}
            </template>
            <template v-if="column.dataIndex === 'action'">
              <a-space>
                <a-button
                  type="link"
                  size="small"
                  @click="handleEditItem(record)"
                  >修改</a-button
                >
                <a-popconfirm
                  title="确定要删除这个项目吗?"
                  ok-text="确定"
                  cancel-text="取消"
                  @confirm="confirmDeleteItem(record)"
                >
                  <a-button
                    type="link"
                    danger
                    size="small"
                  >删除</a-button>
                </a-popconfirm>
              </a-space>
            </template>
          </template>
          <template #emptyText>
            <div class="no-data">暂无数据</div>
          </template>
        </a-table>
      </div>
    </div>

    <template #footer>
      <a-button key="back" @click="handleCancel">取消</a-button>
      <a-button key="submit" type="primary" @click="handleOk">确定</a-button>
    </template>

    <!-- 编辑项目的弹窗 -->
    <a-modal
      v-model:visible="editModalVisible"
      title="编辑项目"
      @ok="confirmEdit"
      @cancel="cancelEdit"
      :maskClosable="false"
      cancelText="取消"
      okText="确定"
    >
      <div style="margin-bottom: 16px">
        <div style="margin-bottom: 8px">项目名称:</div>
        <a-input
          v-model:value="editFormData.name"
          placeholder="请输入项目名称"
        />
      </div>
      <div>
        <div style="margin-bottom: 8px">备注:</div>
        <a-input v-model:value="editFormData.remark" placeholder="请输入备注" />
      </div>
    </a-modal>

    <!-- 新增项目的弹窗 -->
    <a-modal
      v-model:visible="addModalVisible"
      :title="addModalTitle"
      @ok="confirmAdd"
      @cancel="cancelAdd"
      :maskClosable="false"
      cancelText="取消"
      okText="确定"
    >
      <div style="margin-bottom: 16px">
        <div style="margin-bottom: 8px">项目名称:</div>
        <a-input
          v-model:value="addFormData.name"
          placeholder="请输入项目名称"
        />
      </div>
      <div>
        <div style="margin-bottom: 8px">备注:</div>
        <a-input v-model:value="addFormData.remark" placeholder="请输入备注" />
      </div>
    </a-modal>
  </a-modal>
</template>

<script setup>
import { ref, onMounted, computed } from "vue";
import { Modal, message } from "ant-design-vue";

// 控制弹窗显示/隐藏
const visible = ref(false);
// 控制编辑弹窗显示/隐藏
const editModalVisible = ref(false);
// 控制新增弹窗显示/隐藏
const addModalVisible = ref(false);
// 临时存储编辑项目的数据
const editFormData = ref({
  name: "",
  remark: "",
  id: "",
  gridType: -1,
});
// 临时存储新增项目的数据
const addFormData = ref({
  name: "",
  remark: "",
  gridType: -1,
});

// 当前需要添加的项目类型
const currentGridType = ref(null);

// 新增弹窗标题
const addModalTitle = ref("");

// 表格列定义
const columns = [
  {
    title: "序号",
    dataIndex: "sequence",
    key: "sequence",
    width: 60,
    align: "center",
  },
  {
    title: "项目名称",
    dataIndex: "name",
    key: "name",
  },
  {
    title: "备注",
    dataIndex: "remark",
    key: "remark",
    width: "30%",
  },
  {
    title: "操作",
    dataIndex: "action",
    key: "action",
    width: 120,
    align: "center",
    fixed: "right",
  },
];

// 项目数据 - 统一保存所有gridType的项目
const allProjects = ref([]);

// 根据gridType过滤项目
const getProjectsByType = (gridType) => {
  return allProjects.value.filter((item) => item.gridType === gridType);
};

// 初始化时加载默认数据
onMounted(() => {
  // 这里模拟初始数据，实际项目中可能从API获取
  allProjects.value = [
    // 核岛主要项目 (gridType: 0)
    { id: "1", routineName: "主泵A/B轴承箱解体", remark: "提前排期", gridType: 0 },
    {
      id: "2",
      routineName: "停冷热交换器B解体",
      remark: "需解决问题: 控制冷温问题",
      gridType: 0,
    },
    { id: "3", routineName: "蓄能器检修", remark: "A系列优先", gridType: 0 },
    { id: "4", routineName: "储气槽压力维护", remark: "更换阀门", gridType: 0 },
    { id: "5", routineName: "主蒸汽母管检修", remark: "配合外部单位", gridType: 0 },
    {
      id: "6",
      routineName: "一回路水化学维护",
      remark: "安排技术人员培训",
      gridType: 0,
    },
    {
      id: "7",
      routineName: "安全注射系统管道检修",
      remark: "需准备大量备件",
      gridType: 0,
    },
    {
      id: "8",
      routineName: "稳压器喷淋管线维护",
      remark: "按计划执行",
      gridType: 0,
    },

    // 常规岛主要项目 (gridType: 1)
    { id: "9", routineName: "凝汽器管道检修", remark: "需重点关注", gridType: 1 },
    { id: "10", routineName: "低压缸更换", remark: "配合外部单位", gridType: 1 },
    { id: "11", routineName: "汽轮机叶片更换", remark: "技术难度高", gridType: 1 },
    { id: "12", routineName: "给水泵检修", remark: "需准备备件", gridType: 1 },
    { id: "13", routineName: "冷凝水管道清洗", remark: "停机进行", gridType: 1 },
    { id: "14", routineName: "主变压器维护", remark: "外部单位配合", gridType: 1 },
    { id: "15", routineName: "电动给水泵维护", remark: "系统优化", gridType: 1 },
    { id: "16", routineName: "主蒸汽管道检修", remark: "高温部件", gridType: 1 },
    { id: "17", routineName: "凝汽器真空系统优化", remark: "提高效率", gridType: 1 },

    // 技术改造的主要项目 (gridType: 2)
    { id: "18", routineName: "安全注射泵D改造", remark: "提高性能", gridType: 2 },
    { id: "19", routineName: "监控系统升级", remark: "新增摄像头位置", gridType: 2 },
    {
      id: "20",
      routineName: "辐射监测系统改造",
      remark: "更换先进设备",
      gridType: 2,
    },
    {
      id: "21",
      routineName: "数字化控制系统升级",
      remark: "提升自动化程度",
      gridType: 2,
    },
    {
      id: "22",
      routineName: "应急柴油发电机组升级",
      remark: "提高可靠性",
      gridType: 2,
    },
    {
      id: "23",
      routineName: "控制室人机界面改造",
      remark: "优化操作体验",
      gridType: 2,
    },
    {
      id: "24",
      routineName: "主控制室显示系统升级",
      remark: "4K显示器",
      gridType: 2,
    },
    {
      id: "25",
      routineName: "消防系统改造",
      remark: "新增自动灭火装置",
      gridType: 2,
    },
  ];
});

// 显示弹窗的方法
const showModal = (gridType = null) => {
  // 设置当前选择的gridType
  currentGridType.value = gridType;
  visible.value = true;
};

// 确定按钮处理
const handleOk = () => {
  // 这里可以保存数据到后端
  console.log("保存项目数据:", allProjects.value);

  // 将数据转换为父组件期望的格式
  const outputData = allProjects.value.map((item) => ({
    id: item.id,
    routineName: item.routineName,
    gridType: item.gridType,
  }));

  // 发出更新事件
  emit("update", outputData);

  message.success("保存成功");
  visible.value = false;
};

// 定义emit
const emit = defineEmits(["update"]);

// 取消按钮处理
const handleCancel = () => {
  visible.value = false;
};

// 统一的项目编辑方法
const handleEditItem = (record) => {
  console.log("编辑项目ID:", record.id);

  editFormData.value = {
    name: record.routineName,
    remark: record.remark || "",
    id: record.id,
    gridType: record.gridType,
  };

  editModalVisible.value = true;
};

// 确认编辑
const confirmEdit = () => {
  if (editFormData.value.name.trim() === "") {
    message.error("项目名称不能为空");
    return;
  }

  // 打印编辑的数据
  console.log("确认编辑项目:", {
    id: editFormData.value.id,
    routineName: editFormData.value.name,
    remark: editFormData.value.remark,
    gridType: editFormData.value.gridType,
  });

  message.success("修改成功");
  editModalVisible.value = false;
};

// 取消编辑
const cancelEdit = () => {
  editModalVisible.value = false;
};

// 确认删除项目
const confirmDeleteItem = (record) => {
  console.log("确认删除项目ID:", record.id);
  message.success("删除成功");
};

// 获取项目类型名称
const getSectionName = (gridType) => {
  switch (gridType) {
    case 0:
      return "核岛";
    case 1:
      return "常规岛";
    case 2:
      return "技术改造";
    default:
      return "";
  }
};

// 新增项目前的准备
const prepareAddProject = (gridType) => {
  addFormData.value = {
    name: "",
    remark: "",
    gridType: gridType,
  };

  // 更新弹窗标题
  addModalTitle.value = `新增${getSectionName(gridType)}项目`;

  addModalVisible.value = true;
};

// 确认新增
const confirmAdd = () => {
  const { gridType, name, remark } = addFormData.value;

  if (name.trim() === "") {
    message.error("项目名称不能为空");
    return;
  }

  // 打印新增的数据
  console.log("新增项目:", {
    routineName: name,
    remark,
    gridType,
  });

  message.success("新增成功");
  addModalVisible.value = false;
};

// 取消新增
const cancelAdd = () => {
  addModalVisible.value = false;
};

// 暴露方法给父组件调用
defineExpose({
  showModal,
});
</script>

<style scoped>
.project-sections {
  height: 600px;
  overflow-y: auto;
  padding: 0 10px;
}

/* Add max-height to the main modal */
:deep(.ant-modal-body) {
  max-height: calc(100vh - 200px);
  overflow-y: auto;
}

/* Make each project section more compact */
.project-section {
  margin-bottom: 15px;
  background-color: #fff;
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 15px;
  background: #ffffff;
  border-bottom: 1px solid #e8e8e8;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.section-title {
  font-weight: bold;
  color: #000000;
  font-size: 15px;
  position: relative;
  padding-left: 12px;
}

.section-title::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 60%;
  background: #1890ff;
  border-radius: 2px;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.no-data {
  padding: 20px;
  text-align: center;
  color: #999;
}

/* 自定义滚动条样式 */
.project-sections::-webkit-scrollbar {
  width: 6px;
}

.project-sections::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.project-sections::-webkit-scrollbar-thumb {
  background-color: #c1c1c1;
  border-radius: 3px;
  border: 2px solid #f1f1f1;
}

.project-sections::-webkit-scrollbar-thumb:hover {
  background-color: #a1a1a1;
}
</style>
