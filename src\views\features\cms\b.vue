<template>
  <div class="container">
    <div class="header">
      <div class="main-title">年度配置管理</div>
    </div>
    <!-- 内容区域 -->
    <div class="content">
      <div class="section">
        <div class="section-title">Q2-0T412 参考时间</div>
        <div class="time-section-container">
          <div class="time-table-container">
            <a-table
              :columns="dynamicColumns"
              :data-source="tableData"
              :pagination="false"
              bordered
              size="middle"
              :scroll="{ y: 400 }"
            >
            </a-table>
          </div>
          <div class="time-action-container">
            <div class="action-block" @click="handleManageTask">
              <div class="action-title">大修任务管理</div>
              <div class="action-icon">
                <i class="action-arrow">→</i>
              </div>
            </div>
            <div class="action-block" @click="handleManageAlgorithm">
              <div class="action-title">参考时间算法管理</div>
              <div class="action-icon">
                <i class="action-arrow">→</i>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 新增人员来源部分 -->
      <div class="section">
        <div class="section-title">人员来源</div>
        <div class="filter-container">
          <div class="filter-item">
            <span class="filter-label">处室名称：</span>
            <a-select
              v-model:value="departmentValue"
              placeholder="请选择处室"
              style="width: 200px"
              allowClear
              @change="handleDepartmentChange"
            >
              <a-select-option
                v-for="item in departmentOptions"
                :key="item.value"
                :value="item.value"
              >
                {{ item.label }}
              </a-select-option>
            </a-select>
          </div>

          <div class="filter-item">
            <span class="filter-label">科室名称：</span>
            <a-select
              v-model:value="officeValue"
              placeholder="请选择科室"
              style="width: 200px"
              allowClear
              @change="handleOfficeChange"
            >
              <a-select-option
                v-for="item in officeOptions"
                :key="item.value"
                :value="item.value"
              >
                {{ item.label }}
              </a-select-option>
            </a-select>
          </div>

          <div class="filter-item">
            <span class="filter-label">班组名称：</span>
            <a-select
              v-model:value="teamValue"
              placeholder="请选择班组"
              style="width: 200px"
              allowClear
              @change="handleTeamChange"
            >
              <a-select-option
                v-for="item in teamOptions"
                :key="item.value"
                :value="item.value"
              >
                {{ item.label }}
              </a-select-option>
            </a-select>
          </div>

          <!-- 添加人员来源查询按钮 -->
          <div class="filter-item">
            <a-button type="primary" @click="handlePersonnelSearch">
              查询
            </a-button>
          </div>
        </div>

        <!-- 包含多个名字的名字框 -->
        <div class="names-container">
          <div class="names-box">
            <div class="names-list">
              <!-- 按部门分组显示人员，每行一个部门 -->
              <div
                v-for="department in nameList"
                :key="department.baseName"
                class="department-row"
              >
                <div class="department-title">{{ department.baseName }}:</div>
                <div class="department-members">
                  <div
                    v-for="member in department.data"
                    :key="member.staffAccount"
                    class="member-wrapper"
                  >
                    <span
                      class="name-tag"
                      draggable="true"
                      @dragstart="
                        onDragStart($event, member, department.baseName)
                      "
                    >
                      <div class="ellipsis-text">
                        {{
                          member.staffName.length > 10
                            ? member.staffName.substring(0, 10) + "..."
                            : member.staffName
                        }}
                      </div>
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 新增复杂表格部分 -->
      <div class="section">
        <div class="section-title">任务管理</div>

        <!-- 添加表格上方的过滤器 -->
        <div class="filter-container">
          <div class="filter-item">
            <span class="filter-label">大修编号：</span>
            <a-select
              v-model:value="selectedRepairCodes"
              placeholder="请选择大修编号"
              style="width: 350px"
              mode="multiple"
              allowClear
            >
              <a-select-option
                v-for="codeInfo in allRepairCodes"
                :key="codeInfo.code"
                :value="codeInfo.code"
              >
                {{ codeInfo.code }}
              </a-select-option>
            </a-select>
          </div>

          <div class="filter-item">
            <a-button type="primary" @click="handleTableSearch">
              查询
            </a-button>
          </div>
        </div>

        <a-table
          :columns="complexTableColumns"
          :data-source="complexTableData"
          :pagination="false"
          :bordered="true"
          size="middle"
          :scroll="{ x: 'max-content' }"
          :customRow="customRow"
        >
          <!-- 人员插槽 -->
          <template #personnel="{ text, record }">
            <template v-if="text && text !== '\\'">
              <a-popover title="人员列表" trigger="hover" placement="top">
                <template #content>
                  <div class="personnel-list">
                    <div
                      v-for="(name, index) in getPersonnelArray(text)"
                      :key="index"
                      class="personnel-item"
                    >
                      <span class="personnel-name">{{ name }}</span>
                      <a-button
                        type="link"
                        danger
                        size="small"
                        class="delete-btn"
                        @click="handleDeletePersonnel(record, name, index)"
                      >
                        删除
                      </a-button>
                    </div>
                  </div>
                </template>
                <div class="ellipsis">
                  {{ text.length > 20 ? text.substring(0, 20) + "..." : text }}
                </div>
              </a-popover>
            </template>
            <template v-else>
              {{ text }}
            </template>
          </template>

          <!-- 到岗时间插槽 -->
          <template #startTime="{ text, record, column }">
            <template v-if="text && text !== '\\'">
              <a-date-picker
                :value="text ? dayjs(text) : null"
                :allowClear="false"
                size="middle"
                style="width: 100%; min-width: 120px"
                placeholder="请选择到岗时间"
                :bordered="true"
                @change="
                  (date, dateString) =>
                    handleDateChange(date, dateString, record, column.dataIndex)
                "
                format="YYYY-MM-DD"
              />
            </template>
            <template v-else>
              {{ text }}
            </template>
          </template>

          <!-- 离岗时间插槽 -->
          <template #endTime="{ text, record, column }">
            <template v-if="text && text !== '\\'">
              <a-date-picker
                :value="text ? dayjs(text) : null"
                :allowClear="false"
                size="middle"
                style="width: 100%; min-width: 120px"
                placeholder="请选择离岗时间"
                :bordered="true"
                @change="
                  (date, dateString) =>
                    handleDateChange(date, dateString, record, column.dataIndex)
                "
                format="YYYY-MM-DD"
              />
            </template>
            <template v-else>
              {{ text }}
            </template>
          </template>
        </a-table>
      </div>
    </div>

    <!-- 引入任务管理模态框组件 -->
    <TaskManageModal ref="taskModalRef" />

    <!-- 引入算法管理模态框组件 -->
    <AlgorithmManageModal ref="algorithmModalRef" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, h, onMounted, onUnmounted } from "vue";
import TaskManageModal from "./cms/TaskManageModal.vue";
import AlgorithmManageModal from "./cms/AlgorithmManageModal.vue";
import dayjs from "dayjs";

// 模态框引用
const taskModalRef = ref(null);
const algorithmModalRef = ref(null);

// 动态表头数据
const headerItems = ref([
  {
    name: "Q1季度",
    beginDate: "2023-01-01",
    endDate: "2023-03-31",
    algorithm: "A1算法",
  },
  {
    name: "Q2季度",
    beginDate: "2023-04-01",
    endDate: "2023-06-30",
    algorithm: "A2算法",
  },
  {
    name: "Q3季度",
    beginDate: "2023-07-01",
    endDate: "2023-09-30",
    algorithm: "B1算法",
  },
  {
    name: "Q3季度",
    beginDate: "2023-07-01",
    endDate: "2023-09-30",
    algorithm: "B1算法",
  },
  {
    name: "Q3季度",
    beginDate: "2023-07-01",
    endDate: "2023-09-30",
    algorithm: "B1算法",
  },
  {
    name: "Q3季度",
    beginDate: "2023-07-01",
    endDate: "2023-09-30",
    algorithm: "B1算法",
  },
  {
    name: "Q3季度",
    beginDate: "2023-07-01",
    endDate: "2023-09-30",
    algorithm: "B1算法",
  },
  {
    name: "Q3季度",
    beginDate: "2023-07-01",
    endDate: "2023-09-30",
    algorithm: "B1算法",
  },
  {
    name: "Q4季度",
    beginDate: "2023-10-01",
    endDate: "2023-12-31",
    algorithm: "B2算法",
  },
]);

// 生成动态列
const dynamicColumns = computed(() => {
  // 固定第一列为阶段名称
  const columns = [
    {
      title: "阶段名称",
      dataIndex: "stageName",
      key: "stageName",
      width: 120,
      fixed: "left",
      align: "center",
    },
  ];

  // 根据headerItems动态添加列
  headerItems.value.forEach((item) => {
    columns.push({
      title: item.name,
      dataIndex: item.name,
      key: item.name,
      width: 120,
      align: "center",
      fixed: undefined,
    });
  });

  return columns;
});

// 表格数据 - 固定三行：开始时间、结束时间、算法
const tableData = computed(() => {
  // 创建开始时间行
  const startTimeRow = {
    key: "beginDate",
    stageName: "开始时间",
  };

  // 创建结束时间行
  const endTimeRow = {
    key: "endDate",
    stageName: "结束时间",
  };

  // 创建算法行
  const algorithmRow = {
    key: "algorithm",
    stageName: "算法",
  };

  // 为每一行添加数据
  headerItems.value.forEach((item) => {
    startTimeRow[item.name] = item.beginDate;
    endTimeRow[item.name] = item.endDate;
    algorithmRow[item.name] = item.algorithm;
  });

  return [startTimeRow, endTimeRow, algorithmRow];
});

// 人员来源下拉框数据
const departmentValue = ref<string>("");
const officeValue = ref<string>("");
const teamValue = ref<string>("");

// 表格过滤数据
const selectedRepairCodes = ref<string[]>([]);

// 处室选项 - 初始数据
const departmentOptions = ref([
  { value: "dept1", label: "运行处" },
  { value: "dept2", label: "技术处" },
  { value: "dept3", label: "安全处" },
]);

// 科室选项 - 将会根据选择的处室动态变化
const officeOptions = ref([
  { value: "office1", label: "生产科" },
  { value: "office2", label: "管理科" },
  { value: "office3", label: "规划科" },
]);

// 班组选项 - 将会根据选择的科室动态变化
const teamOptions = ref([
  { value: "team1", label: "甲班组" },
  { value: "team2", label: "乙班组" },
  { value: "team3", label: "丙班组" },
]);

// 修改人员姓名列表 - 改为直接数组对象结构
const nameList = ref([
  {
    baseName: "招商部",
    data: [
      { staffAccount: "ZS001", staffName: "张三" },
      { staffAccount: "ZS002", staffName: "李四" },
      { staffAccount: "ZS003", staffName: "王五" },
      { staffAccount: "ZS004", staffName: "赵六" },
      { staffAccount: "ZS005", staffName: "钱七" },
      { staffAccount: "ZS006", staffName: "孙八" },
      { staffAccount: "ZS007", staffName: "周九" },
      { staffAccount: "ZS008", staffName: "吴十" },
    ],
  },
  {
    baseName: "采购部",
    data: [
      { staffAccount: "CG001", staffName: "郑十一" },
      { staffAccount: "CG002", staffName: "王十二" },
      { staffAccount: "CG003", staffName: "冯十三" },
      { staffAccount: "CG004", staffName: "陈十四" },
      { staffAccount: "CG005", staffName: "楚十五" },
      { staffAccount: "CG006", staffName: "魏十六" },
    ],
  },
  {
    baseName: "生产部",
    data: [
      { staffAccount: "SC001", staffName: "刘一" },
      { staffAccount: "SC002", staffName: "陈二" },
      { staffAccount: "SC003", staffName: "黄大仙" },
      { staffAccount: "SC004", staffName: "朱小明" },
      { staffAccount: "SC005", staffName: "陆小凤" },
    ],
  },
  {
    baseName: "技术部",
    data: [
      { staffAccount: "JS001", staffName: "高山" },
      { staffAccount: "JS002", staffName: "林海" },
      { staffAccount: "JS003", staffName: "江山" },
      { staffAccount: "JS004", staffName: "谢雨" },
      { staffAccount: "JS005", staffName: "杜鹃" },
    ],
  },
  {
    baseName: "保障部",
    data: [
      { staffAccount: "BZ001", staffName: "马超" },
      { staffAccount: "BZ002", staffName: "刘备" },
      { staffAccount: "BZ003", staffName: "关羽" },
      { staffAccount: "BZ004", staffName: "张飞" },
      { staffAccount: "BZ005", staffName: "诸葛亮" },
      { staffAccount: "BZ006", staffName: "曹操" },
    ],
  },
]);

// 定义大修项目类型接口
interface OverhaulCodeItem {
  overhaulCode: string;
  id?: any; // 可选ID字段
  realArriveTime: string;
  realLeaveTime: string;
  staff: Array<{ staffAccount: string; staffName: string }>;
  startTime: string;
  endTime: string;
}

// 定义任务类型接口
interface TaskItem {
  taskName: string;
  types: string;
  totalNumber: string;
  annualConfigTaskOverhaulCodeVOS: OverhaulCodeItem[];
}

// 实际任务数据
const res = ref<TaskItem[]>([
  {
    taskName: "设备检修",
    types: "着急",
    totalNumber: "10",
    annualConfigTaskOverhaulCodeVOS: [
      {
        overhaulCode: "QX-0099",
        id: "1001", // 添加ID字段
        realArriveTime: "2022-10-25",
        realLeaveTime: "2023-01-09",
        staff: [
          { staffAccount: "ZS001", staffName: "张三" },
          { staffAccount: "ZS002", staffName: "李四" },
        ],
        startTime: "2022-90-25",
        endTime: "2022-12-06",
      },
      {
        overhaulCode: "Q2-OT011",
        id: "1002", // 添加ID字段
        realArriveTime: "2022-10-25",
        realLeaveTime: "2023-01-09",
        staff: [
          { staffAccount: "ZS001", staffName: "张三" },
          { staffAccount: "ZS003", staffName: "王五" },
          { staffAccount: "ZS002", staffName: "李四" },
        ],
        startTime: "2022-90-25",
        endTime: "2022-12-06",
      },
    ],
  },

  {
    taskName: "设备检修2",
    types: "着急",
    totalNumber: "10",
    annualConfigTaskOverhaulCodeVOS: [
      {
        overhaulCode: "QX-0012",
        id: "1003", // 添加ID字段
        realArriveTime: "2022-10-25",
        realLeaveTime: "2023-01-09",
        staff: [
          { staffAccount: "ZS001", staffName: "张三" },
          { staffAccount: "ZS002", staffName: "李四" },
        ],
        startTime: "2022-90-25",
        endTime: "2022-12-06",
      },
      {
        overhaulCode: "QX-0005",
        id: "1004", // 添加ID字段
        realArriveTime: "2022-10-25",
        realLeaveTime: "2023-01-09",
        staff: [
          { staffAccount: "ZS001", staffName: "张三" },
          { staffAccount: "ZS003", staffName: "王五" },
          { staffAccount: "ZS002", staffName: "李四" },
        ],
        startTime: "2022-90-25",
        endTime: "2022-12-06",
      },
    ],
  },

  {
    taskName: "设备检修9",
    types: "着急",
    totalNumber: "10",
    annualConfigTaskOverhaulCodeVOS: [
      {
        overhaulCode: "QX-00139",
        id: "1005", // 添加ID字段
        realArriveTime: "2022-10-25",
        realLeaveTime: "2023-01-09",
        staff: [
          { staffAccount: "ZS009", staffName: "张三2" },
          { staffAccount: "ZS010", staffName: "李四2" },
        ],
        startTime: "2022-90-25",
        endTime: "2022-12-06",
      },
      {
        overhaulCode: "QX-00121",
        id: "1006", // 添加ID字段
        realArriveTime: "2022-10-25",
        realLeaveTime: "2023-01-09",
        staff: [
          { staffAccount: "ZS001", staffName: "张三" },
          { staffAccount: "ZS003", staffName: "王五" },
          { staffAccount: "ZS002", staffName: "李四" },
        ],
        startTime: "2022-90-25",
        endTime: "2022-12-06",
      },
    ],
  },

  {
    taskName: "设备检修4",
    types: "着急",
    totalNumber: "10",
    annualConfigTaskOverhaulCodeVOS: [
      {
        overhaulCode: "QX-00111",
        id: "1007", // 添加ID字段
        realArriveTime: "2022-10-25",
        realLeaveTime: "2023-01-09",
        staff: [],
        startTime: "2022-90-25",
        endTime: "2022-12-06",
      },
      {
        overhaulCode: "QX-0011",
        id: "1008", // 添加ID字段
        realArriveTime: "2022-10-25",
        realLeaveTime: "2023-01-09",
        staff: [
          { staffAccount: "ZS001", staffName: "张三" },
          { staffAccount: "ZS003", staffName: "王五" },
          { staffAccount: "ZS002", staffName: "李四" },
        ],
        startTime: "2022-90-25",
        endTime: "2022-12-06",
      },
    ],
  },

  {
    taskName: "设备检修5",
    types: "着急",
    totalNumber: "10",
    annualConfigTaskOverhaulCodeVOS: [
      {
        overhaulCode: "QX-00139",
        id: "1009", // 添加ID字段
        realArriveTime: "2022-10-25",
        realLeaveTime: "2023-01-09",
        staff: [
          { staffAccount: "ZS009", staffName: "张三2" },
          { staffAccount: "ZS010", staffName: "李四2" },
        ],
        startTime: "2022-90-25",
        endTime: "2022-12-06",
      },
      {
        overhaulCode: "QX-0011",
        id: "1010", // 添加ID字段
        realArriveTime: "2022-10-25",
        realLeaveTime: "2023-01-09",
        staff: [
          { staffAccount: "ZS001", staffName: "张三" },
          { staffAccount: "ZS003", staffName: "王五" },
          { staffAccount: "ZS002", staffName: "李四" },
          { staffAccount: "ZS003", staffName: "王五" },
          { staffAccount: "ZS004", staffName: "赵六" },
          { staffAccount: "ZS011", staffName: "osadada" },
        ],
        startTime: "2022-90-25",
        endTime: "2022-12-06",
      },
    ],
  },
]);

// 从实际数据中提取所有唯一的大修编号和ID组合
const getUniqueRepairCodesWithId = () => {
  const uniqueCodeItems = new Map<string, Set<any>>();

  res.value.forEach((task) => {
    if (
      Array.isArray(task.annualConfigTaskOverhaulCodeVOS) &&
      task.annualConfigTaskOverhaulCodeVOS.length > 0
    ) {
      task.annualConfigTaskOverhaulCodeVOS.forEach((item) => {
        if (item.overhaulCode) {
          if (!uniqueCodeItems.has(item.overhaulCode)) {
            uniqueCodeItems.set(item.overhaulCode, new Set());
          }
          if (item.id) {
            uniqueCodeItems.get(item.overhaulCode)?.add(item.id);
          }
        }
      });
    }
  });

  // 转换为对象数组，每个元素包含code和id
  const result: { code: string; id?: any }[] = [];
  uniqueCodeItems.forEach((idSet, code) => {
    // 修改这里: 不再为每个ID创建独立项，而是只创建一个代表大修编号的项
    result.push({ code });
  });

  return result;
};

// 根据大修编号和ID生成唯一的列标识符
const getColumnKey = (code: string, id?: any) => {
  // 修改这里: 不再使用ID作为列标识的一部分
  return code;
};

// 大修编号数据 - 仅用于下拉选择框
const allRepairCodes = [
  { code: "QX-0099" },
  { code: "QX-0011" },
  { code: "QX-0012" },
  { code: "QX-0005" },
  { code: "QX-00139" },
  { code: "QX-00121" },
];

// 复杂表格 - 基础列配置（左侧固定部分）
const baseColumns = [
  {
    title: "序号",
    dataIndex: "index",
    key: "index",
    width: 60,
    fixed: "left",
    align: "center",
  },
  {
    title: "任务名称",
    dataIndex: "taskName",
    key: "taskName",
    width: 150,
    fixed: "left",
  },
  {
    title: "任务属性",
    dataIndex: "taskType",
    key: "taskType",
    width: 100,
    fixed: "left",
    align: "center",
  },
  {
    title: "标准人数",
    dataIndex: "standardCount",
    key: "standardCount",
    width: 100,
    fixed: "left",
    align: "center",
  },
];

// 动态生成表格列
const complexTableColumns = computed(() => {
  const columns = [];
  const uniqueCodeItems = getUniqueRepairCodesWithId();

  // 创建左侧"大修编号"主列，并将基础列作为其子列
  columns.push({
    title: "大修编号",
    children: [
      {
        title: "序号",
        dataIndex: "index",
        key: "index",
        width: 60,
        fixed: "left",
        align: "center",
      },
      {
        title: "任务名称",
        dataIndex: "taskName",
        key: "taskName",
        width: 150,
        fixed: "left",
      },
      {
        title: "任务属性",
        dataIndex: "taskType",
        key: "taskType",
        width: 100,
        fixed: "left",
        align: "center",
      },
      {
        title: "标准人数",
        dataIndex: "standardCount",
        key: "standardCount",
        width: 100,
        fixed: "left",
        align: "center",
      },
    ],
  });

  // 只有当有大修编号时才添加对应列
  if (uniqueCodeItems.length > 0) {
    // 为每个大修编号创建一列，每列下有到岗时间、离岗时间和人员三个子列
    uniqueCodeItems.forEach((item) => {
      const { code } = item;
      const columnKey = getColumnKey(code);
      const displayTitle = code;

      columns.push({
        title: displayTitle,
        children: [
          {
            title: "到岗时间",
            dataIndex: `${columnKey}_startTime`,
            key: `${columnKey}_startTime`,
            width: 150,
            align: "center",
            slots: { customRender: "startTime" },
          },
          {
            title: "离岗时间",
            dataIndex: `${columnKey}_endTime`,
            key: `${columnKey}_endTime`,
            width: 150,
            align: "center",
            slots: { customRender: "endTime" },
          },
          {
            title: "人员",
            dataIndex: `${columnKey}_personnel`,
            key: `${columnKey}_personnel`,
            width: 120,
            align: "center",
            slots: { customRender: "personnel" },
          },
        ],
      });
    });
  }

  return columns;
});

// 构建表格数据
const complexTableData = computed(() => {
  const uniqueCodeItems = getUniqueRepairCodesWithId();

  return res.value.map((task, index) => {
    // 基础行数据
    const row = {
      key: String(index),
      index: index + 1,
      taskName: task.taskName,
      taskType: task.types,
      standardCount: task.totalNumber,
    };

    // 只有当任务有列表且不为空时，才添加大修编号相关数据
    if (
      Array.isArray(task.annualConfigTaskOverhaulCodeVOS) &&
      task.annualConfigTaskOverhaulCodeVOS.length > 0
    ) {
      // 处理每个大修编号
      uniqueCodeItems.forEach((item) => {
        const { code } = item;
        const columnKey = getColumnKey(code);

        // 查找当前任务中是否有匹配的大修编号
        // 修改这里: 收集所有相同大修编号的项目
        const matchItems = task.annualConfigTaskOverhaulCodeVOS.filter(
          (taskItem) => taskItem.overhaulCode === code
        );

        if (matchItems.length > 0) {
          // 如果找到匹配项，使用第一个匹配项的时间数据
          row[`${columnKey}_startTime`] = matchItems[0].realArriveTime;
          row[`${columnKey}_endTime`] = matchItems[0].realLeaveTime;

          // 合并所有匹配项的人员数据
          const allStaff = matchItems.reduce((acc: any[], item) => {
            if (Array.isArray(item.staff)) {
              // 使用Map去重，避免同一人员出现多次
              const staffMap = new Map();
              item.staff.forEach((s: any) => {
                const key = s.staffAccount || s.staffName;
                if (!staffMap.has(key)) {
                  staffMap.set(key, s);
                }
              });

              acc.push(...Array.from(staffMap.values()));
            }
            return acc;
          }, []);

          if (allStaff.length > 0) {
            row[`${columnKey}_personnel`] = allStaff
              .map((s: any) => s.staffName)
              .join(", ");
          } else {
            // 修改这里: 当有时间但人员为空时，显示空字符串而不是反斜杠
            row[`${columnKey}_personnel`] = "";
          }
        } else {
          // 如果没有找到匹配项，使用反斜杠代替空值
          row[`${columnKey}_startTime`] = "\\";
          row[`${columnKey}_endTime`] = "\\";
          row[`${columnKey}_personnel`] = "\\";
        }
      });
    } else {
      // 如果list为空，也为所有大修编号列添加反斜杠
      uniqueCodeItems.forEach((item) => {
        const { code } = item;
        const columnKey = getColumnKey(code);

        row[`${columnKey}_startTime`] = "\\";
        row[`${columnKey}_endTime`] = "\\";
        row[`${columnKey}_personnel`] = "\\";
      });
    }

    return row;
  });
});

// 添加拖拽相关状态
const draggingName = ref("");
const draggingMember = ref(null);

// 拖拽开始处理
const onDragStart = (event: DragEvent, member: any, departmentName: string) => {
  if (event.dataTransfer) {
    // 设置member对象，保留部门信息
    const memberWithDept = {
      ...member,
      departmentName,
    };

    // 将完整对象转为JSON字符串传递
    event.dataTransfer.setData("text/plain", JSON.stringify(memberWithDept));
    draggingName.value = member.staffName;
    draggingMember.value = memberWithDept;
  }
};

// 自定义行属性，处理单元格点击
const customRow = (record: any) => {
  return {
    onClick: (event: MouseEvent) => {
      // 获取被点击的单元格
      const target = event.target as HTMLElement;
      const cell = target.closest("td");
      if (!cell) return;

      // 从复杂表格数据源中获取任务名称
      const taskName = record.taskName;

      // 获取单元格的列索引
      const cellIndex = Array.from(cell.parentNode?.children || []).indexOf(
        cell
      );

      // 从列索引判断对应的大修编号和列类型
      const { repairCode, repairId, columnType } =
        getColumnInfoFromIndex(cellIndex);

      // 如果是人员列，并且有拖拽的人员
      if (columnType === "personnel" && repairCode && draggingMember.value) {
        // 使用精确的ID匹配，确保只执行点击的那个单元格对应的操作
        const exactColumnKey = repairId
          ? `${repairCode}-${repairId}_personnel`
          : `${repairCode}_personnel`;

        // 检查当前单元格是否与点击的单元格匹配
        if (record[exactColumnKey] !== undefined) {
          addPersonToRepair(
            draggingMember.value,
            repairCode,
            taskName,
            repairId
          );
        }
      }
    },

    // 拖拽处理
    onDragover: (event: DragEvent) => {
      event.preventDefault();
    },

    onDrop: (event: DragEvent) => {
      event.preventDefault();

      // 获取目标单元格
      const target = event.target as HTMLElement;
      const cell = target.closest("td");
      if (!cell) return;

      // 从复杂表格数据源中获取任务名称
      const taskName = record.taskName;

      // 获取单元格的列索引
      const cellIndex = Array.from(cell.parentNode?.children || []).indexOf(
        cell
      );

      // 从列索引判断对应的大修编号和列类型
      const { repairCode, repairId, columnType } =
        getColumnInfoFromIndex(cellIndex);

      // 只处理人员列
      if (columnType === "personnel" && repairCode) {
        // 使用精确的ID匹配，确保只执行点击的那个单元格对应的操作
        const exactColumnKey = repairId
          ? `${repairCode}-${repairId}_personnel`
          : `${repairCode}_personnel`;

        // 检查当前单元格是否与拖放的单元格匹配
        if (record[exactColumnKey] !== undefined) {
          if (event.dataTransfer) {
            const memberInfoJson = event.dataTransfer.getData("text/plain");

            try {
              const memberInfo = JSON.parse(memberInfoJson);

              if (memberInfo && memberInfo.staffName) {
                addPersonToRepair(memberInfo, repairCode, taskName, repairId);
              }
            } catch (e) {
              console.log("解析拖拽数据失败:", e);
            }
          }
        }
      }
    },
  };
};

// 根据列索引获取大修编号和列类型
const getColumnInfoFromIndex = (cellIndex: number) => {
  // 跳过基础列（序号、任务名称、任务属性、标准人数）
  const baseColumnCount = baseColumns.length;

  // 如果是基础列，返回空信息
  if (cellIndex < baseColumnCount) {
    return { repairCode: "", columnType: "" };
  }

  // 调整索引，排除基础列
  const adjustedIndex = cellIndex - baseColumnCount;

  // 每个大修编号有3列（到岗时间、离岗时间、人员）
  const repairGroupIndex = Math.floor(adjustedIndex / 3);
  const columnSubIndex = adjustedIndex % 3;

  // 列类型: 0=到岗时间, 1=离岗时间, 2=人员
  let columnType = "";
  switch (columnSubIndex) {
    case 0:
      columnType = "startTime";
      break;
    case 1:
      columnType = "endTime";
      break;
    case 2:
      columnType = "personnel";
      break;
  }

  // 获取对应的大修编号
  const uniqueCodeItems = getUniqueRepairCodesWithId();
  let repairCode = "";
  let repairId;

  if (repairGroupIndex < uniqueCodeItems.length) {
    const item = uniqueCodeItems[repairGroupIndex];
    repairCode = item.code;
    // 不再使用ID
    repairId = undefined;
  }

  return { repairCode, repairId, columnType };
};

// 添加日期选择器变更处理函数
const handleDateChange = (
  date: any,
  dateString: string,
  record: any,
  dataIndex: string
) => {
  console.log("日期变更:", dateString, "数据索引:", dataIndex, "记录:", record);

  // 如果日期为空，则不进行更新
  if (!dateString) {
    console.log("日期为空，不进行更新");
    return;
  }

  // 获取大修编号
  const parts = dataIndex.split("_");
  if (parts.length === 2) {
    const repairCode = parts[0]; // 现在只包含大修编号
    const fieldType = parts[1]; // startTime 或 endTime

    // 找到对应的任务和大修项目
    const taskIndex = res.value.findIndex(
      (task) => task.taskName === record.taskName
    );

    if (taskIndex >= 0) {
      const task = res.value[taskIndex];
      // 找到所有匹配大修编号的项目
      const items = task.annualConfigTaskOverhaulCodeVOS?.filter(
        (item) => item.overhaulCode === repairCode
      );

      if (items && items.length > 0) {
        // 更新所有匹配项的时间
        items.forEach((item) => {
          // 更新对应的时间值
          if (fieldType === "startTime") {
            item.realArriveTime = dateString;
          } else if (fieldType === "endTime") {
            item.realLeaveTime = dateString;
          }
        });

        // 打印更新后的详细信息
        items.forEach((item) => {
          const logData = {
            overhaulCode: item.overhaulCode,
            id: item.id || "无ID",
            realArriveTime: item.realArriveTime,
            realLeaveTime: item.realLeaveTime,
            taskName: record.taskName,
            staff: Array.isArray(item.staff)
              ? item.staff.map((s: any) => ({
                  staffAccount: s.staffAccount,
                  staffName: s.staffName,
                }))
              : [],
          };
          console.log("日期变更后详细信息:", logData);
        });
        console.log("----------------------------");
      } else {
        console.log(`未找到大修编号为 ${repairCode} 的大修项目`);
      }
    } else {
      console.log(`未找到任务名称为 ${record.taskName} 的任务`);
    }
  }
};

// 生成唯一ID
const generateUniqueId = () => {
  return "id-" + Math.random().toString(36).substr(2, 9);
};

// 添加人员到大修项目的函数
const addPersonToRepair = (
  member: any,
  repairCode: string,
  taskName: string,
  repairId?: any
) => {
  // 更新数据
  const taskIndex = res.value.findIndex((task) => task.taskName === taskName);

  if (taskIndex >= 0) {
    const task = res.value[taskIndex];
    // 查找所有匹配大修编号的项目
    const items = task.annualConfigTaskOverhaulCodeVOS?.filter(
      (item) => item.overhaulCode === repairCode
    );

    if (items && items.length > 0) {
      // 默认添加到第一个匹配的项目中
      const item = items[0];

      // 如果已有人员列表，检查名称是否已存在
      if (!Array.isArray(item.staff)) {
        item.staff = [];
      }

      // 检查这个人员是否已经存在于列表中
      const existingIndex = item.staff.findIndex((p: any) => {
        if (typeof p === "string") {
          return p === member.staffName;
        }
        return p.staffAccount === member.staffAccount;
      });

      if (existingIndex < 0) {
        // 将完整的人员对象添加到列表中
        item.staff.push({
          staffAccount: member.staffAccount,
          staffName: member.staffName,
        });

        // 将所有信息整合到一个对象中
        const logData = {
          overhaulCode: item.overhaulCode,
          id: item.id || "无ID",
          realArriveTime: item.realArriveTime,
          realLeaveTime: item.realLeaveTime,
          taskName: taskName,
          newStaff: {
            staffAccount: member.staffAccount,
            staffName: member.staffName,
            departmentName: member.departmentName || "未知部门",
          },
          staff: item.staff.map((s: any) => ({
            staffAccount: s.staffAccount,
            staffName: s.staffName,
          })),
        };

        console.log("成功添加人员，详细信息如下:", logData);
        console.log("----------------------------");
      } else {
        // 将所有信息整合到一个对象中
        const logData = {
          overhaulCode: item.overhaulCode,
          id: item.id || "无ID",
          taskName: taskName,
          staff: {
            staffAccount: member.staffAccount,
            staffName: member.staffName,
          },
        };

        console.log("人员已存在于列表中，详细信息如下:", logData);
        console.log("----------------------------");
      }
    } else {
      // 未找到大修项目，只打印信息
      const logData = {
        taskName: taskName,
        overhaulCode: repairCode,
        draggedStaff: {
          staffAccount: member.staffAccount,
          staffName: member.staffName,
          departmentName: member.departmentName || "未知部门",
        },
        message: "未找到匹配的大修项目，未添加人员",
      };

      console.log("未找到大修项目:", logData);
      console.log("----------------------------");
    }
  } else {
    console.log(`未找到任务名称为 ${taskName} 的任务`);
  }

  // 重置拖拽状态
  draggingName.value = "";
  draggingMember.value = null;
};

// 用于重置拖拽状态的处理函数
const resetDragging = () => {
  draggingName.value = "";
  draggingMember.value = null;
};

// 为表头设置点击事件
onMounted(() => {
  setTimeout(() => {
    setupHeaderEvents();
  }, 500); // 给表格渲染一些时间
});

// 为已渲染的表头设置事件
const setupHeaderEvents = () => {
  const headers = document.querySelectorAll(".ant-table-thead th");
  headers.forEach((header) => {
    const headerText = header.textContent || "";

    // 匹配大修编号 - 支持更复杂的编号格式，包括Q2-OT412这种格式
    const headerMatch = headerText.match(/^([A-Za-z0-9]+-[A-Za-z0-9]+)/);
    if (headerMatch) {
      const repairCode = headerMatch[1];

      // 清除原始内容，重新渲染
      header.innerHTML = "";

      // 创建大修编号元素
      const codeElement = document.createElement("div");
      codeElement.textContent = repairCode;
      codeElement.style.fontWeight = "bold";
      header.appendChild(codeElement);

      // 查找所有匹配此大修编号的项目
      const matchingItems: OverhaulCodeItem[] = [];
      res.value.forEach((task) => {
        if (Array.isArray(task.annualConfigTaskOverhaulCodeVOS)) {
          const items = task.annualConfigTaskOverhaulCodeVOS.filter(
            (item) => item.overhaulCode === repairCode
          );
          matchingItems.push(...items);
        }
      });

      // 如果有匹配项，显示第一个匹配项的时间范围
      if (
        matchingItems.length > 0 &&
        matchingItems[0].startTime &&
        matchingItems[0].endTime
      ) {
        const timeRangeElement = document.createElement("div");
        timeRangeElement.textContent = `(${matchingItems[0].startTime} ~ ${matchingItems[0].endTime})`;
        timeRangeElement.style.fontSize = "12px";
        timeRangeElement.style.color = "#666";
        header.appendChild(timeRangeElement);
      }

      // 创建一个新的闭包，确保每个处理程序有自己的状态
      (function (code) {
        let lastClickTime = 0;
        const DOUBLE_CLICK_THRESHOLD = 300; // 双击阈值，单位毫秒

        // 使用单一事件处理程序来处理单击和双击
        const handleClick = function (e) {
          e.stopPropagation();

          const now = new Date().getTime();
          const timeSinceLastClick = now - lastClickTime;

          if (timeSinceLastClick < DOUBLE_CLICK_THRESHOLD) {
            // 双击行为
            console.log("双击表头大修编号:", code);
            // 打开任务管理模态框 - 不再传递ID信息
            if (
              taskModalRef.value &&
              typeof taskModalRef.value.open === "function"
            ) {
              taskModalRef.value.open(code);
            } else {
              console.log("任务管理模态框未准备好或open方法不可用", {
                code,
                taskModalRef: taskModalRef.value,
              });
            }
            lastClickTime = 0; // 重置，避免连续多次点击触发多次双击
          } else {
            // 设置单击延迟，给双击检测留出时间
            setTimeout(() => {
              // 检查从这次点击到现在是否有新的点击发生
              if (now === lastClickTime) {
                console.log("单击表头大修编号:", code);
              }
            }, DOUBLE_CLICK_THRESHOLD);

            lastClickTime = now;
          }
        };

        // 只添加一个事件监听器，简化逻辑
        header.addEventListener("click", handleClick);

        // 记录事件处理函数，以便清理
        header.setAttribute("data-click-handler", "true");
      })(repairCode);

      // 添加样式指示可点击
      (header as HTMLElement).style.cursor = "pointer";
    }
  });
};

// 组件卸载时清理事件监听器
onUnmounted(() => {
  const headers = document.querySelectorAll(
    '.ant-table-thead th[data-click-handler="true"]'
  );
  headers.forEach((header) => {
    // 移除click事件 - 使用新元素替换是最彻底的方式
    const clone = header.cloneNode(true);
    if (header.parentNode) {
      header.parentNode.replaceChild(clone, header);
    }
  });
});

// 修改点击处理函数
const handleManageTask = () => {
  console.log("点击了大修任务管理");
  // 打开任务管理模态框
  taskModalRef.value.open();
};

const handleManageAlgorithm = () => {
  console.log("点击了参考时间算法管理");
  // 打开算法管理模态框
  algorithmModalRef.value.open();
};

// 处室下拉框变更处理函数
const handleDepartmentChange = (value: string) => {
  console.log("选择的处室值:", value);

  // 打印选中的部门信息
  const logData = {
    selectedDepartment: value,
    departmentInfo: departmentOptions.value.find(
      (item) => item.value === value
    ),
  };

  console.log("处室变更，详细信息:", logData);

  // 重置科室和班组值
  officeValue.value = "";
  teamValue.value = "";

  // 这里可以添加根据选中的处室更新科室选项的逻辑
  // 例如调用API获取对应处室下的科室列表
  // 或者根据本地数据过滤
};

// 科室下拉框变更处理函数
const handleOfficeChange = (value: string) => {
  console.log("选择的科室值:", value);

  // 打印选中的科室信息
  const logData = {
    selectedOffice: value,
    officeInfo: officeOptions.value.find((item) => item.value === value),
    currentDepartment: departmentValue.value,
  };

  console.log("科室变更，详细信息:", logData);

  // 重置班组值
  teamValue.value = "";

  // 这里可以添加根据选中的科室更新班组选项的逻辑
  // 例如调用API获取对应科室下的班组列表
  // 或者根据本地数据过滤

  // 示例：根据选择的科室值更新班组选项
  if (value === "office1") {
    // 这里可以替换为实际逻辑
    console.log("更新班组选项为生产科下属班组");
  } else if (value === "office2") {
    console.log("更新班组选项为管理科下属班组");
  } else {
    console.log("重置班组选项为默认值");
  }
};

// 班组下拉框变更处理函数
const handleTeamChange = (value: string) => {
  console.log("选择的班组值:", value);

  // 打印选中的班组信息以及当前的处室和科室信息
  const logData = {
    selectedTeam: value,
    teamInfo: teamOptions.value.find((item) => item.value === value),
    currentDepartment: departmentValue.value,
    currentOffice: officeValue.value,
  };

  console.log("班组变更，详细信息:", logData);

  // 可以在这里添加其他逻辑，例如根据选中的班组筛选人员等
};

// 添加人员来源查询处理函数
const handlePersonnelSearch = () => {
  // 打印所有选中的筛选条件
  const filterData = {
    department: departmentValue.value,
    departmentLabel: departmentOptions.value.find(
      (item) => item.value === departmentValue.value
    )?.label,
    office: officeValue.value,
    officeLabel: officeOptions.value.find(
      (item) => item.value === officeValue.value
    )?.label,
    team: teamValue.value,
    teamLabel: teamOptions.value.find((item) => item.value === teamValue.value)
      ?.label,
  };

  console.log("人员来源查询，筛选条件:", filterData);

  // 如果没有选择筛选条件，可以重置为完整列表或保持当前列表不变
  if (!departmentValue.value && !officeValue.value && !teamValue.value) {
    console.log("未选择任何筛选条件，返回所有人员");
    return;
  }

  // 实际项目中这里可能需要调用API获取过滤后的数据
  // nameList.value = filteredData;
};

// 添加表格查询处理函数
const handleTableSearch = () => {
  if (selectedRepairCodes.value.length > 0) {
    console.log("已选择的大修编号:", selectedRepairCodes.value);
  } else {
    console.log("未选择大修编号");
  }
};

// 将人员字符串转换为数组
const getPersonnelArray = (text: string) => {
  if (!text) return [];
  return text.split(", ").filter((name) => name.trim() !== "");
};

// 处理删除人员
const handleDeletePersonnel = (record: any, name: string, index: number) => {
  // 从表格数据中获取任务名称和列信息
  const taskName = record.taskName;

  // 找出这是哪个大修编号的人员列
  const columnKey = Object.keys(record).find(
    (key) => key.endsWith("_personnel") && record[key]?.includes(name)
  );

  if (!columnKey) {
    console.log("未找到对应的人员列");
    return;
  }

  // 解析大修编号
  const repairCode = columnKey.split("_")[0];

  // 找到对应的任务
  const taskIndex = res.value.findIndex((task) => task.taskName === taskName);

  if (taskIndex >= 0) {
    const task = res.value[taskIndex];
    // 找到所有匹配大修编号的项目
    const items = task.annualConfigTaskOverhaulCodeVOS?.filter(
      (item) => item.overhaulCode === repairCode
    );

    if (items && items.length > 0) {
      let personDeleted = false;
      let deletedFromItem: any = null;

      // 遍历所有匹配的项目，查找并删除人员
      for (const item of items) {
        if (Array.isArray(item.staff)) {
          const staffIndex = item.staff.findIndex(
            (s: any) => s.staffName === name || s === name
          );

          if (staffIndex >= 0) {
            // 保存删除前的信息
            const deletedStaff = item.staff[staffIndex];

            // 删除人员
            item.staff.splice(staffIndex, 1);
            personDeleted = true;
            deletedFromItem = item;

            // 打印删除信息
            const logData = {
              overhaulCode: item.overhaulCode,
              id: item.id || "无ID",
              realArriveTime: item.realArriveTime,
              realLeaveTime: item.realLeaveTime,
              taskName: taskName,
              deletedStaff: {
                staffAccount:
                  typeof deletedStaff === "string"
                    ? "未知"
                    : deletedStaff.staffAccount,
                staffName:
                  typeof deletedStaff === "string"
                    ? deletedStaff
                    : deletedStaff.staffName,
              },
              currentStaff: item.staff.map((s: any) => ({
                staffAccount: typeof s === "string" ? "未知" : s.staffAccount,
                staffName: typeof s === "string" ? s : s.staffName,
              })),
            };

            console.log("已删除人员，详细信息:", logData);
            break; // 找到并删除一次后退出循环
          }
        }
      }

      if (personDeleted) {
        // 更新表格显示数据 - 合并所有项目的人员
        const allStaff = items.reduce((acc: any[], item) => {
          if (Array.isArray(item.staff)) {
            // 使用Map去重
            const staffMap = new Map();
            item.staff.forEach((s: any) => {
              const key = s.staffAccount || s.staffName;
              if (!staffMap.has(key)) {
                staffMap.set(key, s);
              }
            });

            acc.push(...Array.from(staffMap.values()));
          }
          return acc;
        }, []);

        // 更新表格显示 - 修改这里，当人员为空时显示空字符串
        record[columnKey] =
          allStaff.length > 0
            ? allStaff.map((s: any) => s.staffName).join(", ")
            : "";

        console.log("----------------------------");
      } else {
        console.log(`未找到名为 ${name} 的人员`);
      }
    }
  }
};
</script>

<style scoped>
.container {
  padding: 20px;
  background-color: #f5f7fa;
  height: 800px;
  box-sizing: border-box;
  overflow: auto;
}

.header {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 24px;
  padding-left: 8px;
}

.main-title {
  font-size: 20px;
  font-weight: 600;
  color: #262626;
  position: relative;
  padding-left: 12px;
  border-left: 4px solid #1890ff;
  line-height: 28px;
}

.content {
  background-color: #fff;
  padding: 24px;
  border-radius: 4px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.section {
  margin-bottom: 10px;
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  color: #262626;
  margin-bottom: 5px;
  position: relative;
  padding-left: 10px;
  border-left: 3px solid #52c41a;
  line-height: 22px;
}

.filter-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.filter-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.filter-label {
  margin-right: 8px;
  color: #606266;
  font-size: 14px;
  white-space: nowrap;
}

.names-container {
}

.names-box {
  border: 1px solid #e0e6ed;
  border-radius: 4px;
  background-color: #fff;
  padding: 12px;
  height: 300px; /* 固定高度 */
  overflow-y: auto; /* 启用垂直滚动 */
}

.names-list {
  display: flex;
  flex-direction: column;
}

.department-row {
  display: flex;
  align-items: flex-start; /* 改为顶部对齐，便于换行时布局 */
  padding: 8px;
  border-radius: 4px;
  background-color: #fafafa;
  margin-bottom: 8px; /* 添加间距 */
}

.department-title {
  font-size: 14px;
  font-weight: bold;
  color: #333;
  margin-right: 12px;
  min-width: 80px;
  white-space: nowrap;
}

.department-members {
  display: flex;
  flex-wrap: wrap;
  flex: 1;
  gap: 8px;
}

.member-wrapper {
  display: inline-block;
}

.name-tag {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 32px;
  padding: 0 10px;
  font-size: 14px;
  background-color: #f0f7ff;
  color: #1890ff;
  border: 1px solid #d6e9ff;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
  user-select: none; /* 防止文本被选中 */
  max-width: 120px; /* 限制最大宽度 */
  overflow: hidden;
  box-sizing: border-box;
  width: 120px;
}

.ellipsis-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
  text-align: center;
}

.name-tag:hover {
  background-color: #e6f4ff;
  border-color: #91caff;
  transform: scale(1.05);
}

.name-tag:active {
  transform: scale(0.95);
}

.drop-result {
  margin-top: 16px;
  margin-bottom: 16px;
  padding: 10px 15px;
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 4px;
  color: #52c41a;
  font-size: 14px;
  animation: fadeIn 0.3s;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 自定义滚动条样式 */
.names-box::-webkit-scrollbar {
  width: 6px;
}

.names-box::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.names-box::-webkit-scrollbar-thumb {
  background: #ccc;
  border-radius: 3px;
}

.names-box::-webkit-scrollbar-thumb:hover {
  background: #aaa;
}

/* 人员单元格样式 */
.personnel-cell {
  width: 100%;
  cursor: pointer;
  overflow: hidden;
  padding: 0;
  margin: 0;
}

.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100%;
  display: block;
  line-height: 1.5;
  cursor: pointer;
  font-size: 14px;
}

/* 弹出层内的人员列表样式 */
:deep(.personnel-popover) {
  min-width: 200px;
  max-width: 400px !important;
}

:deep(.ant-popover-title) {
  font-weight: bold;
  text-align: center;
  padding: 8px 16px;
}

:deep(.ant-popover-inner-content) {
  padding: 8px 12px;
}

:deep(.personnel-list) {
  max-height: 300px;
  overflow-y: auto;
  padding: 4px;
  line-height: 1.5;
  word-break: break-all;
  white-space: normal;
}

:deep(.personnel-list div) {
  margin-bottom: 4px;
  padding: 2px 0;
  font-size: 14px;
}

/* 添加人员列表项样式 */
:deep(.personnel-item) {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 8px;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.personnel-item:last-child) {
  border-bottom: none;
}

:deep(.personnel-name) {
  flex: 1;
  margin-right: 8px;
}

:deep(.delete-btn) {
  padding: 0 4px;
  font-size: 12px;
  height: 22px;
}

/* 参考时间部分的布局样式 */
.time-section-container {
  display: flex;
  width: 100%;
  margin-top: 10px;
}

.time-table-container {
  width: 80%;
  box-sizing: border-box;
  padding-right: 16px;
}

.time-action-container {
  width: 20%;
  display: flex;
  flex-direction: column;
  padding: 0;
}

.action-block {
  height: 30px;
  background-color: #f0f7ff;
  border: 1px solid #d6e9ff;
  border-radius: 4px;
  padding: 0 16px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s;
  margin-bottom: 10px;
}

.action-block:hover {
  background-color: #e6f4ff;
  border-color: #91caff;
  transform: translateX(5px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.action-title {
  font-size: 14px;
  font-weight: bold;
  color: #1890ff;
  margin-bottom: 0;
}

.action-icon {
  font-size: 16px;
  color: #1890ff;
}

.action-arrow {
  display: inline-block;
  font-style: normal;
  transition: transform 0.3s;
}

.action-block:hover .action-arrow {
  transform: translateX(5px);
}

/* 添加日期选择器样式 */
:deep(.ant-picker) {
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 4px 8px;
  transition: all 0.3s;
}

:deep(.ant-picker:hover) {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

:deep(.ant-picker-input) {
  font-size: 14px;
}

:deep(.ant-picker-input > input) {
  font-size: 14px;
  font-weight: normal;
}

:deep(.ant-table-cell .ant-picker) {
  width: 100%;
  min-width: 140px;
}

/* 搜索按钮样式 */
.filter-item a-button {
  margin-left: 8px;
}

/* 查询按钮底部对齐 */
.filter-item:last-child {
  display: flex;
  align-items: flex-end;
  margin-left: 16px;
}
</style>
