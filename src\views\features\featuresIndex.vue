<template>
  <div class="outer-container">
    <div class="inner-container">
      <!-- 顶部标题和操作区 -->
      <div class="header-section">
        <div class="left-title">
          <h2>基地大修规划</h2>
        </div>
        <div class="right-actions">
          <span class="version-info">当前版本：241018-10cb53c9-01</span>
          <span class="status">状态：完布</span>
          <div class="divider-vertical"></div>
          <a-button>
            <template #icon><sync-outlined /></template>
            查看历史记录
          </a-button>
          <a-button>
            <template #icon><download-outlined /></template>
            数据导入
          </a-button>
          <a-button>
            <template #icon><upload-outlined /></template>
            导出EXCEL
          </a-button>
        </div>
      </div>

      <!-- 查询条件区域 -->
      <div class="search-header">
        <div class="year-select">
          <span>开始年份：</span>
          <a-date-picker
            v-model:value="formState.startTime"
            picker="year"
            format="YYYY年"
            placeholder="开始年份"
            @change="onTimeChange"
          />
          <span class="ml-4">结束年份：</span>
          <a-date-picker
            v-model:value="formState.endTime"
            picker="year"
            format="YYYY年"
            placeholder="结束年份"
            @change="onTimeChange"
          />
          <a-button type="primary" @click="handleSearch">查询</a-button>
          <a-button class="ml-2" @click="handleReset">全部</a-button>
          <div class="divider-vertical"></div>
          <a-checkbox v-model:checked="isSpringFestivalHighlight">
            春节大修
          </a-checkbox>
          <a-checkbox v-model:checked="isOverlappingHighlight" class="ml-2">
            重叠大修
          </a-checkbox>
          <a-checkbox class="ml-4">原因变更</a-checkbox>
          <a-checkbox class="ml-4">临界检查</a-checkbox>
          <a-checkbox class="ml-4">实时数据</a-checkbox>
        </div>
      </div>

      <!-- 统计信息区域 -->
      <div class="statistics-section">
        <div class="info-arrows">›››››</div>
        <div class="info-content">
          2024年-2028年进行的大修次数：
          <span class="highlight">31</span>次 | A类：<span class="highlight"
            >6</span
          >次 | B类：<span class="highlight">7</span>次 | C类：<span
            class="highlight"
            >18</span
          >次
        </div>
        <div class="info-arrows">‹‹‹‹‹</div>
      </div>

      <!-- 表格区域 -->
      <div class="table-section">
        <a-table
          :columns="columns"
          :dataSource="tableData"
          :scroll="{ x: 1500, y: 400 }"
          bordered
        >
          <template #bodyCell="{ column, record, index }">
            <template v-if="column.dataIndex === 'unit'">
              机组{{ record[column.dataIndex] }}
            </template>
            <template v-else>
              <div class="cell-container">
                <div class="gantt-section">
                  <div
                    class="timeline"
                    :style="{
                      backgroundColor: rowColors[index % rowColors.length],
                    }"
                  ></div>
                  <template v-if="record[column.dataIndex]">
                    <!-- 提示框 -->
                    <a-popover placement="top">
                      <template #content>
                        <div>
                          时间：{{ record[column.dataIndex].startTime }} -
                          {{ record[column.dataIndex].endTime }}
                        </div>
                        <div>说明：{{ record[column.dataIndex].text }}</div>
                      </template>
                      <div
                        class="gantt-block"
                        :style="
                          getGanttStyle(
                            record[column.dataIndex],
                            column.year,
                            index % rowColors.length
                          )
                        "
                      ></div>
                    </a-popover>
                  </template>
                </div>
                <!-- 下面文字内容 -->
                <div class="text-section">
                  <template v-if="record[column.dataIndex]">
                    <div class="time-text">
                      {{ record[column.dataIndex].startTime }}
                      {{ record[column.dataIndex].endTime }}
                    </div>
                    <div class="description-text">
                      {{ record[column.dataIndex].text }}
                    </div>
                  </template>
                </div>
                <!-- 春节标记线 -->
                <div
                  v-if="
                    isSpringFestivalHighlight &&
                    column.year &&
                    getSpringFestivalMarker(column.year)
                  "
                  class="spring-festival-marker"
                  :style="getSpringFestivalMarker(column.year)"
                ></div>
              </div>
            </template>
          </template>
        </a-table>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, h } from "vue";
import type { Dayjs } from "dayjs";
import lunar from "chinese-lunar";
import {
  SyncOutlined,
  DownloadOutlined,
  UploadOutlined,
} from "@ant-design/icons-vue";

interface FormState {
  startTime: Dayjs | null;
  endTime: Dayjs | null;
  checkTypes: string[];
}

interface CellData {
  startTime: string;
  endTime: string;
  text: string;
}

// 定义每行的颜色
const rowColors = [
  "#1890ff", // 蓝色
  "#52c41a", // 绿色
  "#faad14", // 黄色
  "#722ed1", // 紫色
  "#eb2f96", // 粉色
];

// 春节高亮状态
const isSpringFestivalHighlight = ref(false);
// 重叠检测状态
const isOverlappingHighlight = ref(false);

// 获取春节日期字符串并转换为日期对象
const parseSpringFestivalDate = (year: number) => {
  try {
    const springDate = new Date(year, 0, 1);
    let lunarInfo = lunar.solarToLunar(springDate);
    while (!(lunarInfo.month === 1 && lunarInfo.day === 1)) {
      springDate.setDate(springDate.getDate() + 1);
      lunarInfo = lunar.solarToLunar(springDate);
    }
    return springDate;
  } catch (error) {
    console.error(
      `Error getting Spring Festival date for year ${year}:`,
      error
    );
    return null;
  }
};

// 用于存储重叠的计划
const overlappingPlans = [];

// 判断是否经过春节
const isPassingSpringFestival = (
  startTime: string,
  endTime: string,
  year: number
) => {
  const start = new Date(startTime);
  const end = new Date(endTime);

  // 获取维修期间涉及的年份
  const startYear = start.getFullYear();
  const endYear = end.getFullYear();

  // 如果当前年份不在维修时间范围内，直接返回 false
  if (year < startYear || year > endYear) {
    return false;
  }

  // 转换日期为本地字符串格式，用于比较
  const startDateStr = start.toLocaleDateString();
  const endDateStr = end.toLocaleDateString();

  // 同一年的情况
  if (startYear === endYear) {
    // 获取开始年份的春节
    const springFestival = parseSpringFestivalDate(startYear);
    if (!springFestival) return false;

    const springFestivalStr = springFestival.toLocaleDateString();
    // 只需判断是否经过开始年份的春节
    return startDateStr <= springFestivalStr && endDateStr >= springFestivalStr;
  }
  // 跨年的情况
  else {
    // 条件1：判断开始日期是否经过开始年份的春节
    const startYearSpringFestival = parseSpringFestivalDate(startYear);
    if (startYearSpringFestival) {
      const startYearSpringFestivalStr =
        startYearSpringFestival.toLocaleDateString();
      if (
        startDateStr <= startYearSpringFestivalStr &&
        new Date(startYear, 11, 31).toLocaleDateString() >=
          startYearSpringFestivalStr
      ) {
        return true;
      }
    }

    // 条件2：判断结束日期是否经过当前年份的春节
    const currentYearSpringFestival = parseSpringFestivalDate(year);
    if (currentYearSpringFestival) {
      const currentYearSpringFestivalStr =
        currentYearSpringFestival.toLocaleDateString();
      if (
        new Date(year, 0, 1).toLocaleDateString() <=
          currentYearSpringFestivalStr &&
        endDateStr >= currentYearSpringFestivalStr
      ) {
        return true;
      }
    }
  }

  return false;
};

// 检查是否有重叠的维修计划
const hasOverlappingMaintenance = (newPlan: any) => {
  const newStartTime = new Date(newPlan.startTime);
  const newEndTime = new Date(newPlan.endTime);
  // 用于存储重叠的计划
  const overlappingPlans = [];
  // 遍历所有维修计划
  tableData.value.forEach((plan) => {
    // 遍历每个计划的所有年份
    Object.keys(plan).forEach((key) => {
      // 只检查包含年份的属性（year开头的属性）
      if (key.startsWith("year")) {
        const yearPlan = plan[key];
        const planStartTime = new Date(yearPlan.startTime);
        const planEndTime = new Date(yearPlan.endTime);

        // 检查时间是否重叠
        if (newStartTime <= planEndTime && newEndTime >= planStartTime) {
          overlappingPlans.push({
            unit: plan.unit,
            year: key,
            text: yearPlan.text,
            startTime: yearPlan.startTime,
            endTime: yearPlan.endTime,
          });
        }
      }
    });
  });

  return overlappingPlans.length >= 2;
};

// 修改甘特图样式计算
const getGanttStyle = (data: any, year: number, rowIndex: number) => {
  const baseStyle = calculateBaseGanttStyle(data, year, rowIndex);
  if (!baseStyle) return null;

  let backgroundColor = rowColors[rowIndex % rowColors.length];
  // 判断是否需要高亮显示
  const isPassingFestival =
    isSpringFestivalHighlight.value &&
    isPassingSpringFestival(data.startTime, data.endTime, year);
  const isOverlapping =
    isOverlappingHighlight.value && hasOverlappingMaintenance(data);
  // 只有当两个条件都满足时才标红
  if (isSpringFestivalHighlight.value && isOverlappingHighlight.value) {
    // 两个条件都必须为 true 才标红
    backgroundColor =
      isPassingFestival && isOverlapping ? backgroundColor : "#d9d9d9";
  } else if (isSpringFestivalHighlight.value) {
    // 只选中春节大修时
    backgroundColor = isPassingFestival ? backgroundColor : "#d9d9d9";
  } else if (isOverlappingHighlight.value) {
    // 只选中重叠大修时
    backgroundColor = isOverlapping ? backgroundColor : "#d9d9d9";
  }

  return {
    ...baseStyle,
    backgroundColor,
    transition: "background-color 0.3s",
  };
};

// 基础甘特图样式计算（保持原有逻辑）
const calculateBaseGanttStyle = (
  data: CellData,
  year: number,
  rowIndex: number
) => {
  // 解析日期字符串为日期对象
  const [startYear, startMonth, startDay] = data.startTime
    .split("-")
    .map(Number);
  const [endYear, endMonth, endDay] = data.endTime.split("-").map(Number);

  // 计算在当年的天数位置（1月1日为第1天）
  const getDayOfYear = (month: number, day: number) => {
    const daysInMonth = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];
    let dayCount = day;
    for (let i = 0; i < month - 1; i++) {
      dayCount += daysInMonth[i];
    }
    return dayCount;
  };
  // 计算开始日期在年内的天数
  const startDayOfYear = getDayOfYear(startMonth, startDay);
  // 计算结束日期在年内的天数
  let endDayOfYear = getDayOfYear(endMonth, endDay);
  // 计算位置和宽度的百分比

  //  判断是否为同一年
  const numDouble = endYear - startYear;
  const newEndYear = numDouble * 365;
  const left = ((startDayOfYear - 1) / 365) * 100;
  const width = ((endDayOfYear + newEndYear - startDayOfYear + 1) / 365) * 100;

  return {
    left: `${left}%`,
    width: `${width}%`,
    backgroundColor: rowColors[rowIndex % rowColors.length],
  };
};

// 辅助函数：计算一年中的第几天
const getDayOfYear = (month: number, day: number) => {
  const daysInMonth = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];
  let dayCount = day;
  for (let i = 0; i < month - 1; i++) {
    dayCount += daysInMonth[i];
  }
  return dayCount;
};

// 表单状态
const formState = ref<FormState>({
  startTime: null,
  endTime: null,
  checkTypes: [],
});

// 获取标题文本
const getTitleText = computed(() => {
  const start = formState.value.startTime?.year() || 2024;
  const end = formState.value.endTime?.year() || 2028;
  return `${start}-${end}年大修次数`;
});

// 生成表头
const columns = computed(() => {
  const start = formState.value.startTime?.year() || 2024;
  const end = formState.value.endTime?.year() || 2029;
  const years = Array.from(
    { length: end - start + 1 },
    (_, index) => start + index
  );

  return [
    {
      title: "机组",
      dataIndex: "unit",
      width: 100,
      fixed: "left",
    },
    ...years.map((year) => ({
      title: h("div", [
        h("div", `${year}年`),
        h(
          "div",
          {
            style: {
              fontSize: "12px",
              color: "#666",
              marginTop: "4px",
            },
          },
          `春节：${parseSpringFestivalDate(year)?.toLocaleDateString()}`
        ),
      ]),
      dataIndex: `year${year}`,
      width: 200,
      year: year,
    })),
  ];
});

// 生成表格数据
const tableData = ref([
  {
    key: 1,
    unit: 1,
    year2024: {
      startTime: "2024-07-29",
      endTime: "2025-09-14",
      text: "维修计划1-2024",
    },
    year2025: {
      startTime: "2025-12-09",
      endTime: "2025-12-18",
      text: "维修计划1-2025",
    },
    year2027: {
      startTime: "2027-01-16",
      endTime: "2027-08-12",
      text: "维修计划1-2027",
    },
    year2028: {
      startTime: "2028-10-30",
      endTime: "2028-11-30",
      text: "维修计划1-2028",
    },
  },
  {
    key: 2,
    unit: 2,
    year2024: {
      startTime: "2024-08-19",
      endTime: "2024-09-19",
      text: "维修计划2-2024",
      rowIndex: 1,
    },
    year2025: {
      startTime: "2025-12-03",
      endTime: "2025-12-26",
      text: "维修计划2-2025",
      rowIndex: 1,
    },
    year2027: {
      startTime: "2027-06-26",
      endTime: "2027-07-27",
      text: "维修计划2-2027",
      rowIndex: 1,
    },
    year2028: {
      startTime: "2028-05-20",
      endTime: "2028-10-30",
      text: "维修计划2-2028",
      rowIndex: 1,
    },
    year2029: {
      startTime: "2029-04-02",
      endTime: "2029-04-12",
      text: "维修计划2-2029",
      rowIndex: 1,
    },
  },
  {
    key: 3,
    unit: 3,
    year2024: {
      startTime: "2024-03-15",
      endTime: "2025-11-10",
      text: "维修计划3-2024",
      rowIndex: 2,
    },
    year2026: {
      startTime: "2026-10-10",
      endTime: "2026-11-05",
      text: "维修计划3-2026",
      rowIndex: 2,
    },
    year2028: {
      startTime: "2028-09-30",
      endTime: "2028-11-24",
      text: "维修计划3-2028",
      rowIndex: 2,
    },
    year2029: {
      startTime: "2029-07-11",
      endTime: "2029-11-15",
      text: "维修计划3-2029",
      rowIndex: 2,
    },
  },
  {
    key: 4,
    unit: 4,
    year2024: {
      startTime: "2024-12-13",
      endTime: "2024-12-15",
      text: "维修计划4-2024",
      rowIndex: 3,
    },
    year2025: {
      startTime: "2025-03-15",
      endTime: "2025-08-20",
      text: "维修计划4-2025",
      rowIndex: 3,
    },
    year2027: {
      startTime: "2027-11-26",
      endTime: "2027-11-28",
      text: "维修计划4-2027",
      rowIndex: 3,
    },
  },
  {
    key: 5,
    unit: 5,
    year2027: {
      startTime: "2027-08-29",
      endTime: "2028-02-07",
      text: "维修计划5-2027",
      rowIndex: 4,
    },
    year2028: {
      startTime: "2028-08-07",
      endTime: "2028-11-29",
      text: "维修计划5-2028",
      rowIndex: 4,
    },
    year2029: {
      startTime: "2029-07-29",
      endTime: "2029-10-07",
      text: "维修计划5-2029",
      rowIndex: 4,
    },
  },
  {
    key: 6,
    unit: 6,
    year2027: {
      startTime: "2027-08-29",
      endTime: "2028-02-07",
      text: "维修计划5-2027",
      rowIndex: 5,
    },
    year2028: {
      startTime: "2028-08-07",
      endTime: "2028-11-29",
      text: "维修计划5-2028",
      rowIndex: 5,
    },
    year2029: {
      startTime: "2029-07-29",
      endTime: "2029-10-07",
      text: "维修计划5-2029",
      rowIndex: 5,
    },
  },
]);

// 处理查询
const handleSearch = () => {
  console.log("Search with:", formState.value);
};

// 处理重置
const handleReset = () => {
  formState.value = {
    startTime: null,
    endTime: null,
    checkTypes: [],
  };
};

// 处理时间变化的函数
const onTimeChange = () => {
  // 在这里可以添加处理时间变化的逻辑
};

// 计算春节标记线的位置
const getSpringFestivalMarker = (year: number) => {
  if (!year) return null;

  const springDate = parseSpringFestivalDate(year);
  if (!springDate) return null;

  // 计算春节在这一年中的位置百分比
  const yearStart = new Date(year, 0, 1);
  const yearEnd = new Date(year + 1, 0, 1);
  const totalDays =
    (yearEnd.getTime() - yearStart.getTime()) / (1000 * 60 * 60 * 24);
  const springDays =
    (springDate.getTime() - yearStart.getTime()) / (1000 * 60 * 60 * 24);
  const position = (springDays / totalDays) * 100;

  return {
    position: "absolute",
    left: `${position}%`,
    top: "0",
    width: "2px",
    height: "100%",
    backgroundColor: "#ff4d4f",
    zIndex: 1,
  };
};
</script>

<style scoped>
.outer-container {
  padding: 16px;
  background: #f0f2f5;
}

.inner-container {
  background: #fff;
  border-radius: 4px;
}

/* 顶部标题区域样式 */
.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid #f0f0f0;
}

.left-title {
  display: flex;
  align-items: center;
}

.left-title h2 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #000000d9;
}

.right-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.version-info,
.status {
  color: #00000073;
  font-size: 14px;
}

.divider-vertical {
  height: 20px;
  width: 1px;
  background: #d9d9d9;
  margin: 0 12px;
}

/* 按钮样式 */
:deep(.ant-btn) {
  color: #1890ff;
  border-color: #1890ff;
}

:deep(.ant-btn .anticon) {
  color: #1890ff;
}

/* 查询区域样式优化 */
.search-header {
  padding: 16px 24px;
  border-bottom: 1px solid #f0f0f0;
}

.year-select {
  display: flex;
  align-items: center;
  gap: 12px;
}

.year-select span {
  color: #000000d9;
  font-size: 14px;
}

/* 查询按钮样式 */
:deep(.ant-btn-primary) {
  color: #fff;
  background-color: #1890ff;
  border-color: #1890ff;
}

:deep(.ant-btn-primary:hover) {
  color: #fff;
  background-color: #40a9ff;
  border-color: #40a9ff;
}

/* 统计信息区域样式 */
.statistics-section {
  margin: 16px 24px;
  padding: 12px;
  background-color: #f8fbff;
  border: 1px dashed #a8c8e8;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.info-arrows {
  color: #1890ff;
  font-size: 16px;
  letter-spacing: 2px;
  margin: 0 12px;
}

.info-arrows.reverse {
  transform: rotate(180deg);
}

.info-content {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #666;
  font-size: 14px;
}

.highlight {
  color: #1890ff;
  font-weight: bold;
  margin: 0 4px;
}

/* 间距工具类 */
.ml-2 {
  margin-left: 8px;
}

.ml-4 {
  margin-left: 16px;
}

/* 表格区域样式 */
.table-section {
  padding: 0 24px 24px;
}

.cell-container {
  padding: 0;
  position: relative;
  width: 100%;
  display: flex;
  flex-direction: column;
}

.gantt-section {
  height: 50px;
  position: relative;
  width: 100%;
  background-color: #f0f1f3;
}

.text-section {
  text-align: center;
  flex: 1;
  padding: 8px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.timeline {
  position: absolute;
  width: 100%;
  height: 2px;
  top: 25px;
  transform: none;
  z-index: 1;
}

.gantt-block {
  position: absolute;
  height: 30px;
  top: 10px;
  border-radius: 4px;
  min-width: 4px;
  z-index: 2;
  transition: background-color 0.3s;
}

.time-text {
  font-size: 14px;
  color: #333;
  margin-bottom: 4px;
}

.description-text {
  font-size: 12px;
  color: #666;
}

:deep(.ant-table-cell) {
  position: relative !important;
  padding: 0 !important;
  vertical-align: top !important;
}

.time-range {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.description {
  font-size: 12px;
  color: #999;
}

:deep(.ant-popover-inner-content) {
  padding: 8px 12px;
  font-size: 12px;
}

.title-section h2 {
  margin: 0;
  font-size: 18px;
  line-height: 50px;
}

:deep(.ant-table-cell) {
  min-width: 200px;
}

:deep(.ant-table-row) {
  height: 100px;
}

:deep(.ant-table-thead > tr > th) {
  padding: 8px 16px;
  text-align: center;
}

:deep(.ant-picker) {
  width: 120px;
}

:deep(.ant-btn) {
  height: 32px;
  padding: 0 16px;
}

/* 复选框组样式优化 */
:deep(.ant-checkbox-wrapper) {
  margin-right: 16px; /* 减小复选框之间的间距 */
}

:deep(.ant-checkbox-wrapper:last-child) {
  margin-right: 0;
}

.cell-content {
  position: relative;
  height: 100%;
  min-height: 54px;
}

.spring-festival-marker {
  position: absolute;
  pointer-events: none;
  animation: markerFadeIn 0.3s ease-in-out;
  z-index: 999; /* 确保在最上层 */
}

@keyframes markerFadeIn {
  from {
    opacity: 0;
    transform: scaleX(0);
  }
  to {
    opacity: 1;
    transform: scaleX(1);
  }
}

/* 调整表格单元格样式 */
:deep(.ant-table-cell) {
  position: relative;
  padding: 0 !important;
}

/* 确保表格体的单元格有最小高度 */
:deep(.ant-table-tbody > tr > td) {
  min-height: 54px;
  height: 54px;
}

/* 确保空单元格也能显示标记线 */
:deep(.ant-table-tbody > tr > td.ant-table-cell-with-append) {
  position: relative;
  height: 54px;
}

/* 确保标记线在表格内容之上 */
:deep(.ant-table) {
  position: relative;
}

:deep(.ant-table-content) {
  position: relative;
}

:deep(.ant-table-body) {
  position: relative;
}
</style>
