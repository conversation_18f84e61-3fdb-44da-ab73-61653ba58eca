<template>
  <a-modal
    v-model:open="visible"
    title="大修工期设置"
    @ok="handleOk"
    @cancel="handleCancel"
    width="500px"
    okText="确认"
    cancelText="取消"
  >
    <div class="power-name-wrapper">
      <div class="power-name">{{ powerName }}</div>
    </div>
    <a-form
      :model="formState"
      layout="horizontal"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 18 }"
      class="maintenance-form"
    >
      <!-- 大修工期 -->
      <a-form-item label="大修工期">
        <a-input v-model:value="formState.maintenancePeriod" />
      </a-form-item>

      <!-- 大修类型 -->
      <a-form-item label="大修类型">
        <a-input v-model:value="formState.maintenanceType" />
      </a-form-item>

      <!-- 目标工期 -->
      <a-form-item label="目标工期">
        <a-input v-model:value="formState.targetPeriod" />
      </a-form-item>

      <!-- 电网工期 -->
      <a-form-item label="电网工期">
        <a-input v-model:value="formState.gridPeriod" />
      </a-form-item>

      <!-- 非标项目 -->
      <a-form-item label="非标项目">
        <a-input v-model:value="formState.specialProject" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
import { ref } from "vue";

const visible = ref(false);
const powerName = ref("");
const formState = ref({
  maintenancePeriod: "",
  maintenanceType: "",
  targetPeriod: "",
  gridPeriod: "",
  specialProject: "",
});

// 打开模态框的方法
const openModal = (data: any) => {
  powerName.value = data.powerName;
  formState.value = {
    maintenancePeriod: data.maintenancePeriod || "",
    maintenanceType: data.type || "",
    targetPeriod: data.targetPeriod || "",
    gridPeriod: data.gridPeriod || "",
    specialProject: data.specialProject || "",
  };
  visible.value = true;
};

// 处理确认
const handleOk = () => {
  visible.value = false;
};

// 处理取消
const handleCancel = () => {
  visible.value = false;
};

// 暴露方法给父组件
defineExpose({
  openModal,
});
</script>

<style scoped>
.power-name-wrapper {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
}

.power-name {
  font-size: 16px;
  font-weight: 500;
  padding: 8px 20px;
  background-color: #f5f5f5;
  border-radius: 4px;
  display: inline-block;
  width: 100%;
  text-align: center;
}

.maintenance-form {
  padding: 0 4px;
}

:deep(.ant-modal-body) {
  padding: 24px 24px 0;
}

:deep(.ant-form-item) {
  margin-bottom: 24px;
}

:deep(.ant-form-item-label) {
  text-align: left;
}

:deep(.ant-modal-header) {
  border-bottom: none;
  padding-bottom: 0;
}

:deep(.ant-modal-title) {
  font-size: 18px;
  font-weight: 600;
}
</style>
